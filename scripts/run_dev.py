#!/usr/bin/env python

import signal
import subprocess
import sys
import threading
import time
from collections.abc import Iterator
from contextlib import contextmanager


def terminate_process(process: subprocess.Popen, attempt_sigint: bool = False) -> None:
    if attempt_sigint:
        if process.poll() is None:
            process.send_signal(signal.SIGINT)
        time.sleep(5)

    if process.poll() is None:
        process.terminate()
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()


def monitor_tunnel(tunnel_process: subprocess.Popen, dagster_process: subprocess.Popen):
    while True:
        if tunnel_process.poll() is not None:
            print("SSH tunnel has died. Terminating Dagster.")

            # Print stdout and stderr if available
            stdout, stderr = tunnel_process.communicate()
            if stdout:
                print("Tunnel process stdout:")
                print(stdout.decode())
            if stderr:
                print("Tunnel process stderr:")
                print(stderr.decode())

            terminate_process(dagster_process, True)
            sys.exit(1)
        time.sleep(1)


@contextmanager
def run_sql_tunnel() -> Iterator[subprocess.Popen]:
    print("Starting SSH tunnel to Cloud SQL...")
    tunnel_process = subprocess.Popen(
        [
            "gcloud",
            "compute",
            "ssh",
            "bastion-host",
            "--project",
            "nonprod-platform-f24dad",
            "--zone",
            "us-central1-a",
            "--ssh-flag=-NL 3306:127.0.0.1:3306 -o ExitOnForwardFailure=yes",
        ],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
    )

    try:
        yield tunnel_process
    finally:
        # Terminate the SSH tunnel process
        tunnel_process.terminate()
        tunnel_process.wait(timeout=5)  # Wait for up to 5 seconds for the process to terminate
        if tunnel_process.poll() is None:
            # If the process hasn't terminated, force kill it
            tunnel_process.kill()


def main() -> None:
    with run_sql_tunnel() as tunnel_process:
        try:
            print("Starting Dagster...")
            dagster_process = subprocess.Popen(["dagster", "dev"])

            # Start a monitoring thread
            monitor_thread = threading.Thread(
                target=monitor_tunnel,
                args=(tunnel_process, dagster_process),
            )
            monitor_thread.daemon = True
            monitor_thread.start()

            # Wait for Dagster to finish or for an interrupt
            while dagster_process.poll() is None:
                time.sleep(1)

            print("Dagster process has ended.")
        except KeyboardInterrupt:
            print("Received KeyboardInterrupt. Terminating processes...")
        finally:
            terminate_process(dagster_process)


if __name__ == "__main__":
    main()
