import signal
from contextlib import contextmanager
from datetime import datetime
from unittest.mock import Magic<PERSON>ock

import pandas as pd
import pytest
from dagster import build_init_resource_context, build_op_context

from warehouse.utils.sheets.sheet_config import SheetConfig
from warehouse.utils.sheets.sheets_factory import SheetsAssetFactory


@contextmanager
def dagster_test_context():
    """Context manager for Dagster test resources."""
    init_context = build_init_resource_context()
    try:
        yield init_context
    finally:
        # Reset signal handlers to default
        signal.signal(signal.SIGINT, signal.SIG_DFL)
        signal.signal(signal.SIGTERM, signal.SIG_DFL)
        if hasattr(init_context, "cleanup"):
            init_context.cleanup()


@pytest.fixture
def mock_sheets_resource():
    resource = MagicMock()
    return resource


@pytest.fixture
def mock_context(mock_sheets_resource):
    with dagster_test_context() as _:
        op_context = build_op_context(
            partition_key="2024-01-01 00:00:00", resources={"sheets_resource": mock_sheets_resource}
        )
        yield op_context


@pytest.fixture
def base_config():
    return SheetConfig(
        name="Test Sheet",
        spreadsheet_id={"dev": "id1", "prod": "id1"},
        tab_name="tab1",
        sheet_range="A1:Z100",
        destination_table_name="table1",
        schema={
            "str_col": "string",
            "int_col": "int64",
            "float_col": "float64",
            "bool_col": "bool",
            "date_col": "datetime64[ns]",
        },
    )


class TestBasicSheetOperations:
    """Tests for basic sheet reading and data conversion operations."""

    def test_row_padding_and_type_conversion(self, mock_context, mock_sheets_resource, base_config):
        """Test row padding and type conversion in one test."""
        mock_sheets_resource.get_sheet.return_value = {
            "values": [
                ["str_col", "int_col", "float_col", "bool_col", "date_col"],
                ["a", "1"],  # Short row
                ["b", "2", "3.5", "TRUE"],  # Medium row
                ["c", "3", "4.5", "FALSE", "2024-01-01"],  # Complete row
                ["", "", "", "", ""],  # Empty row
            ]
        }

        df = SheetsAssetFactory()._read_sheet(base_config, mock_context)

        # Create expected DataFrame with proper types
        expected_df = pd.DataFrame(
            {
                "str_col": pd.Series(["a", "b", "c", None], dtype="string"),
                "int_col": pd.Series([1, 2, 3, None], dtype="Int64"),
                "float_col": pd.Series([None, 3.5, 4.5, None], dtype="float64"),
                "bool_col": pd.Series([None, True, False, None], dtype="boolean"),
                "date_col": pd.Series(
                    [None, None, pd.Timestamp("2024-01-01"), None], dtype="datetime64[ns]"
                ),
                "source_google_sheet_id": pd.Series(["id1", "id1", "id1", "id1"], dtype="string"),
            }
        )

        # Compare DataFrames and show detailed diff on failure
        pd.testing.assert_frame_equal(df, expected_df, check_dtype=True, check_index_type=False)

    def test_column_mapping(self, mock_context, mock_sheets_resource, base_config):
        """Test column name mapping and filtering."""
        base_config.col_name_mapping = {
            "str_col": "String Column",
            "int_col": "Integer Column",
            "float_col": "Float Column",
            "bool_col": "Boolean Column",
            "date_col": "Date Column",
        }

        mock_sheets_resource.get_sheet.return_value = {
            "values": [
                [
                    "String Column",
                    "Integer Column",
                    "Float Column",
                    "Boolean Column",
                    "Date Column",
                ],
                ["a", "1", "1.5", "TRUE", "2024-01-01"],
            ]
        }

        df = SheetsAssetFactory()._read_sheet(base_config, mock_context)

        expected_df = pd.DataFrame(
            {
                "str_col": pd.Series(["a"], dtype="string"),
                "int_col": pd.Series([1], dtype="Int64"),
                "float_col": pd.Series([1.5], dtype="float64"),
                "bool_col": pd.Series([True], dtype="boolean"),
                "date_col": pd.Series([pd.Timestamp("2024-01-01")], dtype="datetime64[ns]"),
                "source_google_sheet_id": pd.Series(["id1"], dtype="string"),
            }
        )

        pd.testing.assert_frame_equal(df, expected_df, check_dtype=True, check_index_type=False)

    def test_incomplete_rows_and_empty_values(self, mock_context, mock_sheets_resource):
        """Test handling of incomplete rows and empty values."""
        factory = SheetsAssetFactory()

        # Create a config with mixed types
        config = SheetConfig(
            name="Incomplete Rows Test Sheet",
            spreadsheet_id={"dev": "id1", "prod": "id1"},
            tab_name="tab1",
            sheet_range="A1:D3",
            destination_table_name="table1",
            schema={
                "str_col": "string",
                "int_col": "int64",
                "float_col": "float64",
                "date_col": "datetime64[ns]",
            },
        )

        # Create a proper Dagster context with incomplete rows
        mock_sheets_resource.get_sheet.return_value = {
            "values": [
                ["str_col", "int_col", "float_col", "date_col"],  # Header row
                ["a", "1", "1.5", "2024-01-01"],  # Complete row
                ["b", "2"],  # Incomplete row
                ["", "", "", ""],  # Empty row
            ]
        }

        # Test sheet reading
        df = factory._read_sheet(config, mock_context)

        expected_df = pd.DataFrame(
            {
                "str_col": pd.Series(["a", "b", None], dtype="string"),
                "int_col": pd.Series([1, 2, None], dtype="Int64"),
                "float_col": pd.Series([1.5, None, None], dtype="float64"),
                "date_col": pd.Series(
                    [pd.Timestamp("2024-01-01"), None, None], dtype="datetime64[ns]"
                ),
                "source_google_sheet_id": pd.Series(["id1", "id1", "id1"], dtype="string"),
            }
        )

        pd.testing.assert_frame_equal(df, expected_df, check_dtype=True, check_index_type=False)


class TestSheetValidation:
    """Tests for sheet validation and error handling."""

    def test_empty_sheet_validation(self, mock_context, mock_sheets_resource, base_config):
        """Test that empty sheets or sheets with only headers raise an exception."""
        factory = SheetsAssetFactory()

        # Test with empty spreadsheet
        mock_sheets_resource.get_sheet.return_value = {"values": []}
        with pytest.raises(AssertionError) as exc_info:
            factory._read_sheet(base_config, mock_context)
        assert "No data found in sheet Test Sheet" in str(exc_info.value)

        # Test with header row but no data
        mock_sheets_resource.get_sheet.return_value = {
            "values": [["str_col", "int_col", "float_col", "bool_col", "date_col"]]
        }
        with pytest.raises(AssertionError) as exc_info:
            factory._read_sheet(base_config, mock_context)
        assert "No data found in sheet Test Sheet" in str(exc_info.value)

    def test_validate_schemas_match(self):
        """Test schema validation for multiple sheets."""
        factory = SheetsAssetFactory()
        configs = [
            SheetConfig(
                name="Sheet 1",
                spreadsheet_id={"dev": "id1", "prod": "id1"},
                tab_name="tab1",
                sheet_range="A1:B2",
                destination_table_name="table1",
                schema={"col1": "string", "col2": "int64"},
            ),
            SheetConfig(
                name="Sheet 2",
                spreadsheet_id={"dev": "id2", "prod": "id2"},
                tab_name="tab2",
                sheet_range="A1:B2",
                destination_table_name="table1",
                schema={"col1": "string", "col2": "int64"},
            ),
        ]

        factory._validate_schemas_match(configs)  # Should not raise

        configs[1].schema = {"col1": "string", "col2": "str"}
        with pytest.raises(AssertionError, match="Schema mismatch for table table1"):
            factory._validate_schemas_match(configs)


class TestAssetCreation:
    """Tests for asset creation and configuration grouping."""

    def test_source_google_sheet_id(self, mock_context, mock_sheets_resource):
        """Test that source_google_sheet_id is added correctly."""
        configs = [
            SheetConfig(
                name="Sheet 1",
                spreadsheet_id={"dev": "id1", "prod": "id1"},
                tab_name="tab1",
                sheet_range="A1:B2",
                destination_table_name="table1",
                schema={"col1": "string"},
            ),
            SheetConfig(
                name="Sheet 2",
                spreadsheet_id={"dev": "id2", "prod": "id2"},
                tab_name="tab2",
                sheet_range="A1:B2",
                destination_table_name="table1",
                schema={"col1": "string"},
            ),
        ]

        def get_sheet_side_effect(spreadsheet_id, *args, **kwargs):
            return {"values": [["col1"], [f"from_{spreadsheet_id}"]]}

        mock_sheets_resource.get_sheet.side_effect = get_sheet_side_effect

        factory = SheetsAssetFactory()
        assets = factory.create_assets(configs)
        df = assets[0](mock_context)

        expected_df = pd.DataFrame(
            {
                "col1": pd.Series(["from_id1", "from_id2"], dtype="string"),
                "source_google_sheet_id": pd.Series(["id1", "id2"], dtype="string"),
                "ds": pd.Series(
                    [datetime(2024, 1, 1).date(), datetime(2024, 1, 1).date()], dtype="object"
                ),
            }
        )

        pd.testing.assert_frame_equal(df, expected_df, check_dtype=True, check_index_type=False)

    def test_skip_missing_dev_spreadsheet(self, mock_context, mock_sheets_resource):
        """Test that sheets without dev spreadsheet_id are skipped."""
        configs = [
            SheetConfig(
                name="Dev Sheet",
                spreadsheet_id={"dev": "id1", "prod": "id1"},
                tab_name="tab1",
                sheet_range="A1:B2",
                destination_table_name="table1",
                schema={"col1": "string"},
            ),
            SheetConfig(
                name="Missing Dev Sheet",
                spreadsheet_id={"prod": "id2"},
                tab_name="tab2",
                sheet_range="A1:B2",
                destination_table_name="table1",
                schema={"col1": "string"},
            ),
        ]

        mock_sheets_resource.get_sheet.return_value = {"values": [["col1"], ["from_id1"]]}

        factory = SheetsAssetFactory()
        assets = factory.create_assets(configs)
        df = assets[0](mock_context)

        expected_df = pd.DataFrame(
            {
                "col1": pd.Series(["from_id1"], dtype="string"),
                "source_google_sheet_id": pd.Series(["id1"], dtype="string"),
                "ds": pd.Series([datetime(2024, 1, 1).date()], dtype="object"),
            }
        )

        pd.testing.assert_frame_equal(df, expected_df, check_dtype=True, check_index_type=False)
        assert mock_sheets_resource.get_sheet.call_count == 1
        mock_sheets_resource.get_sheet.assert_called_once_with("id1", "tab1!A1:B2")

    def test_group_configs_by_table(self):
        factory = SheetsAssetFactory()

        configs = [
            SheetConfig(
                name="First Group Sheet",
                spreadsheet_id={"dev": "id1", "prod": "id1"},
                tab_name="tab1",
                sheet_range="A1:B2",
                destination_table_name="table1",
                schema={"col1": "string"},
            ),
            SheetConfig(
                name="Second Group Sheet",
                spreadsheet_id={"dev": "id2", "prod": "id2"},
                tab_name="tab2",
                sheet_range="A1:B2",
                destination_table_name="table1",
                schema={"col1": "string"},
            ),
            SheetConfig(
                name="Third Group Sheet",
                spreadsheet_id={"dev": "id3", "prod": "id3"},
                tab_name="tab3",
                sheet_range="A1:B2",
                destination_table_name="table2",
                schema={"col1": "string"},
            ),
        ]

        grouped = factory._group_configs_by_table(configs)
        assert len(grouped) == 2
        assert len(grouped["table1"]) == 2
        assert len(grouped["table2"]) == 1

    def test_asset_execution(self):
        factory = SheetsAssetFactory()
        assets = factory.create_assets(
            [
                SheetConfig(
                    name="First Test Sheet",
                    spreadsheet_id={"dev": "id1", "prod": "id1"},
                    tab_name="tab1",
                    sheet_range="A1:B2",
                    destination_table_name="table1",
                    schema={"col1": "string", "col2": "float64"},
                ),
                SheetConfig(
                    name="Second Test Sheet",
                    spreadsheet_id={"dev": "id2", "prod": "id2"},
                    tab_name="tab2",
                    sheet_range="A1:B2",
                    destination_table_name="table1",
                    schema={"col1": "string", "col2": "float64"},
                ),
            ]
        )
        asset = assets[0]  # Take any asset for testing

        # Create a proper Dagster context
        sheets_resource = MagicMock()
        sheets_resource.get_sheet.return_value = {"values": [["col1", "col2"], ["val1", "0.05"]]}
        context = build_op_context(
            partition_key="2024-01-01 00:00:00", resources={"sheets_resource": sheets_resource}
        )

        # Test asset execution
        df = asset(context)

        assert len(df) == 2
        assert df["ds"].iloc[0] == datetime(2024, 1, 1).date()
        assert df["col1"].iloc[0] == "val1"
        assert df["col2"].iloc[0] == 0.05


class TestMixedValues:
    """Tests for handling mixed values and types in sheets."""

    def test_mixed_values_and_types(self, mock_context, mock_sheets_resource):
        factory = SheetsAssetFactory()

        # Create a config with mixed types
        config = SheetConfig(
            name="Mixed Values Test Sheet",
            spreadsheet_id={"dev": "id1", "prod": "id1"},
            tab_name="tab1",
            sheet_range="A1:E3",
            destination_table_name="table1",
            schema={
                "str_col": "string",
                "int_col": "int64",
                "float_col": "float64",
                "bool_col": "bool",
                "date_col": "datetime64[ns]",
            },
        )

        # Create a proper Dagster context with mixed values
        mock_sheets_resource.get_sheet.return_value = {
            "values": [
                ["str_col", "int_col", "float_col", "bool_col", "date_col"],
                ["text", "1", "1.5", "TRUE", "2024-01-01"],  # Complete row
                ["", "2", "", "FALSE", ""],  # Row with some empty values
            ]
        }

        # Test sheet reading
        df = factory._read_sheet(config, mock_context)

        expected_df = pd.DataFrame(
            {
                "str_col": pd.Series(["text", None], dtype="string"),
                "int_col": pd.Series([1, 2], dtype="Int64"),
                "float_col": pd.Series([1.5, None], dtype="float64"),
                "bool_col": pd.Series([True, False], dtype="boolean"),
                "date_col": pd.Series([pd.Timestamp("2024-01-01"), None], dtype="datetime64[ns]"),
                "source_google_sheet_id": pd.Series(["id1", "id1"], dtype="string"),
            }
        )

        pd.testing.assert_frame_equal(df, expected_df, check_dtype=True, check_index_type=False)
