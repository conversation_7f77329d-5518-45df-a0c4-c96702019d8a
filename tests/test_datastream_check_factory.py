from datetime import datetime
from uuid import UUID

import pandas as pd
import pytest
from dagster import TimeWindow

from warehouse.assets.datastream_check_factory import DataStreamChecker


# parametrize the test to test multiple cases
@pytest.mark.parametrize(
    "psql_df, bq_df, column_names, expected",
    [
        # same data in both (good)
        (
            pd.DataFrame({"id": [1, 2, 3]}),
            pd.DataFrame({"id": [1, 2, 3]}),
            ["id"],
            pd.DataFrame(columns=["combined"]),
        ),
        # exists in Postgres but not in BigQuery (bad)
        (
            pd.DataFrame({"id": [1, 2, 3, 4]}),
            pd.DataFrame({"id": [1, 2, 3]}),
            ["id"],
            pd.DataFrame(data={"combined": ["4"]}),
        ),
        # exists in BigQuery but not in Postgres (ok)
        (
            pd.DataFrame({"id": [1, 2, 3]}),
            pd.DataFrame({"id": [1, 2, 3, 4]}),
            ["id"],
            pd.<PERSON>Frame(columns=["combined"]),
        ),
        # multiple id columns, same data in both (good)
        (
            pd.DataFrame({"id1": [1, 2, 3], "id2": ["a", "b", "c"]}),
            pd.DataFrame({"id1": [1, 2, 3], "id2": ["a", "b", "c"]}),
            ["id1", "id2"],
            pd.DataFrame(columns=["combined"]),
        ),
        # multiple id columns, exists in Postgres but not in BigQuery (bad)
        (
            pd.DataFrame({"id1": [1, 2, 3, 4], "id2": ["a", "b", "c", "d"]}),
            pd.DataFrame({"id1": [1, 2, 3], "id2": ["a", "b", "c"]}),
            ["id1", "id2"],
            pd.DataFrame(data={"combined": ["4|d"]}),
        ),
        # multiple id columns, exists in BigQuery but not in Postgres (ok)
        (
            pd.DataFrame({"id1": [1, 2, 3], "id2": ["a", "b", "c"]}),
            pd.DataFrame({"id1": [1, 2, 3, 4], "id2": ["a", "b", "c", "d"]}),
            ["id1", "id2"],
            pd.DataFrame(columns=["combined"]),
        ),
        # same data in both, different types
        (
            pd.DataFrame({"id": [UUID("10bff177-f677-4318-8c1c-680f10f3abee")]}),
            pd.DataFrame({"id": ["10bff177-f677-4318-8c1c-680f10f3abee"]}),
            ["id"],
            pd.DataFrame(columns=["combined"]),
        ),
    ],
)
def test_find_missing_postgres_records(psql_df, bq_df, column_names, expected):
    diff = DataStreamChecker._compare_pks(psql_df, bq_df, column_names)
    diff["combined"] = diff[column_names].apply(
        lambda row: "|".join(row.astype(str).values), axis=1
    )
    assert set(diff["combined"].to_list()) == set(expected["combined"].to_list())


def test_split_asset_name():
    assert DataStreamChecker._split_asset_name("public_table") == ("public", "table")
    assert DataStreamChecker._split_asset_name("public_table_with_underscores") == (
        "public",
        "table_with_underscores",
    )
    with pytest.raises(ValueError):
        DataStreamChecker._split_asset_name("invalid.asset_name")


def test_get_query_window():
    grace_period = pd.Timedelta(minutes=15)
    partition_time_window = TimeWindow(
        start=datetime.fromisoformat("2024-01-03T00:00:00"),
        end=datetime.fromisoformat("2024-01-03T01:00:00"),
    )
    assert DataStreamChecker.get_query_window(partition_time_window, grace_period) == (
        datetime.fromisoformat("2024-01-02T23:45:00"),
        datetime.fromisoformat("2024-01-03T00:45:00"),
    )
