from unittest import mock

from warehouse import EncordResource


def _get_encord_resource():
    encord_resource = EncordResource(
        encord_api_key_name="encord_api_key_name",
        encord_project_hashes=["encord_project_hash"],
    )
    encord_resource._encord_user_client = mock.MagicMock()
    bundle_init = mock.MagicMock()
    project = mock.MagicMock()
    project.list_label_rows_v2 = mock.MagicMock(
        return_value=[
            mock.MagicMock(
                data_hash="TEST",
                workflow_graph_node=mock.MagicMock(title="TEST"),
                last_edited_at="TEST",
            )
        ]
    )
    project.create_bundle = mock.MagicMock(return_value=bundle_init)
    encord_resource._encord_user_client.get_project = mock.MagicMock(return_value=project)
    return encord_resource, project, bundle_init


def test_get_labels():
    encord_resource, project, bundle_init = _get_encord_resource()

    results = encord_resource.get_labels()
    encord_resource._encord_user_client.get_project.assert_called_once()
    encord_resource._encord_user_client.get_project.assert_called_with("encord_project_hash")
    project.create_bundle.assert_called_once()

    assert len(results) == 1
    results[0].label_row.initialise_labels.assert_called_once()
    results[0].label_row.initialise_labels.assert_called_with(bundle=bundle_init)
    project.list_label_rows_v2.assert_called_once()
    project.list_label_rows_v2.assert_called_with(
        include_client_metadata=True,
        workflow_graph_node_title_eq="Complete",
    )
