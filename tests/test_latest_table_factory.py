from unittest import mock

import pytest
from google.cloud import bigquery

from warehouse.assets.latest_table_factory import LatestTableFactory

_CURRENT_SCHEMA = [
    bigquery.SchemaField("full_name", "STRING"),
    bigquery.SchemaField("age", "INTEGER"),
]

_DEST_TABLE_ID = "dest_table_id"


@mock.patch("dagster_gcp.bigquery.io_manager.BigQueryClient")
def test_latest_table_factory_synchronize_schema_no_change(
    mocked_bigquery_client: mock.MagicMock,
) -> None:
    mocked_context = mock.MagicMock()
    mocked_bigquery_client.get_table.return_value = mock.MagicMock(schema=_CURRENT_SCHEMA)
    LatestTableFactory._synchronize_schemas(
        mocked_context, mocked_bigquery_client, _CURRENT_SCHEMA, _DEST_TABLE_ID
    )

    mocked_context.log.info.assert_called_once()
    mocked_bigquery_client.get_table.assert_called_once_with(_DEST_TABLE_ID)
    assert not mocked_bigquery_client.update_table.called, "Schema updates should not be made"


@mock.patch("dagster_gcp.bigquery.io_manager.BigQueryClient")
def test_latest_table_factory_synchronize_schema_added_columns(
    mocked_bigquery_client: mock.MagicMock,
) -> None:
    mocked_context = mock.MagicMock()
    mocked_dst_table = mock.MagicMock(schema=_CURRENT_SCHEMA)
    mocked_bigquery_client.get_table.return_value = mocked_dst_table
    src_schema = _CURRENT_SCHEMA + [bigquery.SchemaField("height", "INTEGER")]
    LatestTableFactory._synchronize_schemas(
        mocked_context, mocked_bigquery_client, src_schema, _DEST_TABLE_ID
    )
    mocked_bigquery_client.update_table.assert_called_once_with(mocked_dst_table, ["schema"])
    mocked_bigquery_client.get_table.assert_called_once_with(_DEST_TABLE_ID)
    mocked_context.log.info.assert_called_once()


@mock.patch("dagster_gcp.bigquery.io_manager.BigQueryClient")
def test_latest_table_factory_synchronize_schema_deleted_columns(
    mocked_bigquery_client: mock.MagicMock,
) -> None:
    src_schema = [_CURRENT_SCHEMA[0]]
    mocked_context = mock.MagicMock()
    mocked_bigquery_client.get_table.return_value = mock.MagicMock(schema=_CURRENT_SCHEMA)

    with pytest.raises(ValueError) as error:
        LatestTableFactory._synchronize_schemas(
            mocked_context, mocked_bigquery_client, src_schema, _DEST_TABLE_ID
        )

    mocked_bigquery_client.get_table.assert_called_once_with(_DEST_TABLE_ID)
    assert "Columns {'age'} are in latest table but not in the source table." in str(error.value)
    assert not mocked_bigquery_client.update_table.called, "Schema updates should not be made"


@mock.patch("dagster_gcp.bigquery.io_manager.BigQueryClient")
def test_latest_table_factory_synchronize_schema_add_and_delete_cols(
    mocked_bigquery_client: mock.MagicMock,
) -> None:
    src_schema = [_CURRENT_SCHEMA[0], bigquery.SchemaField("renamed_age_column", "INTEGER")]

    mocked_context = mock.MagicMock()
    mocked_bigquery_client.get_table.return_value = mock.MagicMock(schema=_CURRENT_SCHEMA)

    with pytest.raises(ValueError) as error:
        LatestTableFactory._synchronize_schemas(
            mocked_context, mocked_bigquery_client, src_schema, _DEST_TABLE_ID
        )

    mocked_bigquery_client.get_table.assert_called_once_with(_DEST_TABLE_ID)
    assert "Columns {'age'} are in latest table but not in the source table." in str(error.value)
    assert not mocked_bigquery_client.update_table.called, "Schema updates should not be made"
