import pandas as pd
from google.cloud.bigquery import <PERSON>hema<PERSON>ield

from warehouse.utils.io_managers.date_partitioned_bigquery_pandas_type_handler import (
    DatePartitionedBigQueryPandasTypeHandler,
)


class TestBQPandasTypeHandler:
    def test_get_schema(self):
        test_bq_type_handler = DatePartitionedBigQueryPandasTypeHandler()

        test_scalar_schema = {"id": "STRING"}
        assert test_bq_type_handler._get_schema(pd.DataFrame(), test_scalar_schema, "ds") == [
            SchemaField("id", "STRING")
        ]

        test_schema_with_scalar_list = {"ids": ["STRING"]}
        assert test_bq_type_handler._get_schema(
            pd.DataFrame(), test_schema_with_scalar_list, "ds"
        ) == [SchemaField("ids", "STRING", mode="repeated")]

        test_schema_with_struct = {"object": {"id": "STRING"}}
        assert test_bq_type_handler._get_schema(pd.DataFrame(), test_schema_with_struct, "ds") == [
            SchemaField("object", "struct", fields=[SchemaField("id", "STRING")])
        ]

        test_schema_with_list_struct = {"objects": [{"id": "STRING"}]}
        assert test_bq_type_handler._get_schema(
            pd.DataFrame(), test_schema_with_list_struct, "ds"
        ) == [
            SchemaField("objects", "struct", mode="repeated", fields=[SchemaField("id", "STRING")])
        ]
