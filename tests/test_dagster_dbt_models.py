import pytest
from dagster import <PERSON><PERSON><PERSON><PERSON>

from warehouse import get_source_group_name
from warehouse.assets.dbt_models import DBTTranslator, dbt_models
from warehouse.configs.warehouse_config import WarehouseDatasets


def test_dbt_model__asset_key() -> None:
    datasets = set([v.value for v in WarehouseDatasets.__members__.values()])
    models = dbt_models
    assert all(model.key.path[0] in datasets for model in models)


def test_dbt_translator__get_group_name() -> None:
    translator = DBTTranslator()

    assert (
        translator.get_group_name({"path": "silver/some_project/some_subdir/my_model.sql"})
        == "some_project"
    )


def test_dbt_translator__get_group_name__exception() -> None:
    translator = DBTTranslator()

    path = "iron/some_project/some_subdir/my_model.sql"
    dw_datasets = [v.value for v in WarehouseDatasets.__members__.values()]

    with pytest.raises(AssertionError) as exc_info:
        translator.get_group_name({"path": path})

    expected_error_message = (
        f"Unexpected model path from DBT: dbt/models/{path}. "
        "Models must be placed dbt/models/<target_dataset>/<project_name>, "
        f"where the target dataset is one of {dw_datasets}. Got: iron."
    )

    assert str(exc_info.value) == expected_error_message


def test_source_asset__get_group_name__none() -> None:
    assert get_source_group_name(AssetKey(["ml_feature_store_postgres", "master_table"])) is None


def test_source_asset__get_group_name__api() -> None:
    assert get_source_group_name(AssetKey(["bronze", "public_table"])) == "api_postgres"


def test_source_asset__get_group_name__analytics() -> None:
    assert get_source_group_name(AssetKey(["bronze", "analytics_table"])) == "analytics_postgres"


def test_source_asset__get_group_name__dev_realtime() -> None:
    assert get_source_group_name(AssetKey(["dev_realtime", "images"])) == "dev_realtime"


def test_source_asset__get_group_name__prod_realtime() -> None:
    assert get_source_group_name(AssetKey(["prod_realtime", "images"])) == "prod_realtime"
