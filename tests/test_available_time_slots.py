import os
from datetime import datetime, timedelta
from unittest.mock import MagicMock

import pandas as pd
import pytest
from apella_cloud_api.new_api_server_schema import ApellaSchema
from apella_cloud_api.new_client_schema import GQLAvailableTimeSlot, GQLQuery
from pandas._testing import assert_frame_equal
from zoneinfo import ZoneInfo

from warehouse.utils.available_time_slots.available_time_slots import (
    get_available_time_slots_df,
    pandera_output_schema,
)

os.environ["WAREHOUSE_ENVIRONMENT"] = "dev"
pd.set_option("display.max_columns", None)


@pytest.fixture
def apella_client() -> MagicMock:
    return MagicMock()


@pytest.fixture
def timezone() -> str:
    return "America/New_York"


def test_pandera_schema() -> None:
    email_date = pd.Timestamp("2025-01-10").date()
    df = pd.DataFrame(
        {
            "site_id": ["site_id", "site_id"],
            "room_id": ["empty_block_time_ids", "room_id"],
            "start_time": [
                pd.Timestamp("2025-01-13 16:54:12.758889+0000", tz="UTC"),
                pd.Timestamp("2025-01-13 16:54:12.758913+0000", tz="UTC"),
            ],
            "end_time": [
                pd.Timestamp("2025-01-13 17:54:12.758892+0000", tz="UTC"),
                pd.Timestamp("2025-01-13 17:54:12.758913+0000", tz="UTC"),
            ],
            "block_time_ids": [[], ["block_1", "block_2"]],
            "max_available_duration_minutes": [30, 35],
            "email_date": [email_date, email_date],
            "surgery_date": [pd.Timestamp("2025-01-13").date(), pd.Timestamp("2025-01-13").date()],
        }
    )
    pandera_output_schema.validate(df)


def test_get_available_time_slots_df(apella_client: MagicMock, timezone: str) -> None:
    site_tzinfo = ZoneInfo(timezone)
    utc_tzinfo = ZoneInfo("UTC")
    now = datetime.now().astimezone(site_tzinfo)
    email_date = pd.Timestamp.utcnow().tz_convert(timezone).date()
    # now = datetime.now()
    available_time_slots: list[GQLAvailableTimeSlot] = [
        GQLAvailableTimeSlot(
            room_id="empty_block_time_ids",
            start_time=now,
            end_time=now + timedelta(hours=1),
            max_available_duration=timedelta(minutes=30),
            block_time_ids=[],
        ),
        GQLAvailableTimeSlot(
            room_id="room_id",
            start_time=now,
            end_time=now + timedelta(hours=1),
            max_available_duration=timedelta(minutes=35),
            block_time_ids=["block_1", "block_2"],
        ),
    ]
    apella_client.query_graphql_from_schema.return_value = GQLQuery(
        available_time_slots=available_time_slots
    )
    apella_schema = ApellaSchema()

    result = get_available_time_slots_df(apella_client, apella_schema, "site_id", timezone, 1, 30)

    expected = pd.DataFrame(
        [
            {
                "site_id": "site_id",
                "room_id": "empty_block_time_ids",
                "start_time": available_time_slots[0].start_time.astimezone(utc_tzinfo),
                "end_time": available_time_slots[0].end_time.astimezone(utc_tzinfo),
                "block_time_ids": available_time_slots[0].block_time_ids,
                "max_available_duration_minutes": 30,
                "email_date": email_date,
                "surgery_date": available_time_slots[0].start_time.date(),
            },
            {
                "site_id": "site_id",
                "room_id": "room_id",
                "start_time": available_time_slots[1].start_time.astimezone(utc_tzinfo),
                "end_time": available_time_slots[1].end_time.astimezone(utc_tzinfo),
                "block_time_ids": available_time_slots[1].block_time_ids,
                "max_available_duration_minutes": 35,
                "email_date": email_date,
                "surgery_date": available_time_slots[1].start_time.date(),
            },
        ]
    )
    assert_frame_equal(result, expected)
