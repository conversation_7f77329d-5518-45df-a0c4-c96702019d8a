import json
from typing import Any, Optional

import pytest
from dagster_dbt import DbtCliResource

from warehouse.utils.dbt.helpers import get_dbt_cli


@pytest.fixture
def dbt_cli() -> DbtCliResource:
    return get_dbt_cli()


def run_test(
    dbt_cli: DbtCliResource,
    for_feature_fetch: Optional[bool] = None,
    dbt_data_test_against_sandbox: Optional[bool] = False,
    sandbox_models_for_test: Optional[list[str]] = None,
    test_macro: str = "run_unit_tests",
    ds: str = "2023-05-18 00:00:00",
    expected_unit_test_result: str | None = None,
):
    vars: dict[str, Any] = {"ds": ds}
    if for_feature_fetch is not None:
        vars["for_feature_fetch"] = for_feature_fetch
    if dbt_data_test_against_sandbox is not None:
        vars["dbt_data_test_against_sandbox"] = dbt_data_test_against_sandbox
    if sandbox_models_for_test is not None:
        vars["sandbox_models_for_test"] = ",".join(sandbox_models_for_test)
    if expected_unit_test_result:
        vars["expected_unit_test_result"] = expected_unit_test_result

    invocation = dbt_cli.cli(
        args=[
            "--quiet",
            "run-operation",
            test_macro,
            "--vars",
            json.dumps(vars),
        ],
        raise_on_error=False,
    )

    assert invocation.is_successful(), list(invocation.stream_raw_events())


def test_macros_for_warehouse(dbt_cli):
    run_test(dbt_cli)


def test_ds(dbt_cli):
    for ds, tz in (
        ("2024-12-23", "UTC"),
        ("2025-01-01", "America/Los_Angeles"),
        ("2026-01-01", "America/Los_Angeles"),
    ):
        run_test(
            dbt_cli,
            ds=f"{ds} 00:00:00",
            test_macro="test_ds_default",
            expected_unit_test_result=f"date('{ds} 00:00:00 {tz}')",
        )
        run_test(
            dbt_cli,
            ds=f"{ds} 00:00:00",
            test_macro="test_ds_as_timestamp",
            expected_unit_test_result=f"'{ds} 00:00:00 {tz}'",
        )


def test_ds_for_feature_fetch(dbt_cli):
    run_test(
        dbt_cli,
        ds="1970-01-01 00:00:00",
        test_macro="test_ds_default_for_feature_fetch_set",
        expected_unit_test_result="'{ds}'",
        for_feature_fetch=True,
    )


def test_macros_for_feature_fetch__true(dbt_cli):
    run_test(dbt_cli, for_feature_fetch=True)


def test_macros_dbt_sandbox_testing__true(dbt_cli):
    run_test(dbt_cli, dbt_data_test_against_sandbox=True)


def test_macros_dbt_sandbox_testing__false__specific_models(dbt_cli):
    run_test(
        dbt_cli, dbt_data_test_against_sandbox=False, sandbox_models_for_test=["sandbox-model"]
    )


def test_macros_dbt_sandbox_testing__true__specific_models(dbt_cli):
    run_test(dbt_cli, dbt_data_test_against_sandbox=True, sandbox_models_for_test=["sandbox-model"])
