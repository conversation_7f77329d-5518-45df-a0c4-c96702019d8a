import gzip
import json
import zipfile
from io import BytesIO
from unittest import mock

from warehouse.utils.amplitude_events.amplitude import AmplitudeResource


@mock.patch("warehouse.utils.amplitude_events.amplitude.SecretManagerServiceClient")
@mock.patch("requests.get")
def test_amplitude_resource_fetch_events(
    mock_get: mock.MagicMock, mock_secret_manager: mock.MagicMock
) -> None:
    with open("./tests/amplitude_mock_data/349420_2024-08-28_0#0.json", "r") as file:
        json_lines = file.readlines()

    access_secret_version = mock.MagicMock()
    access_secret_version.return_value.payload.data = b"test"
    mock_secret_manager.return_value = access_secret_version

    gz_bytes = BytesIO()
    with gzip.GzipFile(fileobj=gz_bytes, mode="wb") as gz:
        for line in json_lines:
            json_bytes = line.encode("utf-8")
            gz.write(json_bytes)

    zip_bytes = BytesIO()
    with zipfile.ZipFile(zip_bytes, "w") as z:
        z.writestr("data.json.gz", gz_bytes.getvalue())

    zip_bytes.seek(0)

    mock_response = mock.MagicMock()
    mock_response.content = zip_bytes.getvalue()
    mock_get.return_value = mock_response

    mocked_context = mock.MagicMock()
    amplitude_resource = AmplitudeResource(amplitude_api_key="", amplitude_secret_key="")
    amplitude_resource.setup_for_execution(mocked_context)
    events = amplitude_resource.fetch_events("1111-11-11 01:01:01")

    assert len(events) == 5
    assert events[0].user_id == "samlp|<EMAIL>"
    assert events[1].insert_id == "500d5d7d-d6b5-4649-bad8-c8954b91e07c"
    assert json.loads(events[2].user_properties)["roles"] == ["Apella Developer"]
    assert events[3].event_type == "[Amplitude] Page Viewed"
    assert events[4].uuid == "15b099a5-**************-b1a2bfdc12e3"
