import gzip
from unittest import mock

import pytest
import requests

from warehouse.utils.user_management.auth0 import Auth0Resource

_APELLA_DASHBOARD_ROLE = "Apella Dashboard User"
_APELLA_DEVELOPER_ROLE = "Apella Developer"
_JOB_ID = "123"
_ORG_ID = "org_123"
_ORG_NAME = "Org 123"
_USER_ID1 = "auth0|62ab5a99430b9"
_USER_ID2 = "auth0|6019baa"


def _get_auth0_resource():
    auth0_resource = Auth0Resource(
        auth0_client_id_name="auth0_client_id_name",
        auth0_client_secret_name="auth0_client_secret_name",
        auth0_domain="auth0_domain",
    )
    auth0_resource._auth0_client = mock.MagicMock()
    return auth0_resource


def test_get_auth0_orgs():
    auth0_resource = _get_auth0_resource()
    auth0_resource._auth0_client.organizations.all_organizations = mock.MagicMock(
        return_value={"organizations": [{"id": _ORG_ID, "display_name": _ORG_NAME}]}
    )
    orgs = auth0_resource.get_orgs()
    assert orgs == [
        {
            "org_id": _ORG_ID,
            "org_name": _ORG_NAME,
        }
    ]


def test_get_auth0_org_roles_info():
    auth0_resource = _get_auth0_resource()
    auth0_resource._auth0_client.organizations.all_organization_members = mock.MagicMock(
        return_value={
            "members": [
                {
                    "user_id": _USER_ID1,
                    "roles": [
                        {
                            "id": "rol_SvFe0F9T8",
                            "name": _APELLA_DEVELOPER_ROLE,
                        }
                    ],
                },
                {
                    "user_id": _USER_ID2,
                    "roles": [
                        {
                            "id": "rol_I19sS7Tk",
                            "name": _APELLA_DASHBOARD_ROLE,
                        },
                        {
                            "id": "rol_SvFe0F9T8",
                            "name": _APELLA_DEVELOPER_ROLE,
                        },
                    ],
                },
            ]
        }
    )
    roles = auth0_resource.get_user_roles(_ORG_ID)
    assert roles == [
        {
            "org_id": _ORG_ID,
            "user_id": _USER_ID1,
            "roles": [_APELLA_DEVELOPER_ROLE],
        },
        {
            "org_id": _ORG_ID,
            "user_id": _USER_ID2,
            "roles": [_APELLA_DASHBOARD_ROLE, _APELLA_DEVELOPER_ROLE],
        },
    ]


def test_get_auth0_user_info_failed_job():
    auth0_resource = _get_auth0_resource()
    auth0_resource._auth0_client.jobs.export_users = mock.MagicMock(return_value={"id": _JOB_ID})
    auth0_resource._auth0_client.jobs.get = mock.MagicMock(return_value={"status": "failed"})
    with pytest.raises(ValueError) as exc_info:
        auth0_resource.get_user_info()

    expected_error_message = f"Failed to get export job for job ID: {_JOB_ID}."
    assert str(exc_info.value) == expected_error_message


def test_get_auth0_user_info_success_job():
    auth0_resource = _get_auth0_resource()
    auth0_resource._auth0_client.jobs.export_users = mock.MagicMock(return_value={"id": _JOB_ID})
    auth0_resource._auth0_client.jobs.get = mock.MagicMock(
        return_value={"status": "completed", "location": "unused.json.gz"}
    )
    requests.get = mock.MagicMock()
    gzip.decompress = mock.MagicMock(
        return_value=('{ "user_id": "' + _USER_ID1 + '"}\n{"user_id": "' + _USER_ID2 + '"}').encode(
            "utf-8"
        )
    )
    users = auth0_resource.get_user_info()
    assert requests.get.call_count == 1
    assert users == [{"user_id": _USER_ID1}, {"user_id": _USER_ID2}]
