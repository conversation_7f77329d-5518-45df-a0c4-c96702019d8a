import datetime
from unittest import mock

import twilio.rest  # type: ignore

from warehouse.utils.twilio_notifications.twilio import TwilioResource


def _get_twilio_resource():
    twilio_resource = TwilioResource(
        twilio_auth_token_name="twilio_auth_token_name",
        twilio_account_sid_name="twilio_account_sid_name",
    )
    # twilio_resource.get_notifications = mock.MagicMock()
    twilio_resource._twilio_client = mock.MagicMock()
    twilio_resource._twilio_client.messages = mock.MagicMock()
    messagePage = mock.MagicMock()
    messagePage.next_page = mock.MagicMock(return_value=None)
    twilio_resource._twilio_client.messages.page = mock.MagicMock(return_value=messagePage)
    twilio.rest.Client = mock.MagicMock()
    return twilio_resource, messagePage


def test_get_notifications():
    twilio_resource, messagePage = _get_twilio_resource()
    # twilio_resource.get_notifications = mock.MagicMock()
    twilio_resource.get_notifications("1111-11-11 01:01:01")
    assert twilio_resource._twilio_client.messages.page.call_count == 1
    assert twilio_resource._twilio_client.messages.page.call_args[1][
        "date_sent"
    ] == datetime.datetime(1111, 11, 11, 1, 1, 1)
    assert messagePage.next_page.call_count == 1
