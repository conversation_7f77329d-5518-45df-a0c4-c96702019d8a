[sqlfluff]
templater = dbt
max_line_length = 120
large_file_skip_byte_limit = 25000
exclude_rules = RF01, AL09, LT14, RF02

[sqlfluff:templater:dbt]
project_dir = dbt
profiles_dir = dbt/config

[sqlfluff:templater:jinja]
apply_dbt_builtins = True

[sqlfluff:templater:dbt:context]
ds = 2023-05-18 00:00:00

# following the DBT style guide
[sqlfluff:rules:capitalisation.keywords]
capitalisation_policy = lower
[sqlfluff:rules:capitalisation.identifiers]
capitalisation_policy = lower
[sqlfluff:rules:capitalisation.functions]
extended_capitalisation_policy = lower
[sqlfluff:rules:capitalisation.literals]
capitalisation_policy = lower
[sqlfluff:rules:capitalisation.types]
extended_capitalisation_policy = lower
[sqlfluff:rules:ambiguous.join]
fully_qualify_join_types = both
