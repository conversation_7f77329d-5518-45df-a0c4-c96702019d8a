FROM python:3.10-slim

RUN apt-get update && apt-get upgrade -y

#
# Install Python dependencies
#
RUN apt install curl -y
RUN curl -sSL https://install.python-poetry.org | POETRY_VERSION=1.5.1 python -
ENV PATH /root/.local/bin:${PATH}

ENV DAGSTER_HOME=/opt/dagster/dagster_home
RUN mkdir -p $DAGSTER_HOME

# venv not needed
RUN poetry config virtualenvs.create false
RUN poetry self add keyrings.google-artifactregistry-auth

COPY pyproject.toml .
COPY poetry.lock .

RUN --mount=type=secret,id=google-application-credentials,mode=444,target=/root/.config/gcloud/application_default_credentials.json \
    poetry install --no-interaction --only main

WORKDIR $DAGSTER_HOME

COPY docker/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

#
# Install DBT dependencies
#
COPY dbt/dbt_project.yml dbt/dbt_project.yml
COPY dbt/packages.yml dbt/packages.yml

WORKDIR $DAGSTER_HOME/dbt

RUN poetry run dbt deps --profiles-dir config

#
# Copy repo into working dir
#
WORKDIR $DAGSTER_HOME

COPY workspace.yaml .
COPY warehouse warehouse/
COPY dbt dbt/
COPY tests tests/

#
# Run gRPC on port 4000
#
EXPOSE 4000


ENTRYPOINT [ "/entrypoint.sh" ]
