#!/bin/bash

# Exit the script as soon as a command fails
set -e

if [[ -z "$WAREHOUSE_ENVIRONMENT" ]]; then
    echo "You must set WAREHOUSE_ENVIRONMENT environment variable to run the image" 1>&2
    exit 1
fi

echo "Creating DBT manifest ..."
cd dbt
poetry run dbt compile -t ${WAREHOUSE_ENVIRONMENT} \
    --vars '{ds: "____-__-__ __:__:__"}' \
    --profiles-dir config

echo "Starting up Dagster ..."
cd ../
exec "$@"

