# Dags<PERSON> + DBT on BigQuery

## Overview

This repository enables creating tables in BigQuery using Dagster and DBT. Tables can be created by one of two ways:

- DBT Model
- Python Asset

Under the hood, <PERSON><PERSON><PERSON> converts all DBT models to Dagster Assets, creates a dependency graph across all Assets, and schedules and manages the execution of that graph.

When used together, Dagster + DBT gives us a great deal of flexibility when building out our Apella data warehouse by enabling us to select the right tool to get the job done on a per-project basis.

### Links

Some useful tools and infrastructure related to this repo:

- Dags<PERSON> [dev](https://dagster.dev.internal.apella.io/asset-groups) | [prod](https://dagster.internal.apella.io/asset-groups)
- BigQuery [dev](https://console.cloud.google.com/bigquery?project=dev-data-platform-439b4c) | [prod](https://console.cloud.google.com/bigquery?project=prod-data-platform-027529)
- Datastream [dev](https://console.cloud.google.com/datastream/streams?project=dev-data-platform-439b4c) | [prod](https://console.cloud.google.com/datastream/streams?project=prod-data-platform-027529)
- Hex [Notebooks](https://hc.hex.tech/apella/home)

## Repository Setup

This repo uses `pyenv` for managing Python installations, and `poetry` for managing third party Python packages and dependencies.

You can find setup instructions for those tools [here](https://www.notion.so/apella/Python-Development-2c24691a9eb54831a04787908c3eda81#5f7b0d524f7145ebb123930e31387544)

### Install dependencies

Install Python and DBT dependencies together via

```
$ make install
```

### Verify Installation

To verify that your setup and installation is complete,

```
$ make run-tests
$ make dbt-compile-dev select=cases ds='2023-05-18 00:00:00'
```

## Warehouse Standards

Before your first PR in the repo, please read our Warehouse & Repository standards documentation [here](./standards.md).

## Contributing Code

If you are writing a DBT model, head over to the DBT [README](./dbt/README.md) for a consolidated developer workflow.


Otherwise if you are generating a table using a Dagster Python Asset, check out the Dagster [README](./warehouse/README.md).

Finally when you are ready to submit a PR, make sure your changes will pass our simple code review [checklist](./review.md).

## Reviewing Code

If you'd like to review PRs, please follow our [Simple Checklist](./review.md).

## Releasing Code

Finally, when it's time to merge a PR, please follow our [Release Workflow](./release.md)
