# Pattern to speed up cloudbuild from Google https://cloud.google.com/build/docs/optimize-builds/speeding-up-builds#using_a_cached_docker_image
steps:
  - name: 'gcr.io/cloud-builders/docker'
    entrypoint: 'bash'
    args:
      ['-c', 'docker pull ${_CONTAINER_REGISTRY}/data-warehouse:latest || exit 0']
  - name: 'gcr.io/cloud-builders/docker'
    args:
      [
        'build',
        '-f',
        'docker/Dockerfile',
        '--network',
        'cloudbuild',
        '--cache-from',
        '${_CONTAINER_REGISTRY}/data-warehouse:latest',
        '-t',
        '${_CONTAINER_REGISTRY}/data-warehouse:${_VERSION}',
        '.',
      ]
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', '${_CONTAINER_REGISTRY}/data-warehouse:${_VERSION}']
images: ['${_CONTAINER_REGISTRY}/data-warehouse:${_VERSION}']
substitutions:
  _VERSION: version
  _CONTAINER_REGISTRY: gcp-container-repository