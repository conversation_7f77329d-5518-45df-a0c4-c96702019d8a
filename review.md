# Code Review Checklist

In addition to Apella's [Code Review Norms](https://www.notion.so/apella/Code-Review-Norms-10-78ff317f288c4508adb31770fb9a3174), this repository has specific requirements for every PR that wants to merge to `main`.

As an author or reviewer, you sign up to enforce these requirements to keep our warehouse safe and manageable.

## Checklist

This can be boiled down to:

1. Enforce repository and warehouse standards
2. Require a thorough test plan

### Enforce Standards

Every new table must follow our [Warehouse Standards](./standards.md). To summarize, every new table must:

1. be defined in the appropriate dataset directory and project subdirectory
2. write to a `ds` partition
3. read from upstream tables using `{{ ref }}` or `{{ source }}`
    1. filter on `ds` if the upstream table is partitioned on `ds`
5. have a standardized table name
6. include a schema in the corresponding `schema.yml`
    1. the schema must include table and column descriptions where not obvious

### Test Plan

Every PR needs a test plan confirming that the introduced changes are working as expected.

1. Test changes against production data by materializing a new table partition in `sandbox`
2. Fill out the *How To Test* PR template by either:
    1. Successful run of DBT data tests (coming in Q3)
    2. Linking to a [Hex Notebook](https://hc.hex.tech/apella/home) validating the proposed changes in `sandbox`
    3. In absense of a notebook, providing a thorough description of how the data in `sandbox` was validated against the PR changes