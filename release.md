# Releasing Code

When you release code, you are responsible for the entire release end-to-end. This means making sure the release is working as expected, and to raise the alarm bells in #guild-data-delegates when things are broken (until we have better automated alerting in place).

Each environment has a different release process.

## `dev`

All PR merges to `main` go out to `dev` automatically. To track the release,

1. Find your build [here](https://github.com/Apella-Technology/data-warehouse/actions?query=branch%3Amain)
2. Wait for your new image's sha256 image hash to update [here](https://dagster.dev.internal.apella.io/locations)
3. Validate the deployment (see below)

## `prod`

Every time you cut a release to production, you assume the interim title of Release Captain. Release captains must:

1. Ping all commit authors who have changes going out to production, and get their sign off
2. Cut a release to production via GitHub release workflow [here](https://github.com/Apella-Technology/data-warehouse/releases) by publishing the latest release draft that includes your changes.
3. Wait for the release version to update [here](https://dagster.internal.apella.io/locations)
3. Validate the deployment (see below)

## Validating Deployments

In the future, we will cover most of these steps with automated metrics and alerting.

But for now, it's important that every time you release code, you validate that:

1. Nothing is broken
2. Your new changes are working as expected

### Nothing Is Broken

For general functionality, confirm that:

1. The `data-warehouse` [code location](https://dagster.internal.apella.io/locations) is `Loaded` with a green status, with the version number in the image subtitle reflecting your release
2. All [schedules](https://dagster.internal.apella.io/overview/schedules) under `data-warehouse` are loaded and green
3. Assets are [loading](https://dagster.internal.apella.io/asset-groups)

### New Changes Are Working

If this is a production release, ping each commit author to verify their changes by either:

- Backfill an old partition, or
- Verifying that new assets materialize and catch up

