name: Validate

on:
  push:
    branches: [main]
  pull_request:

jobs:
  Unit-Tests:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Python Environment
        uses: Apella-Technology/setup-python-env@v1
        with:
          artifact_registry_credentials: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          poetry_version: 1.5.1
          python_version: '3.10'

      # Google auth is required in order to run the `dbt` commands below
      - name: Authenticate to Google Cloud
        id: google-auth
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ secrets.GOOGLE_APPLICATION_CREDENTIALS }}
          token_format: 'access_token'

      - name: Install DBT deps
        run: cd dbt && poetry run dbt deps --profiles-dir config

      - name: Compile DBT
        run: |
          cd dbt &&
          poetry run dbt compile --target dev --profiles-dir config --vars '{ds: "____-__-__ __:__:__"}'

      - name: Run Unit Tests
        run: |
          export WAREHOUSE_ENVIRONMENT=dev &&
          export WAREHOUSE_RUNTIME_ENVIRONMENT=local &&
          poetry run pytest tests

  Lint:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Python Environment
        uses: Apella-Technology/setup-python-env@v1
        with:
          artifact_registry_credentials: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          poetry_version: 1.5.1
          python_version: '3.10'

      # Google auth is required in order to run the `dbt` commands below
      - name: Authenticate to Google Cloud
        id: google-auth
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ secrets.GOOGLE_APPLICATION_CREDENTIALS }}
          token_format: 'access_token'

      - name: Install DBT deps
        run: cd dbt && poetry run dbt deps --profiles-dir config

      - name: Compile DBT
        run: |
          cd dbt &&
          poetry run dbt compile --target dev --profiles-dir config --vars '{ds: "____-__-__ __:__:__"}'

      - name: Lint
        run: make lint

  Run-Modified-Models-Tests-Macros:
    runs-on:
      - self-hosted
      - prod

    steps:
      # Temporary - make commands will be deprecated in this repo
      - name: Install Make
        run: |
          apt-get update &&
          apt-get install -y make

      - name: Git Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Git Fetch Origin Main
        run: git fetch origin main

      - name: Setup Python Environment
        uses: Apella-Technology/setup-python-env@v1
        with:
          artifact_registry_credentials: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          poetry_version: 1.5.1
          python_version: '3.10'

      - name: Install DBT deps
        run: cd dbt &&
          poetry run dbt deps --profiles-dir config

      - name: Compile DBT
        run: |
          cd dbt &&
          poetry run dbt compile --target prod --profiles-dir config --vars '{ds: "____-__-__ __:__:__", upstream_prod_enabled: true}'

      - name: Run modified Models, Tests, and Macros
        run: |
          export WAREHOUSE_ENVIRONMENT=prod &&
          export WAREHOUSE_RUNTIME_ENVIRONMENT=local &&
          poetry run python -m warehouse.scripts.dbt_run_modified_models_and_tests --pull-request-number=${{ github.event.pull_request.number || github.run_number }}

  Run-Feature-Fetch-Query-Test:
    runs-on:
      - self-hosted
      - prod

    steps:
      - name: Install Make
        run: |
          apt-get update &&
          apt-get install -y make

      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Python Environment
        uses: Apella-Technology/setup-python-env@v1
        with:
          artifact_registry_credentials: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          poetry_version: 1.5.1
          python_version: '3.10'

      - name: Install DBT deps
        run: cd dbt &&
          poetry run dbt deps --profiles-dir config

      - name: Test inference SQL
        run: |
          make dbt-compile-feature-fetch
