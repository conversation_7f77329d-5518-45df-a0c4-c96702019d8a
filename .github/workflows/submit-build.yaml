name: Build

on:
  push:
    branches: [main]
  pull_request:
  release:
    types: [published]

env:
  CONTAINER_REGISTRY: us-central1-docker.pkg.dev/prod-platform-29b5cb/prod-docker-registry

jobs:
  Submit-Build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Build Container
        uses: Apella-Technology/build-docker-container@v2
        with:
          CONTAINER_REPOSITORY: data-warehouse
          DOCKERFILE: docker/Dockerfile
          GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
