name: Submit Inference SQL

on:
  push:
    branches: [main]
  pull_request:
  release:
    types: [published]

env:
  CONTAINER_REGISTRY: us-central1-docker.pkg.dev/prod-platform-29b5cb/prod-docker-registry

jobs:
  Submit-Inference-SQL:
    runs-on:
      - self-hosted
      - prod

    steps:
      - name: Install Make
        run: |
          apt-get update &&
          apt-get install -y make

      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Python Environment
        uses: Apella-Technology/setup-python-env@v2
        with:
          artifact_registry_credentials: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          poetry_version: 1.8.1
          python_version: '3.10'

      - name: Install DBT deps
        run: cd dbt &&
          poetry run dbt deps --profiles-dir config

      - name: Generate Inference SQL
        run: |
          make dbt-compile-feature-fetch
        
      - name: Upload Compiled SQL to GCS
        run: |
          # Set the source file path and destination bucket/path
          SOURCE_FILE="dbt/target/compiled/data_warehouse/models/silver/forecasting/features/forecasting_case_features_combined.sql"
          DESTINATION_BUCKET="gs://prod-data-warehouse/compiled_inference_sql"
          TIMESTAMP=$(date +%Y%m%d_%H%M%S)
          GIT_SHA=$(git rev-parse --short HEAD)
          
          # Get the release tag if it exists
          RELEASE_TAG=""
          if [[ "$GITHUB_REF" == refs/tags/* ]]; then
            RELEASE_TAG=$(echo $GITHUB_REF | cut -d/ -f3)
          fi
          
          # Verify the file exists
          if [ ! -f "$SOURCE_FILE" ]; then
            echo "Error: Compiled SQL file not found at $SOURCE_FILE"
            exit 1
          fi
          
          # Upload the file to GCS with timestamp, git SHA, and release tag for versioning
          FILENAME="forecasting_case_features_combined_${TIMESTAMP}_${GIT_SHA}"
          if [ -n "$RELEASE_TAG" ]; then
            FILENAME="${FILENAME}_${RELEASE_TAG}"
          fi
          
          echo "Uploading compiled SQL to $DESTINATION_BUCKET/${FILENAME}.sql"
          gsutil cp "$SOURCE_FILE" "$DESTINATION_BUCKET/${FILENAME}.sql"
          echo "Successfully uploaded compiled SQL to GCS"
