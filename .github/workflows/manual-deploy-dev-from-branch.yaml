name: Manual Deploy Dev From Branch

on:
  workflow_dispatch: {}

env:
  CONTAINER_REGISTRY: us-central1-docker.pkg.dev/prod-platform-29b5cb/prod-docker-registry

jobs:
  Build-Container:
    name: Manual Deploy
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Build Container
        uses: Apella-Technology/build-docker-container@v1
        with:
          CONTAINER_REPOSITORY: data-warehouse
          DOCKERFILE: docker/Dockerfile
          GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.GOOGLE_APPLICATION_CREDENTIALS }}
          # The dev deployment in ArgoCD is currently set up to track the "main" tag, which is
          # applied to images built from the main branch. To force a deployment in the case of
          # `workflow_dispatch`, we can tag this image as "main" to impersonate the branch.
          EXTRA_TAGS: main
