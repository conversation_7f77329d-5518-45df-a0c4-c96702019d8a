[tool.poetry]
name = "data-warehouse"
version = "0.0.0"
description = "<PERSON>gs<PERSON> + DBT to orchestrate jobs on BigQuery"
authors = []
readme = "README.md"

[[tool.poetry.source]]
name = "PyPI"
priority = "primary"

[[tool.poetry.source]]
name = "prod-python-registry"
url = "https://us-central1-python.pkg.dev/prod-platform-29b5cb/prod-python-registry/simple/"
priority = "supplemental"

[tool.poetry.dependencies]
python = "~3.10"
apella_cloud-api = "^22.5.4"
auth0-python = "^4.9.0"
cryptography = "^44.0.1"
dagit = "^1.3.4"
dagster = "^1.7.1"
dagster-dbt = "^0.23.1"
dagster-gcp = "^0.23.1"
dagster-gcp-pandas = "^0.23.1"
dagster-k8s = "^0.23.1"
dagster-postgres = "^0.23.1"
dbt-bigquery = "^1.5.0"
dbt-core = "1.8.4"
google-api-python-client-stubs = "^1.23.0"
google-auth-stubs = "^0.2.0"
google-cloud-bigquery = "^3.13.0"
google-cloud-secret-manager = "^2.16.3"
h11 = ">=0.16.0"
nameparser = "^1.1.3"
opsgenie-sdk = "^2.1.5"
pendulum = "2.1.2" # Locking version for dagster compatibility.
polling2 = "^0.5.0"
requests = "^2.32.2"
slack-sdk = "^3.27.0"
sqlfluff = "^3.0.0,<=3.3.0"
sqlfluff-templater-dbt = "^3.0.0,<=3.3.0"
twilio = "^8.9.0"
gitpython = "^3.1.43"
cloud-sql-python-connector = {version = "^1.2.3", extras = ["pg8000"]}
# Pin Pydantic to v1 because of this annoying typing issue with Dagster: https://github.com/dagster-io/dagster/issues/17443
# If it becomes desirable or important to move to Pydantic v2, we can sprinkle around `type:
# ignore`s to work around the issue, but we don't have a meaninfgul reason to do so now.
# See also the pydantic._internal._model_construction specified in `ignore_missing_imports`.
pydantic = "^1.10.0"
dataclasses-json = "^0.6.7"
fire = "^0.7.0"
pandera = "^0.22.0"
#multimethod = "^1.12"
encord = "^0.1.154"
# Silences errors in the Dagster UI when running it locally. Remove after you upgrade to Dagster 1.8 or higher
watchdog = "^4.0.0"

[tool.poetry.group.dev.dependencies]
ipdb = "^0.13.13"
mypy = "^1.3.0"
pytest = "^7.3.1"
ruff = "^0.4.0"
types-pytz = "^2024.1.0.20240203"
types-pyyaml = "^*********"
types-requests = "2.27.0"

[tool.dagster]
module_name = "warehouse"

[[tool.mypy.overrides]]
module = [
    "auth0.*",
    "dagster_dbt.*",
    "dagster_gcp.*",
    "pandas.*",
    "pg8000.*",
    "pydantic._internal._model_construction",
    "google.cloud.storage.client.*",
    "opsgenie_sdk.*",
    "polling2.*",
    "nameparser.*",
    "fire.*",
]
ignore_missing_imports = true

[tool.ruff]
line-length = 100

[tool.ruff.lint]
ignore = [
   # Trust ruff format to get line length right. Without this, there are cases where ruff won't
   # reflow a line that's too long (e.g. comments) and ruff complains.
   "E501"
]
# Enable pycodestyle (`E`), Pyflakes (`F`) and isort (`I001`)
select = ["E", "F", "I001"]
