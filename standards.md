# Warehouse Standards

## Datasets

Our BigQuery warehouse is split into 4 datasets:

- `bronze`
  - Upstream tables, generated from data external to the warehouse. Right now there are two main sources of external data:
    - API data replicated in realtime from Cloud SQL to BigQuery `bronze.public_*` table via Datastream
    - Historical client data ingested from files located on GCS to BigQuery `bronze.<client_name>_historical_cases` tables
- `silver`
  - Cleaned tables used as building blocks throughout the warehouse
- `gold`
  - Leaf-node tables intended for external warehouse usage (e.g., reports, analyses, model features)
- `sandbox`
  - Playground for developers to generate tables adhoc during R&D. All tables expire after 30 days

If you aren't sure where to place a new table, please reach out to #guild-data-delegates.

## Partitioning

All tables in the warehouse must have a partition column called `ds`.

This column corresponds to the scheduled run timestamp (UTC) of the table's job. Tables read from `ds` and write to `ds` partitions. When the daily Data Warehouse job runs, all `bronze` tables replicated in realtime have their data frozen as-of the `ds` timestamp throughout the duration of the daily job. This guarantees that all Data Warehouse tables are reading from the same state of `bronze` tables, and that when re-running a Data Warehouse job for a given `ds`, the same exact data is processed.

In this sense, `ds` represents the "system time" of the data warehouse - i.e., querying all tables for a given `ds` gives us a consistent, reproduceable state of the data warehouse as-of that given `ds`.

The partition has important utility that provides the following benefits:

- Establish a convention and pattern for how data is selected and loaded into warehouse tables
- Provide data lineage & provenance between derived tables and their upstream dependencies
- Allows us to see what the warehouse looked like at a certain point in time
- Facilitates comparing snapshots of warehouse data to understand software and data bugs

Please see the DBT [README](./dbt/README.md) for how to add `ds` to your table.

## Table Names

Table names should conform to the following formats.

Please note: due to legacy development (before these standards were published), not all tables in the warehouse currently conform to these conventions. This is an ongoing effort to refactor as we get time to get everything in the right shape.

### Core Tables

Core tables hold Apella entities or relationships, that are cleaned and ready to be consumed across all projects. They are not project specific and should be the upstream to every pipeline in the warehouse.

#### Format

> `core_<entity | entity_to_entity>`


#### Examples

> `core_cases`

> `core_case_phases`

### Project Tables

These are tables that have project-specific logic. Cross-team usage of these tables requires that the author understands the nuances of the table.

#### Format

The table naming convention is intentionally generic, but users should opt for a concise name that describe the entities or relationships in the table.

> `<project | team prefix>_<entity | entity_to_entity>`


#### Prefix

`<project | team prefix>` should correspond to the project or team this table is catered to.

#### Examples

> `forecasting_cases`

Cases required for the forecasting pipelines. This table might need custom transformations on top of `core_cases` that are required for training our forecasting ML models.
