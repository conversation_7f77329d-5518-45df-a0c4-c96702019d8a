SHELL := /bin/bash

lower = $(shell echo $(1) | tr A-Z a-z)
sandbox-arg := $(call lower, $(sandbox))

define validate-params
	@if [[ "$(select)" = "" ]]; then \
		echo "No 'select' specified!"; \
		exit 1; \
	elif [[ "$(ds)" = "" ]]; then \
		echo "No 'ds' specified."; \
		exit 1; \
	elif ! echo "$(ds)" | grep -Eq '^[0-9]{4}-[0-9]{2}-[0-9]{2}\ [0-9]{2}:[0-9]{2}:[0-9]{2}$$'; then \
		echo "'ds' must match the pattern 'YYYY-MM-DD HH:MM:SS'."; exit 1; \
	fi
endef

define GET_CHANGED_SQL_VS_ORIGIN_HEAD
	git diff --name-only --diff-filter=AM origin/HEAD...HEAD -- dbt/models dbt/tests \
		| grep '\.sql$$' || true \
		| tr '\n' ' ' \
		| sed 's/ $$//'
endef

run-dev: export WAREHOUSE_ENVIRONMENT=dev
run-dev: export WAREHOUSE_RUNTIME_ENVIRONMENT=local
run-dev:
		cd dbt && \
		poetry run dbt compile \
			-t dev \
			--vars '{ds: "____-__-__ __:__:__"}' \
			--profiles-dir config && \
		cd ../ && \
		poetry run python -m scripts.run_dev

pytest_args ?= tests
run-tests: export WAREHOUSE_ENVIRONMENT=dev
run-tests: export WAREHOUSE_RUNTIME_ENVIRONMENT=local
run-tests:
		cd dbt && \
		poetry run dbt compile \
			-t dev \
			--vars '{ds: "____-__-__ __:__:__"}' \
			--profiles-dir config && \
		cd ../ && \
		poetry run pytest $(pytest_args)


dbt-cli: # todo: cut all this over to a Python script
		$(call validate-params)
		if [ "$(dbt-command)" = "run" ] && [ "$(sandbox-arg)" != "" ]; then \
			echo "The 'sandbox' argument should only be provided when running DBT data tests"; \
			exit 1; \
		elif [ "$(dbt-command)" = "test" ] && [ "$(sandbox-arg)" != "false" ] && [ "$(sandbox-arg)" != "true" ]; then \
			echo "When running data tests locally, 'sandbox' must be set to 'true' or 'false'"; \
			exit 1; \
		fi \

		export DBT_MACRO_DEBUGGING=1 && \
		cd dbt && \
		poetry run dbt $(dbt-command) \
			--profiles-dir config \
			--vars '{ ds: "$(ds)", upstream_prod_enabled: $(call lower, $(upstream-prod-enabled)), custom_model_prefix: $(if $(custom_model_prefix),$(custom_model_prefix),""), dbt_data_test_against_sandbox: $(if $(filter $(dbt-command),test),$(sandbox-arg),false), sandbox_models_for_test: $(if $(filter $(dbt-command),test),"$(sandbox_models_for_test)", "") }' \
			--select "$(select)" \
			--threads $(if $(threads),$(threads),1) \
			-t $(call lower, $(environment))

dbt-compile-feature-fetch:
		export WAREHOUSE_ENVIRONMENT=prod && \
		export WAREHOUSE_RUNTIME_ENVIRONMENT=local && \
		cd dbt && \
		poetry run dbt compile \
			--profiles-dir config \
			--vars '{ds: "____-__-__ __:__:__", upstream_prod_enabled: false, for_feature_fetch: true}' \
			-t $$WAREHOUSE_ENVIRONMENT && \
		cd ../ && \
		poetry run python -m warehouse.scripts.test_compile_for_feature_fetch

dbt-compile-prod:
		make dbt-cli \
			dbt-command=compile \
			ds="$(ds)" \
			upstream-prod-enabled=true \
			select="$(select)" \
			environment=prod

dbt-run-prod:
		make dbt-cli \
			dbt-command=run \
			ds="$(ds)" \
			upstream-prod-enabled=true \
			select="$(select)" \
			environment=prod

dbt-compile-dev:
		make dbt-cli \
			dbt-command=compile \
			ds="$(ds)" \
			upstream-prod-enabled=true \
			select="$(select)" \
			environment=dev

dbt-run-dev:
		make dbt-cli \
			dbt-command=run \
			ds="$(ds)" \
			upstream-prod-enabled=true \
			select="$(select)" \
			environment=dev

dbt-test-dev:
		make dbt-cli \
			dbt-command=test \
			ds="$(ds)" \
			upstream-prod-enabled=false \
			select="$(select)" \
			environment=dev \
			sandbox=$(sandbox)

dbt-test-prod:
		make dbt-cli \
			dbt-command=test \
			ds="$(ds)" \
			upstream-prod-enabled=false \
			select="$(select)" \
			environment=prod \
			sandbox=$(sandbox)

# temporary - will remove this when all make commands are cut over to a Python script
dbt-run-prod-for-ci:
		make dbt-cli \
			dbt-command=run \
			ds="$(ds)" \
			upstream-prod-enabled=true \
			select="$(select)" \
			environment=prod \
			custom_model_prefix="$(custom_model_prefix)" \
			threads=$(threads)


dbt-test-prod-for-ci:
		make dbt-cli \
			dbt-command=test \
			ds="$(ds)" \
			upstream-prod-enabled=false \
			select="$(select)" \
			environment=prod \
			sandbox=false \
			sandbox_models_for_test="$(sandbox_models_for_test)" \
			custom_model_prefix="$(custom_model_prefix)"

.PHONY: dbt-generate-docs
dbt-generate-docs:
	@cd dbt && \
	poetry run dbt docs generate \
		--profiles-dir config \
		--vars '{ds: "____-__-__ __:__:__", upstream_prod_enabled: false, for_feature_fetch: true}' \
		-t prod

.PHONY: dbt-serve-docs
dbt-serve-docs: dbt-generate-docs
	@cd dbt && \
	poetry run dbt docs serve \
		--port 8001 \
		--profiles-dir config


ruff-format:
		poetry run ruff format .
		poetry run ruff check --fix .

ruff-lint:
		poetry run ruff format --check .
		poetry run ruff check .

mypy-lint:
		poetry run mypy .

sqlfluff-args = --dialect bigquery -v

sqlfluff-format:
		poetry run sqlfluff fix dbt/{models,tests} $(sqlfluff-args) -f --show-lint-violations

sqlfluff-lint:
		poetry run sqlfluff lint dbt/{models,tests} $(sqlfluff-args)

.PHONY: sqlfluff-diff-format sqlfluff-diff-lint
sqlfluff-diff-format:
		@CHANGED_SQL_FILES_LIST=$$($(GET_CHANGED_SQL_VS_ORIGIN_HEAD)); \
		if [ -n "$$CHANGED_SQL_FILES_LIST" ]; then \
			echo "Formatting changed SQL files: $$CHANGED_SQL_FILES_LIST"; \
			poetry run sqlfluff fix $$CHANGED_SQL_FILES_LIST $(sqlfluff-args) -f --show-lint-violations; \
		else \
			echo "No changed SQL files in dbt/models or dbt/tests to format (compared to origin/HEAD)."; \
		fi

sqlfluff-diff-lint:
		@CHANGED_SQL_FILES_LIST=$$($(GET_CHANGED_SQL_VS_ORIGIN_HEAD)); \
		if [ -n "$$CHANGED_SQL_FILES_LIST" ]; then \
			echo "Linting changed SQL files: $$CHANGED_SQL_FILES_LIST"; \
			poetry run sqlfluff lint $$CHANGED_SQL_FILES_LIST $(sqlfluff-args); \
		else \
			echo "No changed SQL files in dbt/models or dbt/tests to lint (compared to origin/HEAD)."; \
		fi

format: ruff-format sqlfluff-format

lint: mypy-lint ruff-lint sqlfluff-lint

sqlfluff-parse-model:
		poetry run sqlfluff parse dbt/models/$(model) $(sqlfluff-args)

sqlfluff-lint-model:
		poetry run sqlfluff lint dbt/models/$(model) $(sqlfluff-args)

sqlfluff-format-model:
		poetry run sqlfluff fix dbt/models/$(model) $(sqlfluff-args) --show-lint-violations

install:
		poetry install --no-root
		cd dbt && poetry run dbt deps --profiles-dir config

dagster-materialize-assets-prod: export WAREHOUSE_ENVIRONMENT=prod
dagster-materialize-assets-prod: export WAREHOUSE_RUNTIME_ENVIRONMENT=local
dagster-materialize-assets-prod:
		$(call validate-params)
		poetry run dagster asset materialize -m warehouse --select "$(select)" --partition "$(ds)"

dagster-materialize-assets-dev: export WAREHOUSE_ENVIRONMENT=dev
dagster-materialize-assets-dev: export WAREHOUSE_RUNTIME_ENVIRONMENT=local
dagster-materialize-assets-dev:
		$(call validate-params)
		poetry run dagster asset materialize -m warehouse --select "$(select)" --partition "$(ds)"
