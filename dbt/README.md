# Local Development Workflow

The DBT documentation can be found [here](https://docs.getdbt.com/), but we have added some custom utilities to make your dev life easier. This guide should be totally sufficient for standard table development.

## Directory Layout

The core layout of our DBT project is pretty standard:

```
dbt/
    models/
        <bronze | silver | gold>/
            <project>/
                <table name>.sql
                schema.yml
```

The top level directory `models/<bronze | silver | gold>` automatically sets the target BigQuery dataset for every model file `<table name>.sql` in that directory. In DBT, a model's name is equivalent to the corresponding table name.

## Adding a Table

There are three steps required to add a DBT model

1. Create a model file at `models/<dataset>/<my project>/<table name>.sql`
2. Add your table schema to `models/<dataset>/<my project>/schema.yml`

### Model Requirements

There are three requirements when adding a new DBT model to the repository:

1. The model must contain a `config` defining the `ds` partition and a run cadence.
2. The model's `select` must read from tables using `ref` and / or `source`
   1. **`ref` and `source` tables partitioned on `ds` must be filtered by `ds`**
3. The model must declare `{{ ds() }} as ds` as the first column

### Example

As an example, let's say we have a model file located at `models/silver/example_project/model_2.sql`

```sql

{% set my_cool_macro = 'date_diff(current_date(), date(a.created_timestamp), day)' %}

{{ daily_config() }}

select
    {{ ds() }} as ds,
    {{ my_cool_macro }} as my_col_name,
    a.col_1
from {{ ref("model_1") }} a
inner join {{ source("bronze", "api_table_1") }} b
on a.col_1 = b.col_1
-- EXPERIMENTAL
inner join {{ api_table_snapshot("bronze", "api_table_2", "c") }}
on b.col_2 = c.col_2
where a.ds = {{ ds() }}
```

Components of the model file:

- `models/silver/example_project/model_2.sql`

  The model `model_2.sql` will generate a table called `model_2` in the `silver` dataset

- `select`

  The `select` statement will collect rows for insertion into a table called `<model_2>`

- `{{ daily_config() }}`

  This macro contains shared config logic used to create daily partitions.

  The `daily_config` defines how this table is partitioned and how often it is materialized. Currently only daily materialization is supported. By default, partitions expire after 30 days and anyone querying this table must specify a partition filter.

  In some cases expiration may need to be changed, e.g. an ROI study needs to be reproducible beyond 30 days or a table has ~1 TB of data, which can be done by updating the `expiration_days` parameter. Changes to `expiration_days` are not automatically applied to existing tables.

  In other cases, users might not want users to require a partition filter when querying their table, e.g. tables that are incremental, which can be done by setting `require_partition_filter=False`. Changes to `require_partition_filter` are not automatically applied to existing tables.

  If you need to apply an `expiration_days` or `require_partition_filter` change to an existing table or have any use case beyond what is supported in the `daily_config`, please reach out in #guild-data-delegates.

- `{{ ds() }}`

  This macro is a UTC timestamp corresponding to the job's scheduled run date from Dagster

- `{{ my_cool_macro }}`

  This macro is defined in this file and allows to simplify/reuse components. For more complex macros or macros that are reused across different sql files you can place them under `dbt/macros/` (there are some examples in that folder)

- `{{ ref("model_1") }}`

  This macro defines a reference to another DBT model, located in a file called `model_1.sql`. The `ref` DBT macro allows DBT and Dagster to understand dependencies between assets during job execution

- `{{ source("bronze", "api_table_1") }}`

  This macro references warehouse tables defined outside of DBT. This is mostly for bookkeeping so we can see the full dependency graph end to end regardless of where tables are defined and generated.

  The first argument to `source` corresponds to the configuration found in `sources.yml`. You may read from tables outside of the `[dev|prod]-data-platform` metal datasets by defining new sources.

- `{{ api_table_snapshot("bronze", "api_table_2", alias="c") }}`

  This macro uses BigQuery's Time Travel feature to query the provided API table "as-of" the job run's `ds`. This gives us idempotency when rerunning old partitions in the warehouse, to guarantee that we are querying the state of the API tables as-of the original time when that partition was scheduled to run.

### Add your table schema to `models/<dataset>/schema.yml`

See existing files for examples. You must include table and column descriptions where it's not obvious.

E.g., `case_id` does not require a description but you should put something for `is_flip_room`

## Making Changes to an Existing Table

When adding a new column, add the column to the appropriate `schema.yml` file with description if necessary.

With the exception of gold latest tables, columns are added and removed automatically on deployment, due to the on_schema_change = "sync_all_columns" configuration. See [Latest Tables - Modifying Schemas](https://github.com/Apella-Technology/data-warehouse/tree/main/dbt#modifying-schemas) when removing or changing columns for gold latest tables.

## Testing Local Changes

### Basic Worfklow

Using the `cases` model as an example,

1. Make sure the model can be compiled and executed in BigQuery by `dbt`, using `$ make dbt-run-prod select=cases ds="..."`
   - If this step fails, click the job link and fix any invalid SQL
2. Format the model using `$ make sqlfluff-format-model model=silver/forecasting/core/cases.sql`
   - This will attempt to fix any formatting issues that `sqlfluff` is able to fix
3. Lint the model using `$ make sqlfluff-lint-model model=silver/forecasting/core/cases.sql`
   - Fix any errors that come up
4. Repeat steps 2-3 until your model lints successfully
   
### Compile and Run Models, Advanced Usage

```
$ make dbt-[run|compile]-[dev|prod] [args]
```

with arguments

- `ds="%Y-%m-%d %H:%M:%S"`
- `select=<DBT model select statement>`

`compile` simply transforms your DBT model code into executable SQL, where `run` will compile AND execute the SQL into your `[dev|prod]` sandbox table.

`select` is a direct interface for DBT's `select` - see documentation [here](https://docs.getdbt.com/reference/node-selection/syntax). Most of the time you only need the following:

- `select=<model name>` runs against a single model
- `select=<model_name>+` runs the DAG from the provided model, and then all models downstream of that model
- `select=+<model_name>` runs all dependencies of the provided model, and then finally the model itself

In our repo, when running a selected model with either of these modes, the upstream data always comes from `dev` or `prod`, and data is always written to the `sandbox` dataset for each environment.

### Querying the new table

Once your table is created under `sandbox`, you will be able to see and query it in
the BigTable UI, just like any existing table.

### Example

Suppose we have a simple DAG:

```
silver.model_1 -> silver.model_2 -> silver.model_3
```

Common commands:

```
$ make dbt-run-prod ds="2023-05-18 00:00:00" select=model_2
```

1. `sandbox.model_2` is built using data from `silver.model_1`

Nothing is done with `model_3` since we have not suffixed the `select` with `+`

```
$ make dbt-run-prod ds="2023-05-18 00:00:00" select=+model_2
```

1. `sandbox.model_1` is built
2. `sandbox.model_2` is built on top of `sandbox.model_1`

```
$ make dbt-run-prod ds="2023-05-18 00:00:00" select=model_2+
```

1. `sandbox.model_2` is built on top of `silver.model_1`
2. `sandbox.model_3` is built on top of `sandbox.model_2`

## Data Tests

Data tests are easy! For now, all data tests are DBT [singular tests](https://docs.getdbt.com/docs/build/tests#singular-tests). In summary, a singular test is a SQL `select` statement, which will fail the data test if any rows are returned. In other words, the SQL statement should select "failing" rows.

All you need to do is add a SQL statement to `dbt/tests/<my_project>/test_<name_of_my_test>.sql`. If any rows are returned from that SQL statement, the test will fail.

Please make sure the file you added is in the correct folder that maps to the model folders. For example, a data test can't be in a group that has no data models. If you add a model under `silver/forecasting`, you should write a test under `tests/forecasting`.

Tests run automatically in production after the test's `ref` dependencies are materialized for a given `ds`. They are non-blocking, meaning that tests that fail do not block the rest of the asset graph from generating.

To run your test locally, simply:

```bash
$ make dbt-test-[dev|prod] ds="2023-11-12 00:00:00" select=<name_of_my_test> sandbox=[true|false]
```

When running against sandbox:

1. Generate all `ref`s in your test in `sandbox` using `make dbt-run-[dev|prod]`
2. Run `make dbt-test[dev|prod]` with `sandbox=true`

When running tests against `dev` or `prod`, simply:

1. Run `make dbt-test[dev|prod]` with `sandbox=false`

which will run against the `[bronze|silver|gold]` copy of the table.

## Latest Snapshots

You may have the warehouse automatically produce a latest snapshot of your `silver` table in `gold` by writing your daily config as

```
{{ daily_config(produce_latest_snapshot=True) }}
```

With this flag, the warehouse produces a `gold.<table_name>_latest` table, blocked by relevant dbt data tests. The default value of `produce_latest_snapshot` is `False`, which will not produce any derivative table in gold (the existing functionality).

As a graphical overview, consider the standard behavior when this flag is not set:

```
                 {{ daily_config() }}

                  ┌─────────────────┐   Runs every day, maintains daily `ds` partitions at
                  │                 │   T, T+1, T+2, etc.
                  │                 │
                  │ silver.my_table │   The oncall gets paged when these tests fail for a given `ds`,
                  │                 │   otherwise all downstream dependencies on silver.my_table
                  │                 │   will materialize as-is
                  └────────┬────────┘
                           │
                           │
                           ▼
┌───────────────┐   ┌───────────────┐   ┌────────────────┐
│               │   │               │   │                │
│  data_test_1  │   │  data_test_2  │   │   data_test_3  │
│               │   │               │   │                │
└───────────────┘   └───────────────┘   └────────────────┘
```

However, when adding `produce_latest_snapshot=True`,

```
      {{ daily_config(produce_latest_snapshot=True) }}

                   ┌─────────────────┐
                   │                 │   Same as the above, `silver.my_table` will maintain
                   │                 │   daily partitions T, T+1, T+2, etc.
                   │ silver.my_table │
                   │                 │
                   │                 │
                   └────────┬────────┘
                            │
                            │
                            ▼
┌───────────────┐   ┌───────────────┐   ┌────────────────┐
│               │   │               │   │                │
│  data_test_1  │   │  data_test_2  │   │   data_test_3  │
│               │   │               │   │                │
└───────────────┘   └───────┬───────┘   └────────────────┘
                            │
                            ▼
                  ┌──────────────────────┐
                  │                      │    When these data tests pass for a given partition T,
                  │                      │    the gold copy here is loaded with
                  │ gold.my_table_latest │       `select * from silver.my_table where ds = T`
                  │                      │
                  │                      │    When `ds = T+1` passes data checks the next day, the existing
                  └──────────────────────┘    data in `gold.my_table_latest/ds=T` will be overwritten with
                                                  `select * from silver.my_table where ds = T + 1`

```

### Modifying Schemas

Since gold latest tables are intended for external warehouse usage, it’s important that their schemas are stable for downstream users and that columns are not accidentally changed. Latest tables automatically support new column additions, but will error if deleted or renamed columns are detected. Table owners are expected to work with downstream users to confirm they are not dependent on columns that are being removed and then use [DDL](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#alter_table_drop_column_statement) to manually drop columns from the latest gold tables using the BigQuery console ([dev](https://console.cloud.google.com/bigquery?project=dev-data-platform-439b4c), [prod](https://console.cloud.google.com/bigquery?project=prod-data-platform-027529)). Note that the column should be dropped from prod after deployment, before the table is re-materialized, otherwise it will error.

### Why even do this?

#### Query Ergonomics

`ds` is the warehouse concept of "system time" - required for internal versioning, auditing, and rollbacks. However, in the vast majority of end user applications only the latest "verified" partition is needed. By only maintaining the latest partition, the end user does not need to even think about `ds` when querying these tables, let alone computing the max `ds` every time.

#### Performance

Per the above, while partition pruning in BigQuery is relatively fast, we may not always have control over the cacheing logic used in tools that may sit in between BigQuery and the end user (e.g., cacheing by table name). So instead, we keep around only the minimum amount of data that's needed.

#### Data Integrity

Eventually there can be hundreds of upstream dependencies to your final table, receiving daily commits and deployments. When exposing leaf-node warehouse data to an end user, this can be frightening.

However, since we only maintain the latest partition that passes DBT data tests, these tables are safeguarded against any gremlins that might expose bad data to the end user. If there are any bugs in the (eventually) massive upstream list of dependencies, you can sleep at night knowing that your data is verified before it goes out of the warehouse.

### Testing

When adding a new table, manually run any new tests (see Data Tests section above) and confirm that the asset graph looks as expected in Dagster via `make run-dev` before sending out the PR for review.

For now we do not support generating latest gold tables in sandbox during local development. Stay tuned for any future updates regarding this feature (tracked in DATA-1589).

## Compiling for `realtime-dags` inference

When running forecasting inference from `realtime-dags` we want to create feature vectors on the fly without duplicating all of the feature generation logic in Python.

To support this, the warehouse provides a DBT compilation mode that can generate any DBT model as a standalone SQL statement. In this mode, all upstream dependencies are represented as CTEs in a single query, instead of materializing intermediate results to tables. Important caveats:

- Historical data is not included
- User must templatize the query for a given `ds` `case_scheduled_date`

Generating the features SQL query is currently a manual process:

### Steps:

First generate every model as a standalone SQL statement

```
$ make dbt-compile-feature-fetch
```

Then from Python pull in the model that you'd like to run as an ephemeral query, and templatize accordingly before execution:

```python
fpath = "dbt/target/compiled/data_warehouse/models/silver/forecasting/features/forecasting_case_features_combined.sql"
query_str = (
    open(fpath, "r").read()
    .replace("{case_scheduled_date}", "...")
    .replace("{ds}", "...")
)
results = bigquery_client.execute(query)
```

## Maintenance

Sometimes we need to make changes to tables that are already deployed. DBT supports automatically doing this, via `--full-refresh`, however this argument is dangerous, since DBT completely recreates each table on each `dbt` invocation. This means wiping all existing partitions.

Until DBT supports modifying tables without recreating, we have some scripts to help.

## Adding a new column to a struct record in an already existing model

If you try to add a column to a struct in an already existing model, it will fail with an error like
`Casting between arrays with incompatible element types is not supported.`.  To fix this, you need
to manually update the schema in BigQuery to have the new column.

See https://cloud.google.com/bigquery/docs/managing-table-schemas#add_a_nested_column_to_a_record_column

But specifically, you run:
```
bq show --schema --format=prettyjson PROJECT_ID:DATASET.TABLE > /tmp/schema.json
```
Then, edit `/tmp/schema.json` to add the new column to the struct, and then run for sandbox:
```
bq update PROJECT_ID:DATASET.TABLE /tmp/schema.json 
```
Then, mention in your PR that you need to do this for prod, staging and dev.  Only AFTER your PR is
approved and merged, but before released, run the update for all 3 of those environments.

### Default Partition Retention

The default partition retention for DBT models is set via the script `warehouse/scripts/manage_partitions.py`

!!! DO NOT RUN THIS UNLESS YOU KNOW WHAT YOU ARE DOING !!!

To run the script, simply set the environment (`dev` is used here as an example), and invoke via

```
$ export WAREHOUSE_ENVIRONMENT="dev" && poetry run python -m warehouse.scripts.manage_partitions update-retention
```

The script will output a list of tables to modify, and confirmation that you want to do this.
