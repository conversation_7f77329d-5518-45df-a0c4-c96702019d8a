{% macro cm_calculate_valid_matches_for_time(time_to_check) %}
{% set matches_for_time_count_sql %}
    min(cases.observed_out_of_room_time_utc > cases.observed_in_room_time_utc) and min(
    phases.{{time_to_check}} is not distinct from cases.case_id
)
{% endset %}
{{ return(matches_for_time_count_sql) }}
{% endmacro %}

{% macro cm_calculate_valid_matches() %}
{% set matches_count_sql %}
        {{cm_calculate_valid_matches_for_time("current_case_id")}} as is_valid_match_now,
        {{cm_calculate_valid_matches_for_time("immediate_case_id")}} as was_valid_match_immediate,
        {{cm_calculate_valid_matches_for_time("three_minute_case_id")}} as was_valid_match_three_minutes,
        {{cm_calculate_valid_matches_for_time("five_minute_case_id")}} as was_valid_match_five_minutes
{% endset %}
{{ return(matches_count_sql) }}
{% endmacro %}