{% macro cm_phase_case_filter() %}
{% set phase_case_filter_string %}
        (
            phases.type_id = 'CASE'
            and phases.source_type = 'unified'
            and phases.status = 'VALID'
        )
{% endset %}
{{ return(phase_case_filter_string) }}
{% endmacro %}

{% macro cm_get_phase_case_id_history(time_interval, asc_desc) %}
{% set get_phase_case_id_history_query %}
        (
            select
                phases.id,
                phases.case_id
            from {{ api_table_snapshot('bronze', 'public_phases_history', 'phases') }}
            inner join {{ api_table_snapshot('bronze', 'public_events', 'events') }}
                on events.id = phases.start_event_id
            where phases.updated_time <= (events.start_time + interval {{time_interval}} minute) and phases.case_id is not null
            and {{ cm_phase_case_filter() }}
            qualify row_number() over (partition by phases.id order by phases.version {{asc_desc}}) = 1
        )
{% endset %}
{{ return(get_phase_case_id_history_query) }}
{% endmacro %}