{% macro daily_config_latest_partition() %}
    {{
        return(
            config(
                partition_by={
                    "field": "ds",
                    "data_type": "date",
                    "granularity": "day",
                },
                partitions=[ds()],
                tags=["daily"],
                materialized="table",
                incremental_strategy="default",
            )
        )
    }}
{% endmacro %}
