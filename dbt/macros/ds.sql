{% macro ds(as_date=True) %}

    {%- if for_feature_fetch() -%}
        {{- return("'{ds}'") -}}
    {%- endif -%}

    {%- if var("ds") >= '2025-01-01 00:00:00' -%}
        {%- set ds_str = var("ds") ~ ' America/Los_Angeles' -%}
    {%- else -%}
        {%- set ds_str = var("ds") ~ ' UTC' -%}
    {%- endif -%}

    {%- if as_date -%}
        {{- return("date('" ~ ds_str ~ "')") -}}
    {%- else -%}
        {{- return("'" ~ ds_str ~ "'") -}}
    {%- endif -%}

{% endmacro %}
