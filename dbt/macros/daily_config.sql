{% macro daily_config(produce_latest_snapshot=False, expiration_days=30, require_partition_filter=true) %}
    {{
        return(
            config(
                incremental_strategy='default' if for_feature_fetch() else 'insert_overwrite',
                materialized='ephemeral' if for_feature_fetch() else 'incremental',
                partition_by={
                    "field": "ds",
                    "data_type": "date",
                    "granularity": "day"
                },
                partitions=[ds()],
                tags=["daily"],
                meta={"produce_latest_snapshot": produce_latest_snapshot},
                require_partition_filter=require_partition_filter,
                partition_expiration_days=expiration_days,
            )
        )
    }}
{% endmacro %}
