{% macro generate_schema_name(custom_schema_name, node, is_upstream_ref=False) -%}
    {%- set sandbox_models_for_test = var("sandbox_models_for_test", "").split(",") -%}
    {%- set dbt_data_test_against_sandbox = var("dbt_data_test_against_sandbox", False) -%}
    {%- set upstream_prod_enabled = var("upstream_prod_enabled", False) -%}
    {%- set custom_schema_name = custom_schema_name | trim -%}

    {%- if (
        custom_schema_name is none
        or (upstream_prod_enabled and not is_upstream_ref)
        or dbt_data_test_against_sandbox
        or (node.name is not none and node.name in sandbox_models_for_test)
    ) -%}

        {{ target.schema }}

    {%- else -%}

        {{ custom_schema_name }}

    {%- endif -%}

{%- endmacro %}