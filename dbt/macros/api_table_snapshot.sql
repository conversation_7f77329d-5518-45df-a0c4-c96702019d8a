{% macro api_table_snapshot(schema_name, table_name, alias="") %}
    {% set source_table = source(schema_name, table_name) ~ " " ~ alias %}

    {%- if for_feature_fetch() -%}
        {{ return(source_table) }}

    {%- else -%}
        {%- set snapshot_sql -%}
        {{ source_table }} for system_time as of {{ ds(as_date=False) }}
        {%- endset -%}
        {{ return(snapshot_sql) }}

    {%- endif -%}
{% endmacro %}
