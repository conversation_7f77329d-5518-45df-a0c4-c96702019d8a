{% macro forecasting_historical_file_filter() %}

    {%- if target.name == "prod" -%}

        {% set file_filter -%}
source_file_gcs_path in (
    'prod-data-warehouse/historical_cases/health_first/health_first_historical_cases_2023-03-01.csv',
    'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMH_DUNN_20240308.csv',
    'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMH_DUNN_6_20240308.csv',
    'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMH_LD_20240308.csv',
    'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMH_Main_OR_20240308.csv',
    'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMH_OPC_18_20240308.csv',
    'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMH_WALTER_20240308.csv',
    'prod-data-warehouse/historical_cases/houston_methodist/houston_methodist_historical_cases_2022-09-01.csv',
    'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMCL_OR_20240308.csv',
    'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMCL_LD_20240308.csv',
    'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMCL_ASC_OR_20240308.csv',
    'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMSJ_ASU_OR_20240308.csv',
    'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMSJ_OR_20240308.csv',
    'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMSJ_LD_20240308.csv',
    'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMSL_LD_20240308.csv',
    'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMSL_MAIN_20240308.csv',
    'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMW_OR_20240308.csv',
    'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMW_LD_20240308.csv',
    'prod-data-warehouse/historical_cases/nyu/nyu_data_ingest_20111101-20240404.csv',
    'prod-data-warehouse/historical_cases/nyu/nyu_data_ingest_20240405-20241029.csv',
    'prod-data-warehouse/historical_cases/nyu/ORLdata-20230405-20241029_EAF.csv',
    'prod-data-warehouse/historical_cases/tampa_general/TGH_Historical_Surgical_Data_v3.txt',
    'prod-data-warehouse/historical_cases/lifebridge/apella_or_hist_rpt_01jul2022_31dec2022_11_25_24.csv',
    'prod-data-warehouse/historical_cases/lifebridge/apella_or_hist_rpt_01jan2023_30jun2023_NEW.csv',
    'prod-data-warehouse/historical_cases/lifebridge/apella_or_hist_rpt_01jul2023_31dec2023_NEW.csv',
    'prod-data-warehouse/historical_cases/lifebridge/apella_or_hist_rpt_01jan2024_30jun2024_NEW.csv',
    'prod-data-warehouse/historical_cases/lifebridge/apella_or_hist_rpt_01jul2024_30nov2024_NEW.csv')
        {%- endset %}

    {%- else -%}

        {% set file_filter -%}
source_file_gcs_path in ('dev-data-warehouse/historical_cases/apella/apella_historical_cases_2023-03-01.csv')
        {%- endset %}

    {%- endif -%}

    {{ return(file_filter) }}

{% endmacro %}