{% macro _get_room_filter(room_col, room) %}
{%- set room_filter -%}
    {{ room_col }} like '{{ room }}'
{%- endset -%}
{{ return(room_filter) }}
{% endmacro %}

{% macro forecasting_is_apella_data(date_sql, site_col, room_col) %}

{% set config = forecasting_sites_config() %}

{% set forecasting_config_str %}
    case
    {% for site_id in config.site_ids.keys() %}
        {%- set start_dates = config.site_ids[site_id].api_start_dates.items()|sort(attribute='1', reverse = True) %}
        {% for start_date, rooms in start_dates %}
            {%- set room_filters = [] %}

            {%- for room in rooms -%}
                {%- set _ = room_filters.append(_get_room_filter(room_col, room)) -%}
            {%- endfor -%}

            {%- set room_filter = room_filters|join(' or ') -%}
            when (
                {{ site_col }} = '{{ site_id }}'
                and ({{ room_filter }}) -- noqa: LT05
                and {{ date_sql }} >= extract(
                    date from parse_timestamp(
                        '%Y-%m-%d',
                        '{{ start_date }}'
                    )
                )
            ) then true
        {% endfor %}
    {% endfor %}
        else false
    end
{% endset %}
    {{ return(forecasting_config_str) }}
{% endmacro %}