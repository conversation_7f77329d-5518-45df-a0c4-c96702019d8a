{% macro generate_alias_name(custom_alias_name=none, node=none, is_upstream_ref=False) -%}
    {%- set custom_model_prefix = var("custom_model_prefix", "") -%}
    {%- set schema_name = generate_schema_name("__not_sandbox__", node, is_upstream_ref) -%}

    {%- if schema_name == "sandbox" and custom_model_prefix != "" -%}

        {{ return(custom_model_prefix ~ "_" ~ node.name) }}
    
    {%- elif custom_alias_name -%}

        {{ custom_alias_name | trim }}

    {%- else -%}

        {{ node.name }}

    {%- endif -%}

{%- endmacro %}
