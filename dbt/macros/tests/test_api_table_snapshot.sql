{% macro api_table_snapshot_test_suite() %}
    {%- if for_feature_fetch() -%}
        {% do test_api_table_snapshot__feature_fetch() %}
    {%- else -%}
        {% do test_api_table_snapshot__warehouse() %}
    {%- endif -%}
{% endmacro %}


{% macro test_api_table_snapshot__feature_fetch() %}
    {% set results = api_table_snapshot("bronze", "public_cases", "a") %}
    {% set expected = "`dev-data-platform-439b4c`.`bronze`.`public_cases` a" %}
    {{ assert_equals(expected, results) }}
{% endmacro %}


{% macro test_api_table_snapshot__warehouse() %}
    {% set results = "" ~ api_table_snapshot("bronze", "public_cases", "a") %}
    {% set expected = "" ~ "`dev-data-platform-439b4c`.`bronze`.`public_cases` a for system_time as of" ~ " " ~ "'2023-05-18 00:00:00 UTC'" %}
    {{ assert_equals(expected, results) }}
{% endmacro %}
