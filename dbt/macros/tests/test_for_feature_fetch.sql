{% macro for_feature_fetch_test_suite() %}
    {%- if var('for_feature_fetch', None)==None -%}
        {% do test_for_feature_fetch_default() %}
    {%- else -%}
        {% do test_for_feature_fetch_true() %}
    {%- endif -%}
{% endmacro %}


{% macro test_for_feature_fetch_default() %}
    {% set results = for_feature_fetch() %}
    {% set expected = False %}
    {{ assert_equals(expected, results) }}
{% endmacro %}


{% macro test_for_feature_fetch_true() %}
    {% set results = "" ~ for_feature_fetch() %}
    {% set expected = "" ~ True %}
    {{ assert_equals(expected, results) }}
{% endmacro %}
