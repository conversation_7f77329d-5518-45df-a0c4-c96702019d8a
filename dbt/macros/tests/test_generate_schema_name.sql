{% macro generate_schema_name_unit_test_suite() %}
    {%- if var('dbt_data_test_against_sandbox', False) == True -%}
        {% do test_schema_ddt__sandbox() %}
    {%- elif var('sandbox_models_for_test', none) is not none -%}
        {% do test_schema_ddt__sandbox_models_provided() %}
    {%- else -%}
        {% do test_schema_ddt__standard() %}
    {%- endif -%}
{% endmacro %}


{% macro test_schema_ddt__standard() %}
    {%- set results = "" ~ generate_schema_name('iron', dict(name="some-model")) -%}
    {%- set expected = "" ~ "iron"-%}
    {{ assert_equals(expected, results) }}

    {%- set results = "" ~ generate_schema_name('iron', dict(name="sandbox-model")) -%}
    {%- set expected = "" ~ "iron"-%}
    {{ assert_equals(expected, results) }}
{% endmacro %}

{% macro test_schema_ddt__sandbox() %}
    {%- set results = "" ~ generate_schema_name('iron', dict(name="some-model")) -%}
    {%- set expected = "" ~ "sandbox"-%}
    {{ assert_equals(expected, results) }}

    {%- set results = "" ~ generate_schema_name('iron', dict(name="sandbox-model")) -%}
    {%- set expected = "" ~ "sandbox"-%}
    {{ assert_equals(expected, results) }}
{% endmacro %}

{% macro test_schema_ddt__sandbox_models_provided() %}
    {%- set results = "" ~ generate_schema_name('iron', dict(name="sandbox-model")) -%}
    {%- set expected = "" ~ "sandbox"-%}
    {{ assert_equals(expected, results) }}

    {%- set results = "" ~ generate_schema_name('iron', dict(name="some-model")) -%}
    {%- set expected = "" ~ "iron" -%}
    {{ assert_equals(expected, results) }}
{% endmacro %}