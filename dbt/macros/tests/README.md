# What is this directory you ask?

This is a makeshift Jinja dbt Macro unit testing harness that helps sanity check macro output

Not to be confused with dbt test which are integration tests

## Running all unit tests

These unit tests run alongside all unit tests in the repo:

```
$ make run-tests
```

## Adding more tests

### Testing a new macro
1. Create a test_<macro_name>.sql file
2. Create a(several) macro(s) in that file that tests the result of running your macro
3. Create a "test suite" macro in that file that executes all the macro test scenarios in that file
4. Add a reference to the test suite macro to the apella_test_harness.sql file macro called run all unit tests