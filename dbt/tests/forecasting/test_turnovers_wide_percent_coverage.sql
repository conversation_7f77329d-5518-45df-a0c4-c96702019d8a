-- count of number of non-null timestamps per site; compare to case counts
-- expect fewer prev case ends than total cases, b/c first case doesn't have prev case
-- also, not all cases have back table open

with turnover_cts as (
    select
        org_id,
        countif(prev_case_actual_end_datetime_local is not null) as num_prev_case_ends,
        countif(next_case_actual_start_datetime_local is not null) as num_next_case_starts,
        countif(back_table_open_datetime_local is not null) as num_back_tables
    from {{ ref("turnovers_wide") }}
    where ds = {{ ds() }}
    group by org_id
),

turnover_available_date_range as (
    select
        org_id,
        min(phase_date_local) as min_turnover_date,
        max(phase_date_local) as max_turnover_date
    from {{ ref("core_turnover_phases") }}
    where
        ds = {{ ds() }}
        and phase_type_id = 'TURNOVER_OPEN'
    group by org_id
),

case_cts as (
    select
        core_cases.org_id,
        count(*) as num_cases
    from {{ ref("core_cases") }} as core_cases
    inner join turnover_available_date_range
        on
            core_cases.org_id = turnover_available_date_range.org_id
            and turnover_available_date_range.min_turnover_date <= date(core_cases.actual_start_datetime_local)
            and turnover_available_date_range.max_turnover_date >= date(core_cases.actual_start_datetime_local)
    where
        core_cases.ds = {{ ds() }}
        and core_cases.is_case_matched
    group by core_cases.org_id
),

percents as (

    select
        turnover_cts.*,
        case_cts.num_cases,
        -- expect nearly all cases to have a "next case id" in turnovers
        turnover_cts.num_next_case_starts / case_cts.num_cases as pct_cases_with_next_start,
        -- there will be no prev case for a first case, so expect less than 100% of next cases with previous case
        turnover_cts.num_prev_case_ends / turnover_cts.num_next_case_starts as pct_next_cases_with_prev_end,
        -- most cases have a back table
        turnover_cts.num_back_tables / case_cts.num_cases as pct_cases_with_back_table
    from turnover_cts
    inner join case_cts
        on turnover_cts.org_id = case_cts.org_id
)

select *
from percents
where
    num_cases = 0
    or pct_cases_with_next_start < 0.99   -- all cases should be represented in this table
    -- for pct_next_cases_with_prev_end: low threshold to allow for sites dominated by one-case-per-day
    --  eliminate small sample size sites because when a site first launches,
    -- all of its data might have only one case per day
    or (pct_next_cases_with_prev_end < 0.1 and num_cases >= 100)
    or (pct_cases_with_back_table < 0.5 and num_cases >= 100)  -- most procedures have a back table
