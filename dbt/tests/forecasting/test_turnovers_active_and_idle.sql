select *
from {{ ref("turnovers_active_and_idle") }}
where
    ds = {{ ds() }}
    and (
        -- test for when there is a back table:
        -- active + idle = total duration for all types of turnovers (open, clean, total)
        turnover_open_before_case != (active_turnover_open_before_case + idle_duration_in_turnover_open_before_case)
        or turnover_clean_after_case != (active_turnover_clean_after_case + idle_duration_in_turnover_clean_after_case)
        or turnover_before_case != (active_turnover_before_case + idle_duration_in_turnover_before_case)
        -- test for when there's no back table
        or (
            back_table_open_datetime_local is null
            -- if there is no back table, none of the following should be true
            and
            (
                turnover_open_before_case is not null
                or active_turnover_open_before_case is not null
                or idle_duration_in_turnover_open_before_case != 0
            )
        )
    )
