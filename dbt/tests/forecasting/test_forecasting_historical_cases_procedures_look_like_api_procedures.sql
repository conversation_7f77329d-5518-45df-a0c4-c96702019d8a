with api_case_procedures as (
    select distinct
        procedure_name,
        org_id
    from {{ ref("core_case_procedures") }}
    where ds = {{ ds() }}
),

historical_procedures as (
    select distinct
        primary_procedure_name as procedure_name,
        org_id
    from {{ ref("forecasting_historical_cases") }}
    where ds = {{ ds() }}
)

select
    historical_procedures.org_id,
    count(historical_procedures.procedure_name) as all_hist_procedure_name_count,
    countif(api_case_procedures.procedure_name is null) as hist_procedure_name_not_seen_api_count
from historical_procedures
left outer join api_case_procedures
    on
        historical_procedures.procedure_name = upper(api_case_procedures.procedure_name)
        and historical_procedures.org_id = api_case_procedures.org_id
group by historical_procedures.org_id
-- This should only improve over time for existing customers as more procedures come through HL7.
having hist_procedure_name_not_seen_api_count / all_hist_procedure_name_count > .8
