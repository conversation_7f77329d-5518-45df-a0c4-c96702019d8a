with historical_cases as (
    select
        source_file_gcs_path,
        count(*) as count
    from {{ ref("historical_cases") }}
    where
        {{ forecasting_historical_file_filter() }}
        and ds = {{ ds() }}
    group by source_file_gcs_path
),

forecasting_historical_cases as (
    select
        source_file_gcs_path,
        count(*) as count
    from {{ ref("forecasting_historical_cases") }}
    where ds = {{ ds() }}
    group by source_file_gcs_path
)

select
    historical_cases.source_file_gcs_path,
    historical_cases.count as historical_cases_count,
    forecasting_historical_cases.count as forecasting_historical_cases_count
from historical_cases
left outer join
    forecasting_historical_cases
    on
        historical_cases.source_file_gcs_path = forecasting_historical_cases.source_file_gcs_path
where
    forecasting_historical_cases.count = 0
    or forecasting_historical_cases.count is null
    or forecasting_historical_cases.count > historical_cases.count
