with historical_ampersand_procedures as (
    select distinct
        org_id,
        upper(primary_procedure_name) as historical_procedure_name
    from {{ ref("forecasting_historical_cases") }}
    where
        ds = {{ ds() }}
        and primary_procedure_name like '%&%'
),

hl7_orgs_with_ampersand as (
    select distinct org_id
    from {{ ref("core_case_procedures") }}
    where
        ds = {{ ds() }}
        and upper(procedure_name) like '%&%'
)

select
    hist.org_id,
    hist.historical_procedure_name
from historical_ampersand_procedures as hist
left outer join hl7_orgs_with_ampersand as hl7_orgs
    on hist.org_id = hl7_orgs.org_id
where
    hl7_orgs.org_id is null
