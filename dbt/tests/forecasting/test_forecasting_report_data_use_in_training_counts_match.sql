with historical_counts as (
    select
        (
            select count(*)
            from {{ ref("forecasting_report_data_used_in_training") }}
            where
                ds = {{ ds() }}
                and data_source = "HISTORICAL_DATA_DUMP"
        ) as count_report_cases,
        (
            select count(*)
            from
                {{ ref("forecasting_case_features_combined") }} where ds = {{ ds() }}
            and apella_data = 0
        ) as count_feature_cases
),

live_counts as (
    select
        (
            select count(*)
            from {{ ref("forecasting_report_data_used_in_training") }}
            where
                ds = {{ ds() }}
                and data_source = "APELLA_LIVE"
        ) as count_report_cases,
        (
            select count(*)
            from
                {{ ref("forecasting_case_features_combined") }} where ds = {{ ds() }}
            and apella_data = 1
        ) as count_feature_cases
)

select *
from (
    select
        count_report_cases,
        count_feature_cases,
        "historical" as count_type
    from historical_counts
    union all

    select
        count_report_cases,
        count_feature_cases,
        "apella_live" as count_type
    from live_counts
) as a
where count_report_cases != count_feature_cases
