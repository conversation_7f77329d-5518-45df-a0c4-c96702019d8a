{% set sites_config = forecasting_sites_config() %}
{% set config_sites = sites_config.site_ids.keys() %}

with recent_cases as (
    select *
    from {{ ref("cases") }}
    where ds = {{ ds() }}
)

select
    config_sites,
    count(*) as cnt
from
    unnest({{ config_sites  | list | tojson }}) as config_sites
left outer join
    recent_cases on config_sites = recent_cases.site_id
group by config_sites
having cnt = 1
