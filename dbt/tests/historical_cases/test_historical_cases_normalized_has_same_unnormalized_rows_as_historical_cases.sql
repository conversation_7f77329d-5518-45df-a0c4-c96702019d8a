with hc as (
    select *
    from {{ ref("historical_cases") }}
    where ds = {{ ds() }}
),

hc_normalized as (
    select *
    from {{ ref("historical_cases_normalized") }}
    where ds = {{ ds() }}
)

select
    hc.*,
    hc_normalized.*
from hc
full outer join hc_normalized
    on
        hc.org_id = hc_normalized.org_id
        and hc.file_case_id = hc_normalized.file_case_id
        and hc.source_file_gcs_path = hc_normalized.source_file_gcs_path
where
    hc.room is distinct from hc_normalized.customer_room_name
    or hc.site is distinct from hc_normalized.customer_site_name
    or hc.scheduled_start_datetime_local is distinct from hc_normalized.scheduled_start_datetime_local
    or hc.scheduled_end_datetime_local is distinct from hc_normalized.scheduled_end_datetime_local
    or hc.actual_start_datetime_local is distinct from hc_normalized.actual_start_datetime_local
    or hc.actual_end_datetime_local is distinct from hc_normalized.actual_end_datetime_local
    or hc.case_class is distinct from hc_normalized.case_class
    or hc.patient_class is distinct from hc_normalized.patient_class
    or hc.is_add_on is distinct from hc_normalized.is_add_on
    or hc.primary_surgeon.first_name is distinct from hc_normalized.primary_surgeon.first_name
    or hc.primary_surgeon.last_name is distinct from hc_normalized.primary_surgeon.last_name
    or hc.full_procedure_name is distinct from hc_normalized.full_procedure_name
    or hc.procedure_count is distinct from hc_normalized.procedure_count
    or hc.source_file_date_utc is distinct from hc_normalized.source_file_date_utc
    or hc.service_line is distinct from hc_normalized.service_line
    or hc.cancellation_reason is distinct from hc_normalized.cancellation_reason
