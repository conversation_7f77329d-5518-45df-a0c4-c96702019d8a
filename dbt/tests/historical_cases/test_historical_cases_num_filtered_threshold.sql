with historical_cases_with_filter as (
    select *
    from {{ ref("historical_cases") }}
    where ds = {{ ds() }}
),

historical_cases_pre_filter as (
    select *
    from {{ source("silver", "historical_cases_merged") }}
    where ds = {{ ds() }}
),

count_filtered_out_by_file as (
    select
        historical_cases_pre_filter.source_file_gcs_path,
        count(*) as num_case_ids_pre_filter,
        countif(historical_cases_with_filter.file_case_id is null) as num_case_ids_filtered_out
    from historical_cases_pre_filter
    left outer join historical_cases_with_filter
        on
            historical_cases_pre_filter.file_case_id = historical_cases_with_filter.file_case_id
            and historical_cases_pre_filter.source_file_gcs_path = historical_cases_with_filter.source_file_gcs_path
    group by historical_cases_pre_filter.source_file_gcs_path
)

select *
from count_filtered_out_by_file
where
    num_case_ids_filtered_out / num_case_ids_pre_filter > .05
    and source_file_gcs_path
    != 'prod-data-warehouse/historical_cases/houston_methodist/houston_methodist_historical_cases_2022-09-01.csv'
--file includes duplicates with other HMH csv
