with org_to_expected_procedure_standardization_rate as (
    select array<struct<key string, value float64>>[
        struct('health_first', .1),
        struct('houston_methodist', .01),
        struct('nyu', .03),
        struct('tampa_general', .02)
    ] as map
)

select
    historical_cases.org_id,
    coalesce(any_value(procedure_standardization_rate.value), 1) as expected_standardization_rate,
    count(*) as row_count,
    count(distinct historical_cases.primary_procedure_name) as distinct_primary_procedure_name_count
from {{ ref("historical_cases") }}
left outer join
    unnest((select map from org_to_expected_procedure_standardization_rate)) as procedure_standardization_rate
    on historical_cases.org_id = procedure_standardization_rate.key
where historical_cases.ds = {{ ds() }}
group by historical_cases.org_id
having (distinct_primary_procedure_name_count / row_count) > expected_standardization_rate
