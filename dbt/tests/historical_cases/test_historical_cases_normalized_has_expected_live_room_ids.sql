-- This config contains sites we should have normalized already.
{% set sites_config = forecasting_sites_config() %}
{% set config_sites = sites_config.site_ids.keys() %}

with case_rooms_live as (
    select room_id
    from {{ ref("cases") }}
    where
        ds = {{ ds() }}
        and org_id != 'north_bay'
        and site_id in ('{{ config_sites|join(", ") }}')
    group by room_id
),

historical_cases_rooms as (
    select distinct apella_room_id
    from
        {{ ref("historical_cases_normalized") }}
    where ds = {{ ds() }}
)

select case_rooms_live.*
from
    case_rooms_live
left outer join historical_cases_rooms
    on case_rooms_live.room_id = historical_cases_rooms.apella_room_id
where historical_cases_rooms.apella_room_id is null
