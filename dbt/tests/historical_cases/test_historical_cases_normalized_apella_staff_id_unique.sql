-- Matching by org_id, first_name, last_name should yield a unique apella_staff_id
with distinct_staff_ids as (
    select distinct
        org_id,
        primary_surgeon.first_name as first_name,
        primary_surgeon.last_name as last_name,
        apella_staff_id
    from {{ ref("historical_cases_normalized") }}
    where
        ds = {{ ds() }}
        and primary_surgeon_staff_id is null and apella_staff_id is not null
)

select
    org_id,
    first_name,
    last_name,
    count(*) as num_apella_staff_ids
from distinct_staff_ids
group by first_name, last_name, org_id
having count(*) > 1
