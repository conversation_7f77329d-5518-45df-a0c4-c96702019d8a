-- The number of ors we billed historically should never change
-- So taking a snapshot of 2024-06-01 we had 121 ORs recording at the time
-- This date was chosen as we had multiple site go lives so if there's any logic
-- issue in the query building this table that duplicately counts ORs we'll be able to catch it.

with snapshot as (
    select total_ors
    from {{ ref("billing_monthly_ors_recording") }}
    where
        ds = {{ ds() }}
        and month_start = date '2024-06-01'
)

select *
from snapshot
where total_ors != 121
