-- Ensure that we don't have more than a single row for any project, service, namespace, and sku
-- Ensure we have
with snapshot as (
    select *
    from {{ ref("billing_costs") }}
    where
        ds = {{ ds() }}
),

project_skus_with_multiple_costs as (
    select
        month,
        project_name,
        service_description,
        namespace,
        sku_id,
        sku_description,
        count(*) as duplicate_costs_count
    from snapshot
    group by
        month,
        project_name,
        service_description,
        namespace,
        sku_id,
        sku_description
    having duplicate_costs_count > 1
)

select duplicate_costs_count from project_skus_with_multiple_costs
