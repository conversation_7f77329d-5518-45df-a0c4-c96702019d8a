-- Currently, we generate block_times by uploading csv files we get from customers
-- Then we group them and generate blocks from those block_times.
-- We assume through our code that a block_id refers to the same business unit (the same name and
-- org_id). Here we test that, that for each name and org_id there is only one actively associated
-- block_id)
select
    b.name,
    b.org_id,
    count(distinct b.id) as cnt_distinct
from `bronze.public_block_times` as bt
inner join `bronze.public_blocks` as b
    on bt.block_id = b.id
where b.archived_time is null
group by b.name, b.org_id
having count(distinct b.id) > 1
