-- each case_id should appear only once (even if multiple surgeons from the block participate in the case).
-- the utilization goes to the block, not to the individual surgeon
-- As they start releasing blocks, it is possible that a case_id overlaps two unreleased parts
-- of the block as in
-- original block_time      ---------------------------------
-- released                         ---
-- available intervals      --------   ----------------------
-- case                           -----------
-- Not clear which interval we should assign it to, but is not critical for computing
-- utilization since utilization is computed at the block level and the block_id
-- is the same for both available intervals
select apella_case_id
from {{ ref("block_time_available_interval_to_case") }}
where ds = {{ ds() }}
group by apella_case_id
having count(*) > 1
