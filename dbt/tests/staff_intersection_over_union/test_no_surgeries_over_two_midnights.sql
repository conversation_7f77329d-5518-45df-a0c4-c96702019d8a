-- current implementation of staff_intersection_over_union assumes that we have no surgery that
-- is long enough to straddle to midnights (or more)
-- here we test that the end_date - 1 day >= start_date
-- or in the test language, we look for
-- start_date < end_date - 1 day
-- and expect to see no results
select *
from {{ ref("core_cases") }}
where
    ds = {{ ds() }}
    and date(scheduled_start_datetime_local) < date_sub(date(scheduled_end_datetime_local), interval 1 day)
