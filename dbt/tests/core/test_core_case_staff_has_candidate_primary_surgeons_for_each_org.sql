-- Meant to catch if we need to update business logic around identifying candidate primary surgeons.
with orgs_with_candidates as (
    select
        org_id,
        count(*) as count
    from {{ ref("core_case_staff") }}
    where
        ds = {{ ds() }}
        and is_candidate_primary_surgeon is true
    group by org_id
),

orgs_without_candidates as (
    select
        org_id,
        count(*) as count
    from {{ ref("core_case_staff") }}
    where
        ds = {{ ds() }}
        and is_candidate_primary_surgeon is not true
    group by org_id
)

select orgs_without_candidates.*
from orgs_without_candidates
left outer join orgs_with_candidates
    on orgs_without_candidates.org_id = orgs_with_candidates.org_id
where orgs_with_candidates.count is null
