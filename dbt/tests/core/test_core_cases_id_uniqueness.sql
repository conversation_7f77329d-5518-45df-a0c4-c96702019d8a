select * from (

    select
        org_id,
        apella_case_id as id,
        'apella_case_id' as id_type
    from {{ ref("core_cases") }}
    where ds = {{ ds() }} and apella_case_id is not null
    group by 1, 2, 3
    having count(*) > 1

    union all

    select
        org_id,
        customer_case_id as id,
        'customer_case_id' as id_type
    from {{ ref("core_cases") }}
    where ds = {{ ds() }} and customer_case_id is not null
    group by 1, 2, 3
    having count(*) > 1

    union all

    select
        org_id,
        case_phase_id as id,
        'case_phase_id' as id_type
    from {{ ref("core_cases") }}
    where ds = {{ ds() }} and case_phase_id is not null
    group by 1, 2, 3
    having count(*) > 1

)
