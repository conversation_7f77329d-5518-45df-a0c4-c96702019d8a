with cases as (
    select *
    from {{ ref("core_case_phases") }}
    where
        ds = {{ ds() }}
        and phase_type_id = "CASE"
),

turnover_phases as (
    select *
    from {{ ref("core_turnover_phases") }}
    where
        ds = {{ ds() }}
),

cases_to_having_turnover as (
    select
        cases.apella_case_id as apella_case_id,
        turnover_phases.next_apella_case_id is not null as has_at_least_one_turnover_phase
    from cases
    left outer join turnover_phases
        on cases.apella_case_id = turnover_phases.next_apella_case_id
    group by cases.apella_case_id, turnover_phases.next_apella_case_id
)

select
    count(*) as num_cases,
    countif(has_at_least_one_turnover_phase is true) as num_cases_with_turnover
from cases_to_having_turnover
having num_cases = 0 or num_cases_with_turnover / num_cases < .95
