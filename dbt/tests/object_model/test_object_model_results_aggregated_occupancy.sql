-- ensure reasonable occupancy counts
with occupancy_avgs as (
    select
        org_id,
        min(frame_time_local_minute_trunc) as min_date,
        max(frame_time_local_minute_trunc) as max_date,
        min(total_occupancy_camera_max_minute_mean) as min_occupancy_mean,
        max(total_occupancy_camera_max_minute_mean) as max_occupancy_mean,
        min(total_occupancy_camera_max_minute_max) as min_occupancy_max,
        max(total_occupancy_camera_max_minute_max) as max_occupancy_max,
        avg(total_occupancy_camera_max_minute_mean) as avg_occupancy_mean
    from {{ ref("object_model_results_aggregated") }}
    where ds = {{ ds() }}
    group by org_id
)

select *
from occupancy_avgs
-- at least some turnovers should have times of zero occupancy
-- if occupancy is too high, there may be duplication issues
where
    min_occupancy_mean != 0
    or min_occupancy_max != 0
    or max_occupancy_mean > 40
    or max_occupancy_max > 60
    or avg_occupancy_mean > 20
