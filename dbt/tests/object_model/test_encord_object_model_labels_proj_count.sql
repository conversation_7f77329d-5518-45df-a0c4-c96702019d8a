-- ensures all images where labeled by the same number of projects. If this fails, it means that
-- we messed up the filtering of the projects in the encord_labels_filtered model, or the queries
-- are incorrect
with array_lengths as (
    select distinct array_length(project_hashes) as len
    from {{ ref("encord_object_model_labels") }}
    where ds = {{ ds() }}
)

select 1 from array_lengths group by len having count(*) > 1
