-- Each task that has an annotation_finish_time must have at one point had a status of
-- 'DONE', 'CANCELLED', or 'READY_FOR_REVIEW'. 
with annotation_task_throughput as (
    select *
    from {{ ref("annotation_task_throughput") }}
    where
        ds = {{ ds() }}
        and annotation_finish_time is not null
),

annotation_tasks_history as (
    select *
    from {{ api_table_snapshot("bronze", "public_annotation_tasks_history") }}
)

select annotation_task_throughput.annotation_task_id
from annotation_task_throughput
inner join annotation_tasks_history
    on annotation_task_throughput.annotation_task_id = annotation_tasks_history.id
group by annotation_task_throughput.annotation_task_id
having countif(annotation_tasks_history.status in ('DONE', 'CANCELLED', 'READY_FOR_REVIEW')) = 0
