-- an error in case matching, means we have a phase that was associated with more than one case_id.
-- This is maybe silly, but just tests the opposite of what we did in the query to define errors.
-- In that query, se selecting `having count(distinct case_id) > 1`
select errors.id
from {{ ref("phases_history_with_case_matching_errors") }} as errors
where ds = {{ ds() }}
group by errors.id
having count(distinct errors.case_id) = 1
