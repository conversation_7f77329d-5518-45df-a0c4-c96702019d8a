select cm_cases_with_obx_times.case_id
from {{ ref('cm_cases_with_obx_times') }}
inner join {{ api_table_snapshot('bronze', 'public_observations', 'observations') }}
    on
        cm_cases_with_obx_times.case_id = observations.case_id
where
    cm_cases_with_obx_times.ds = {{ ds() }}
    and (
        (
            cm_cases_with_obx_times.observed_in_room_time_utc is null
            and observations.observation_time is not null
            and observations.type_id = 'OBSERVED_IN_ROOM'
        )
        or (
            cm_cases_with_obx_times.observed_out_of_room_time_utc is null
            and observations.observation_time is not null
            and observations.type_id = 'OBSERVED_OUT_OF_ROOM'
        )
    )
