with ground_truth_events as (
    -- For the ground truth, we will be fetching just the latest version of all events that the
    -- annotators have created.  We can easily get by querying the public_events table for
    -- non-deleted human_gt events
    select
        id,
        version
    from {{ api_table_snapshot("bronze", "public_events") }}
    where
        source_type = 'human_gt'
        and deleted_at is null
)

select matched_events.ground_truth_id as matched_id
from {{ ref("event_model_all_prediction_versions_matched") }} as matched_events
full outer join ground_truth_events
    on
        matched_events.ground_truth_id = ground_truth_events.id
where
    matched_events.ds = {{ ds() }}
    and matched_events.environment = 'prod'
    and (
        matched_events.ground_truth_id is not null and ground_truth_events.id is null
        or matched_events.ground_truth_id is null and ground_truth_events.id is not null
    )
