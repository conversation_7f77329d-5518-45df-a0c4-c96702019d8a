with prediction_events as (
    select
        *,
        'notification_event' as slo_category
    from {{ ref("event_model_notification_events") }}
    where ds = {{ ds() }}
    union all
    select
        *,
        'schedule_event' as slo_category
    from {{ ref("event_model_schedule_events") }}
    where ds = {{ ds() }}
)

select matched_events.prediction_id as matched_id
from {{ ref("event_model_all_prediction_versions_matched") }} as matched_events
full outer join prediction_events
    on
        matched_events.prediction_id = prediction_events.id
        and matched_events.slo_category = prediction_events.slo_category
        and matched_events.environment = prediction_events.environment
where
    matched_events.ds = {{ ds() }}
    and (
        matched_events.prediction_id is not null and prediction_events.id is null
        or matched_events.prediction_id is null and prediction_events.id is not null
    )
