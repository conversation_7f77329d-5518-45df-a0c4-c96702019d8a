-- This checks to validate that phases that oscillate between two cases are not
-- a significant portion of the total number of phases. If this becomes a significant
-- portion of the total number of phases, it may be a sign that the query is wrong
-- or that case matching is not working as expected.
with discontiguous_phases as (
    select distinct phases.id as id
    from {{ api_table_snapshot('bronze', 'public_phases_history', 'phases') }}
    inner join {{ ref("notifications_event_signals") }} as notifications_event_signals
        on
            phases.id = notifications_event_signals.phase_id
            and phases.updated_time > notifications_event_signals.min_valid_time
            and phases.updated_time < notifications_event_signals.max_valid_time
    where
        phases.case_id != notifications_event_signals.case_id
        and notifications_event_signals.ds = {{ ds() }}
)

select notifications_event_signals.ds
from {{ ref("notifications_event_signals") }} as notifications_event_signals
left outer join discontiguous_phases
    on
        notifications_event_signals.phase_id = discontiguous_phases.id
where notifications_event_signals.ds = {{ ds() }}
group by notifications_event_signals.ds
having count(distinct discontiguous_phases.id) / count(distinct notifications_event_signals.phase_id) > .01
