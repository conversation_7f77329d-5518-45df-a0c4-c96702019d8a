# Name your project! Project names should contain only lowercase characters
# and underscores. A good package name should reflect your organization's
# name or the intended use of these models
name: "data_warehouse"
version: "1.0.0"
config-version: 2

# This setting configures which "profile" dbt uses for this project.
profile: "data_warehouse"

# These configurations specify where dbt should look for different types of files.
# The `model-paths` config, for example, states that models in this project can be
# found in the "models/" directory. You probably won't need to change these!
model-paths: ["models"]
analysis-paths: ["analyses"]
test-paths: ["tests"]
macro-paths: ["macros"]
snapshot-paths: ["snapshots"]

target-path: "target" # directory which will store compiled SQL files
clean-targets: # directories to be removed by `dbt clean`
  - "target"
  - "dbt_packages"

models:
  +on_schema_change: "sync_all_columns"

  data_warehouse:
    bronze:
      +schema: "bronze"

    silver:
      +schema: "silver"

    gold:
      +schema: "gold"

    sandbox:
      +schema: "sandbox"

    +persist_docs:
      relation: "{{ var('enable_persist_docs_to_bq', false) }}"
      columns: "{{ var('enable_persist_docs_to_bq', false) }}"

vars:
  upstream_prod_fallback: False
  upstream_prod_env_schemas: True
  dbt_data_test_against_sandbox: False