version: 1

sources:
  - name: case_forecasting
    schema: case_forecasting
    tables:
      - name: forecasts
      - name: case_forecast_info

  - name: bronze
    schema: bronze
    tables:
      - name: amplitude_events_export_daily
      - name: analytics_alembic_version
      - name: analytics_cases
      - name: analytics_turnovers
      - name: auth0_org_to_user_roles
      - name: auth0_orgs
      - name: auth0_users
      - name: billing_google_sheets_gcp_project_category
      - name: billing_google_sheets_gcp_services_category
      - name: billing_google_sheets_kubernetes_namespaces
      - name: billing_google_sheets_site_start_dates
      - name: billing_google_sheets_gcp_project_service_ownership
      - name: cs_google_sheets_users_to_org_titles
      - name: encord_labels_daily
      - name: public_alembic_version
      - name: public_anesthesias
      - name: public_annotation_task_schedules
      - name: public_annotation_task_schedules_sites
      - name: public_annotation_task_types
      - name: public_annotation_task_types_history
      - name: public_annotation_tasks
      - name: public_annotation_tasks_history
      - name: public_block_surgeons
      - name: public_block_time_releases
      - name: public_block_times
      - name: public_cameras
      - name: public_case_classification_types
      - name: public_case_derived_properties
      - name: public_case_matching_status_reason
      - name: public_case_procedures
      - name: public_case_raw
      - name: public_case_staff
      - name: public_cases
      - name: public_cases_history
      - name: public_event_types
      - name: public_events
      - name: public_events_history
      - name: public_highlight_assets
      - name: public_highlight_feedback
      - name: public_highlight_users
      - name: public_highlights
      - name: public_identifier_mapping
      - name: public_media_assets
      - name: public_observation_types
      - name: public_observations
      - name: public_organizations
      - name: public_phase_relationships
      - name: public_phase_types
      - name: public_phases
      - name: public_phases_history
      - name: public_procedures
      - name: public_room_default_camera
      - name: public_room_prime_time_configs
      - name: public_rooms
      - name: public_service_lines
      - name: public_site_prime_time_configs
      - name: public_sites
      - name: public_staff
      - name: public_staff_codes
      - name: public_staff_event_notification
      - name: public_staff_event_notification_contact_information
      - name: public_staff_role
      - name: public_staffing_needs_ratio

  - name: silver
    schema: silver
    tables:
      - name: historical_cases_merged
      - name: encord_labels_filtered
      - name: billing_monthly_ors_recording

  - name: ml_project
    database: "{{ 'prod-ml-2fc132' if target.name == 'prod' else 'dev-ml-794354' }}"
    schema: '{{ target.name }}_realtime'
    tables:
      - name: event_prediction_event_updates
      - name: event_prediction_event_updates-shadow
      - name: image_embeddings
      - name: image_processing_output

  - name: ml_analytics
    database: "{{ 'prod-ml-2fc132' if target.name == 'prod' else 'dev-ml-794354' }}"
    schema: '{{ target.name }}_ml_analytics'
    tables:
      - name: block_release_cross_validation_samples

  - name: ml_cv_dataset
    database: "{{ 'prod-ml-2fc132' if target.name == 'prod' else 'dev-ml-794354' }}"
    schema: '{{ target.name }}_ml_cv_dataset'
    tables:
      - name: model_precalculated_bins

  - name: all_billing_data
    database: "{{ 'prod-internal-c5ac6b' if target.name == 'prod' else 'dev-internal-b2aa9f' }}"
    schema: 'all_billing_data'
    tables:
      - name: gcp_billing_export_v1_015CCA_A7978F_DE857E
      - name: gcp_billing_export_resource_v1_015CCA_A7978F_DE857E

  - name: annotation_needs
    database: "{{ 'prod-data-platform-027529' if target.name == 'prod' else 'dev-data-platform-439b4c' }}"
    schema: annotation_needs
    tables:
      - name: annotation_needs_run_info
      - name: annotation_needs_history
