{{ daily_config() }}

-- schedule gaps: (we represent cases with "-" and gaps with empty space
--	  start_timestamp 						end_timestamp   (prime time and block_time)
-- 		| 										|
-- 		| |------|       |-----| |-----|		|	example 1 -> first gap = 1, intra_gap = 7 + 1, last_gap = 8
--		|				|-------------|			|   example 2 -> first gap = 15, intra_gap = 0, last_gap = 9
--		|-----------|--------------|------------|   example 3 -> first_gap = 0, intra_gap = 0, last_gap = 0
--   |-------|     |--------------------|  |------|  example 4 -> first_gap = 0, intra_gap = 5 + 2, last_gap = 0
{% set default_prime_time_start="time(7, 0, 0)" %}
{% set default_prime_time_end="time(17, 0, 0)" %}

with room_first_date as (
    select
        site_id,
        room_id,
        min(date(scheduled_start_time)) as first_date
    from {{ api_table_snapshot("bronze", "public_cases") }}
    group by site_id, room_id
),

prime_dates as (
    -- cross join all room_ids with all dates since we started getting cases for that room_id
    select
        rfd.room_id,
        s.timezone,
        case_date,
        extract(dayofweek from case_date) as dow_int
    from room_first_date as rfd, unnest(generate_date_array(first_date, current_date())) as case_date
    inner join {{ api_table_snapshot("bronze", "public_sites", "s") }}
        on rfd.site_id = s.id
),

site_attributes__prime_time_in_tall_format as (
    select *
    from {{ ref("site_attributes__prime_time_in_tall_format") }}
    where ds = {{ ds() }}
),

prime_timestamps as (
    select
        pd.room_id,
        pd.case_date,
        pd.dow_int,
        'prime_time' as interval_type,
        '' as block_id,
        '' as block_time_id,
        timestamp(
            concat(string(pd.case_date), 'T', string(coalesce(pt.start_time, {{ default_prime_time_start }}))),
            pd.timezone
        )
            as start_timestamp,
        timestamp(
            concat(string(pd.case_date), 'T', string(coalesce(pt.end_time, {{ default_prime_time_end }}))), pd.timezone
        )
            as end_timestamp
    from prime_dates as pd
    left outer join site_attributes__prime_time_in_tall_format as pt
        on
            pd.room_id = pt.room_id
),

-- TODO: In the future, match the case staff to get accurate block time intervals with their gaps
-- TODO: We need to group the blocks with multiple rooms to calculate accurate gaps for these blocks
-- We need to inner join for these cases with block_time_available_interval_to_case.sql

block_timestamps as (
    -- extract from `available_interval` the same columns that we have for prime_timestamps
    select
        room_id,
        'block_time_id' as interval_type,
        block_id,
        block_time_id,
        interval_start_time as start_timestamp,
        interval_end_time as end_timestamp,
        date(interval_start_time, timezone) as case_date,
        extract(dayofweek from datetime(interval_start_time, timezone)) as dow_int
    from {{ ref("block_time_available_interval") }}
    where ds = {{ ds() }}
),

all_timestamps as (
    select
        room_id,
        case_date,
        dow_int,
        interval_type,
        block_id,
        block_time_id,
        start_timestamp,
        end_timestamp
    from prime_timestamps
    union all
    select
        room_id,
        case_date,
        dow_int,
        interval_type,
        block_id,
        block_time_id,
        start_timestamp,
        end_timestamp
    from block_timestamps
),

-- TODO: If we keep the room IDs, we need to make sure the room is OPEN before calculating the gaps for that room

with_prev_and_next_case as (
    -- orders the cases by date and room id
    select
        t.room_id,
        t.case_date,
        t.dow_int,
        t.interval_type,
        t.block_id,
        t.block_time_id,
        t.start_timestamp,
        t.end_timestamp,
        -- the start and end times for current cases
        c.scheduled_start_time as current_start_time,
        c.scheduled_end_time as current_end_time,

        -- orders the cases for each day for that room, ascending and descending, to simplify gaps calculation
        row_number()
            over (partition by t.case_date, t.room_id, t.interval_type order by c.scheduled_start_time)
            as case_order_asc,
        row_number()
            over (partition by t.case_date, t.room_id, t.interval_type order by c.scheduled_start_time desc)
            as case_order_desc,

        -- Ideally, for the first case of the day: prev_end_time (previous case end time) would be null
        -- In order to avoid null, we need coalesce so the prev_end_time for the first case is the start timestamp
        -- for the interval

        -- example:
        --	  start_timestamp (07:30 AM) 						end_timestamp (03:00 PM)
        --		|				|-------------|			                |
        -- prev_end_time for the case above would be 07:30 AM (instead of null)

        coalesce(lag(c.scheduled_end_time)
            over (partition by t.case_date, t.room_id order by c.scheduled_start_time), t.start_timestamp)
            as prev_end_time

    from {{ api_table_snapshot("bronze", "public_cases", "c") }}
    inner join {{ api_table_snapshot("bronze", "public_sites", "s") }}
        on c.site_id = s.id
    inner join all_timestamps as t
        on c.room_id = t.room_id and t.case_date = date(c.scheduled_start_time, s.timezone)
    where not (c.scheduled_end_time <= t.start_timestamp or t.end_timestamp <= c.scheduled_start_time)
),

first_gap_calc as (
    -- this assumes we have at least one case. We'll deal with days with no cases later on
    select
        pnc.room_id,
        pnc.case_date,
        pnc.start_timestamp,
        pnc.end_timestamp,
        pnc.interval_type,
        pnc.block_id,
        pnc.block_time_id,
        -- first gap: between the start_timestamp and the first case's start_time
        greatest(timestamp_diff(pnc.current_start_time, pnc.start_timestamp, minute), 0) as first_gap_minutes

    from with_prev_and_next_case as pnc
    where case_order_asc = 1
),

intra_gap_calc as (
    -- this will only return values when case_order_asc > 1, if we have exactly one case,
    -- that whole row for the specific room_id, date will be missing from this table
    select
        pnc.room_id,
        pnc.case_date,
        pnc.start_timestamp,
        pnc.end_timestamp,
        pnc.interval_type,
        pnc.block_id,
        pnc.block_time_id,

        -- intra-gap: sum of gaps between consecutive cases (only if not first case)
        sum(
            greatest(
                timestamp_diff(pnc.current_start_time, pnc.prev_end_time, minute),
                0
            )
        ) as intra_gap_minutes
    from with_prev_and_next_case as pnc
    where case_order_asc > 1
    group by
        pnc.case_date,
        pnc.room_id,
        pnc.start_timestamp,
        pnc.end_timestamp,
        pnc.interval_type,
        pnc.block_id,
        pnc.block_time_id
),

last_gap_calc as (
    -- this assumes we have at least one case. We'll deal with days with no cases later on
    select
        pnc.room_id,
        pnc.case_date,
        pnc.start_timestamp,
        pnc.end_timestamp,
        pnc.interval_type,
        pnc.block_id,
        pnc.block_time_id,
        -- last gap: between the last case's end_time and the end_timestamp
        greatest(
            timestamp_diff(pnc.end_timestamp, pnc.current_end_time, minute),
            0
        ) as last_gap_minutes
    from with_prev_and_next_case as pnc
    where case_order_desc = 1
),

with_cases_gaps as (
    -- see the comment on intra_gap_calc, we need to make a left join since (room_id, dates, block_time_id)
    -- with exactly one case will have a first_gap and a last_gap, but those (room_id, dates, block_time_id)
    -- will not appear on the intra_gap_calc
    select distinct
        fg.case_date,
        fg.room_id,
        fg.start_timestamp,
        fg.end_timestamp,
        fg.interval_type,
        fg.block_id,
        fg.block_time_id,
        fg.first_gap_minutes,
        lg.last_gap_minutes,
        coalesce(ig.intra_gap_minutes, 0) as intra_gap_minutes
    from first_gap_calc as fg
    left outer join intra_gap_calc as ig
        on
            fg.case_date = ig.case_date
            and fg.room_id = ig.room_id
            and fg.block_time_id = ig.block_time_id
    left outer join last_gap_calc as lg
        on
            fg.case_date = lg.case_date
            and fg.room_id = lg.room_id
            and fg.block_time_id = lg.block_time_id
),

with_no_cases as (
    select
        room_id,
        case_date,
        dow_int,
        interval_type,
        block_id,
        block_time_id,
        start_timestamp,
        end_timestamp
    from all_timestamps
    except distinct
    select
        room_id,
        case_date,
        dow_int,
        interval_type,
        block_id,
        block_time_id,
        start_timestamp,
        end_timestamp
    from with_prev_and_next_case
),

schedule_gaps as (
    select
        case_date,
        room_id,
        start_timestamp,
        end_timestamp,
        interval_type,
        block_id,
        block_time_id,
        first_gap_minutes,
        intra_gap_minutes,
        last_gap_minutes
    from with_cases_gaps
    union all
    select
        case_date,
        room_id,
        start_timestamp,
        end_timestamp,
        interval_type,
        block_id,
        block_time_id,
        0 as first_gap_minutes,
        0 as intra_gap_minutes,
        timestamp_diff(end_timestamp, start_timestamp, minute) as last_gap_minutes
    from with_no_cases
)

select
    {{ ds() }} as ds,
    *
from schedule_gaps
