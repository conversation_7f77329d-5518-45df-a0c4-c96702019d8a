version: 1

models:

  - name: schedule_attributes__gaps
    description: >
      Summarize some aspects of how good the schedule was. In here, we are only focusing on gap sizes.
      Here we compute the first, total intra and last gaps (in minutes) for both prime times and block times
      Because the relationship between prime_time and blocks is murky, we compute both.
    columns:
      - name: ds
        data_type: "timestamp"
      - name: room_id
        data_type: "string"
      - name: case_date
        data_type: "date"
      - name: interval_type
        data_type: "string"
        description: either 'prime_time' or 'block_time_id'
      - name: block_id
        data_type: "string"
        description: for those intervals coming from `prime_time`, this column will be null
      - name: block_time_id
        data_type: "string"
        description: for those intervals coming from `prime_time`, this column will be null
      - name: start_timestamp
        data_type: timestamp
        description: either the the prime_time_start_timestamp or block_time_start_timestamp
      - name: end_timestamp
        data_type: timestamp
        description: either the the prime_time_end_timestamp or block_time_end_timestamp
      - name: first_gap_minutes
        data_type: int
        description: time in between `interval_start_time` and first scheduled case that overlaps the interval
      - name: intra_gap_minutes
        data_type: int
        description: sum of all intra gaps (gaps in between 2 cases) that are in between interval_start_time
          and interval_end_time
      - name: last_gap_minutes
        data_type: int
        description: time in between the last scheduled case that overlaps the interval and `interval_end_time`
