{{ daily_config() }}

with case_with_primary_surgeon_and_date as (
    -- this table just adds date so that we can lag partitioning by date on the next cte
    select
        c.apella_case_id,
        c.org_id,
        c.site_id,
        c.room_id,
        c.scheduled_start_datetime_local,
        cs.apella_staff_id,
        date(c.scheduled_start_datetime_local) as date_
    from {{ ref("core_case_staff") }} as cs
    inner join {{ ref("core_cases") }} as c
        on cs.apella_case_id = c.apella_case_id
    where
        cs.is_candidate_primary_surgeon
        and lower(c.case_classification_name) = 'elective'
        and c.ds = {{ ds() }}
        and cs.ds = {{ ds() }}
        and c.is_case_matched
),

with_prev_staff_id as (
    -- this cte, just adds the prev_apella_staff_id
    select
        apella_case_id,
        scheduled_start_datetime_local,
        org_id,
        site_id,
        room_id,
        date_,
        apella_staff_id,
        lag(apella_staff_id)
            over (partition by room_id, date_ order by scheduled_start_datetime_local)
            as prev_apella_staff_id
    from case_with_primary_surgeon_and_date
),

with_consecutive_cases as (
    -- this cte just adds sum of consecutive cases for the same staff_id, date_ and room_id
    select
        org_id,
        site_id,
        room_id,
        date_,
        apella_staff_id,
        sum(
            case
                when apella_staff_id = prev_apella_staff_id then 1
                else 0
            end
        ) as consecutive_cases
    from with_prev_staff_id
    group by org_id, site_id, room_id, date_, apella_staff_id
)

select
    -- this selects the staff_id with the most consecutive_cases
    {{ ds() }} as ds,
    org_id,
    site_id,
    room_id,
    date_,
    max_by(apella_staff_id, consecutive_cases) as apella_staff_id,
    max(consecutive_cases) as consecutive_cases
from with_consecutive_cases
group by org_id, site_id, room_id, date_
