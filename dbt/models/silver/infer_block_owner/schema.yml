version: 1

models:
  - name: infer_block_owner
    description: >
      Assuming that there is a block governing the room, make a guess as what surgeon owns it.
      * For each date and room only look at elective cases
      * Then look at how many consecutive cases each primary surgeon has (we are also guessing
      who the primary surgeon is, but this is done in a different core table)
      * the primary surgeon with the most consecutive elective cases wins and is assign block
      ownership 
      Note 1: this does not work properly in cases where we have split blocks
      Note 2: Currently this does not handle ties properly

    columns:
      - name: ds
        data_type: "timestamp"
      - name: org_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: date_
        data_type: "date"
      - name: number_of_consecutive_pairs_of_cases
        data_type: "int"
        description: "Number of pairs of elective cases, the given surgeon has in the given room and date consecutively.
         The number can be 0 if there are no consecutive pairs of cases. If the surgeon has 2 cases back to back,
         the number of consecutive cases is 1"


