{{ daily_config() }}

-- Creating a table that contains the number of ORs that were recording for a certain month
-- Any ors that came online during the month are counted towards the total for the entire month
with date_range as (
    select
        date_trunc(min(recording_start_date), month) as start_date,
        date_trunc(current_date(), month) as end_date
    from {{ source("bronze", "billing_google_sheets_site_start_dates") }}
    where ds = {{ ds() }}
),

-- Create a data set that contains the first day of the month starting at the earliest recording start date
-- and continuing to the current month
calendar as (
    select date_add(start_date, interval n month) as month_start
    from
        date_range,
        unnest(generate_array(0, date_diff(end_date, start_date, month))) as n
),

-- Calculate the number of ORs added by month
monthly_ors_recording as (
    select
        date_trunc(recording_start_date, month) as month_start,
        sum(ors) as ors_added
    from {{ source("bronze", "billing_google_sheets_site_start_dates") }}
    where ds = {{ ds() }}
    group by month_start
    order by month_start
),

-- Using the calendar created previously and the ors added per month calculate
-- the total number of ORs that were recording during a month
filled_monthly_ors_recording as (
    select
        c.month_start,
        sum(coalesce(m.ors_added, 0)) over (order by c.month_start) as total_ors
    from calendar as c
    left outer join monthly_ors_recording as m
        on c.month_start = m.month_start
)

select
    {{ ds() }} as ds,
    month_start,
    total_ors
from filled_monthly_ors_recording
