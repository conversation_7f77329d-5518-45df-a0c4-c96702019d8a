{{ daily_config(produce_latest_snapshot=True) }}

-- This takes the raw billing costs that GCP exports to BigQuery and pushes them into a format that's parsed
-- and categorized according to the values in our billing sheets
with billing_costs as (
    select
        gcp_billing.project.name as project_name,
        -- The generalized servaice incurring the charge i.e. Compute Engine, Cloud Storage, ...
        gcp_billing.service.description as service_description,
        -- As we're exporting GKE usage the namespace is the k8s namespace
        labels.value as namespace,
        gcp_billing.sku.id as sku_id,
        -- A detailed description of the charge i.e. N1 Predefined Instance core running in Americas
        gcp_billing.sku.description as sku_description,
        parse_date("%Y%m", gcp_billing.invoice.month) as month,
        sum(gcp_billing.cost) as cost
    from
        {{ api_table_snapshot(
            "all_billing_data", "gcp_billing_export_resource_v1_015CCA_A7978F_DE857E", "gcp_billing"
        ) }}
    left outer join unnest(gcp_billing.labels) as labels
        on labels.key = "k8s-namespace"
    group by
        gcp_billing.invoice.month,
        labels.value,
        gcp_billing.project.name,
        gcp_billing.service.description,
        gcp_billing.sku.description,
        gcp_billing.sku.id
),

categorized_billing_costs as (
    -- Taking the grouped billing costs use our categorizations to group costs into
    -- spend categories, teams, and features
    select
        billing_costs.month,
        billing_costs.project_name,
        billing_costs.service_description,
        billing_costs.namespace,
        billing_costs.sku_id,
        billing_costs.sku_description,
        billing_costs.cost,
        -- The kubernetes namespace category i.e. COGS, OpEx
        kubernetes_namespaces.category,
        -- The GCP project category i.e. COGS, OpEx
        gcp_project_category.category as gcp_project_category,
        -- The GCP service category i.e. COGS, OpEx
        gcp_services_category.category as gcp_service_category,
        -- The kubernetes namespace team (i.e. PS, Eng, DSML, EHR) ownership if it exists,
        -- falling back to the project service owner and finally unassigned
        coalesce(kubernetes_namespaces.team, gcp_project_service_ownership.team, "unassigned") as team,
        -- The kubernetes namespace feature (i.e. Object Detection, API Server, CICD, Block, EHR)
        -- falling back to the project service feature and finally unassigned
        coalesce(kubernetes_namespaces.feature, gcp_project_service_ownership.feature, "unassigned") as feature,
        -- If a project is categorized as OpEx the service category doesn't matter
        -- If a project is categorized as COGS then the service category is used
        -- If it's not listed then it's assumed to be a COGS
        case
            when gcp_project_category.category = "OpEx" then gcp_project_category.category
            when gcp_project_category.category = "COGS" then gcp_services_category.category
            else "COGS"
        end as project_categorization,
        -- days_in_month
        extract(day from last_day(billing_costs.month)) as days_in_month
    from billing_costs
    left outer join
        {{ source("bronze", "billing_google_sheets_gcp_project_category") }} as gcp_project_category
        on
            billing_costs.project_name = gcp_project_category.project
            and gcp_project_category.ds = {{ ds() }}
    left outer join
        {{ source("bronze", "billing_google_sheets_gcp_services_category") }} as gcp_services_category
        on
            billing_costs.service_description = gcp_services_category.service
            and gcp_services_category.ds = {{ ds() }}
    left outer join
        {{ source("bronze", "billing_google_sheets_kubernetes_namespaces") }} as kubernetes_namespaces
        on
            billing_costs.namespace = kubernetes_namespaces.namespace
            and kubernetes_namespaces.ds = {{ ds() }}
    left outer join
        {{ source("bronze", "billing_google_sheets_gcp_project_service_ownership") }} as gcp_project_service_ownership
        on
            billing_costs.project_name = gcp_project_service_ownership.project
            and billing_costs.service_description = gcp_project_service_ownership.service
            and gcp_project_service_ownership.ds = {{ ds() }}
),

-- Project Costs Formula just using a linear projection today taking
categorized_billing_costs_with_projected_cost as (
    select
        *,
        -- cost * days_in_month / days_elapsed = projected_cost
        categorized_billing_costs.cost
        * categorized_billing_costs.days_in_month
        -- days_elapsed
        -- least(days_in_month, greatest(current_date - first_day_of_billing_month, 1)
        / least(
            categorized_billing_costs.days_in_month,
            -- Prevent divide by zero
            greatest(date_diff({{ ds() }}, categorized_billing_costs.month, day), 1)
        )
            as projected_cost
    from categorized_billing_costs
)

select
    {{ ds() }} as ds,
    c.month,
    c.project_name,
    c.service_description,
    c.namespace,
    c.sku_id,
    c.sku_description,
    c.cost,
    c.projected_cost,
    c.category,
    c.team,
    c.feature,
    c.gcp_project_category,
    c.gcp_service_category,
    c.project_categorization,
    if(project_categorization = "COGS", c.cost, 0) as cost_cogs,
    if(project_categorization = "COGS", c.projected_cost, 0) as projected_cost_cogs,
    if(project_categorization = "OpEx", c.cost, 0) as cost_opex,
    if(project_categorization = "OpEx", c.projected_cost, 0) as projected_cost_opex,
    safe_divide(c.cost, monthly_ors_recording.total_ors) as cost_per_or,
    safe_divide(c.projected_cost, monthly_ors_recording.total_ors) as projected_cost_per_or,
    if(
        project_categorization = "COGS",
        safe_divide(c.cost, monthly_ors_recording.total_ors), 0
    ) as cost_per_or_cogs,
    if(
        project_categorization = "COGS",
        safe_divide(c.projected_cost, monthly_ors_recording.total_ors), 0
    ) as projected_cost_per_or_cogs,
    if(
        project_categorization = "OpEx",
        safe_divide(c.cost, monthly_ors_recording.total_ors), 0
    ) as cost_per_or_opex,
    if(
        project_categorization = "OpEx",
        safe_divide(c.projected_cost, monthly_ors_recording.total_ors), 0
    ) as projected_cost_per_or_opex,
    monthly_ors_recording.total_ors
from categorized_billing_costs_with_projected_cost as c
inner join {{ ref("billing_monthly_ors_recording") }} as monthly_ors_recording
    on
        monthly_ors_recording.month_start = c.month
where
    monthly_ors_recording.ds = {{ ds() }}
