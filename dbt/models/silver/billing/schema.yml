version: 1

models:
  - name: billing_monthly_ors_recording
    description: The total number of ORs that were recording in a given month.
    columns:
      - name: ds
        data_type: 'timestamp'
      - name: month_start
        data_type: 'date'
      - name: total_ors
        data_type: 'int'
  - name: billing_costs
    description: >
      The overall and per OR GCP costs that we incurred for a specific month by project, service, and sku, with our added 
      categorizations for type of charge, team that owns the charge, and the feature that incurred that charge.
    columns:
      - name: ds
        data_type: 'timestamp'
      - name: month
        data_type: 'date'
        description: The billing month
      - name: project_name
        data_type: 'string'
        description: The GCP project name correlated with the charges
      - name: service_description
        data_type: 'string'
        description: The GCP service name i.e. Compute Engine, Cloud Storage
      - name: namespace
        data_type: 'string'
        description: The kubernetes namespace associated with specific charges i.e. api-server, media-asset
      - name: sku_id
        data_type: 'string'
        description: The GCP sku id for the charge i.e.
      - name: sku_description
        data_type: 'string'
        description: The GCP sku description for the charge i.e.
      - name: cost
        data_type: 'float'
        description: The sum for that month for each particular project, service, namespace, sku id, and sku description combination
      - name: category
        data_type: 'string'
        description: The kubernetes namespace category i.e. COGS, OpEx
      - name: team
        data_type: 'string'
        description: >
          The Apella team that owns the area of billing, determined by the GCP project and Service owners,
          or the specific k8s namespace of ownership. The current method is not 100% foolproof as there are
          certain areas of overlap that can't be specifically defined, and are therefore assigned to one team,
          when they may be shared.
      - name: feature
        data_type: 'string'
        description: >
          The Apella feature that owns the area of billing, determined by the GCP project and Service,
          or the specific k8s namespace for the deployment. The current method is not 100% foolproof as there are
          certain areas of overlap that can't be specifically defined, and are therefore assigned to one feature,
          when they may be shared.
      - name: gcp_project_category
        data_type: 'string'
        description: The GCP project categorization i.e. COGS, OpEx
      - name: gcp_service_category
        data_type: 'string'
        description: The GCP service categorization i.e. COGS, OpEx
      - name: project_categorization
        data_type: 'string'
        description: >
          The derived categorization of the GCP project and GCP service combination.
          If a project is categorized as OpEx the service category doesn't matter,
          if a project is categorized as COGS then the service category is used
          if it's not listed then it's assumed to be a COGS
      - name: cost_cogs
        data_type: 'float'
        description: The COGS cost association for the project and service combination determined by the project_categorization
      - name: cost_opex
        data_type: 'float'
        description: The OpEx cost association for the project and service combination determined by the project_categorization
      - name: cost_per_or
        data_type: 'float'
        description: THe costs for the month divided by the number of cameras that were recording at the time
      - name: cost_per_or_cogs
        data_type: 'float'
        description: The COGS for the month divided by the number of cameras that were recording at the time
      - name: cost_per_or_opex
        data_type: 'float'
        description: The OpEx for the month divided by the number of cameras that were recording at the time
