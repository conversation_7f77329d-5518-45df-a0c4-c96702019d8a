{{ daily_config(produce_latest_snapshot=True) }}

with case_primary_surgeons as (
    select
        apella_case_id as case_id,
        array_agg(apella_staff_id) as primary_surgeon_ids
    from
        {{ ref("core_case_staff") }}
    where
        is_candidate_primary_surgeon
        and ds = {{ ds() }}
    group by case_id
),

all_turnover_and_case_phases as (
    select
        phases.type_id,
        phases.org_id,
        phases.site_id,
        phases.room_id,
        phases.start_event_id,
        phases.end_event_id,
        phases.case_id,
        phases.id,
        sites.timezone,
        case_primary_surgeons.primary_surgeon_ids,
        start_events.start_time as start_timestamp,
        end_events.start_time as end_timestamp,
        datetime(start_events.start_time, sites.timezone) as start_datetime_local,
        datetime(end_events.start_time, sites.timezone) as end_datetime_local
    from {{ api_table_snapshot("bronze", "public_phases", alias="phases") }}
    inner join
        {{ api_table_snapshot("bronze", "public_events", alias="start_events") }}
        on phases.start_event_id = start_events.id
    inner join
        {{ api_table_snapshot("bronze", "public_events", alias="end_events") }}
        on phases.end_event_id = end_events.id
    inner join
        {{ api_table_snapshot("bronze", "public_sites", alias="sites") }}
        on phases.site_id = sites.id
    left outer join
        case_primary_surgeons
        on phases.case_id = case_primary_surgeons.case_id
    where
        (
            phases.type_id = 'TURNOVER'
            or phases.type_id = 'TURNOVER_OPEN'
            or phases.type_id = 'TURNOVER_CLEAN'
            or phases.type_id = 'TURNOVER_CLEAN_V2'
            or phases.type_id = 'CASE'
        )
        and phases.status = 'VALID'
        and phases.source_type = 'unified'

        and start_events.deleted_at is null
        and end_events.deleted_at is null
),

turnover_open_phases as (
    select
        all_turnover_and_case_phases.start_timestamp as open_start_timestamp,
        all_turnover_and_case_phases.end_timestamp as open_end_timestamp,
        relationships.parent_phase_id
    from
        all_turnover_and_case_phases
    inner join {{ api_table_snapshot("bronze", "public_phase_relationships", alias="relationships") }}
        on all_turnover_and_case_phases.id = relationships.child_phase_id
    where
        all_turnover_and_case_phases.type_id = 'TURNOVER_OPEN'
),

turnover_clean_phases as (
    select
        all_turnover_and_case_phases.start_timestamp as clean_start_timestamp,
        all_turnover_and_case_phases.end_timestamp as clean_end_timestamp,
        relationships.parent_phase_id
    from
        all_turnover_and_case_phases
    inner join {{ api_table_snapshot("bronze", "public_phase_relationships", alias="relationships") }}
        on all_turnover_and_case_phases.id = relationships.child_phase_id
    where
        all_turnover_and_case_phases.type_id = 'TURNOVER_CLEAN'
),

turnover_clean_v2_phases as (
    select
        all_turnover_and_case_phases.start_timestamp as clean_start_timestamp,
        all_turnover_and_case_phases.end_timestamp as clean_end_timestamp,
        relationships.parent_phase_id
    from
        all_turnover_and_case_phases
    inner join {{ api_table_snapshot("bronze", "public_phase_relationships", alias="relationships") }}
        on all_turnover_and_case_phases.id = relationships.child_phase_id
    where
        all_turnover_and_case_phases.type_id = 'TURNOVER_CLEAN_V2'
),

turnover_phases as (
    select *
    from
        all_turnover_and_case_phases
    where
        type_id = 'TURNOVER'
),

case_phases as (
    select
        start_event_id,
        end_event_id,
        case_id,
        room_id,
        primary_surgeon_ids,
        id
    from
        all_turnover_and_case_phases
    where
        type_id = 'CASE'
)

select
    {{ ds() }} as ds,
    turnover_phases.id as phase_id,
    turnover_phases.org_id,
    turnover_phases.site_id,
    turnover_phases.room_id,
    turnover_phases.start_datetime_local,
    turnover_phases.start_timestamp,
    turnover_phases.end_datetime_local,
    extract(time from turnover_phases.start_datetime_local) as start_time_local,
    extract(time from turnover_phases.end_datetime_local) as end_time_local,
    -- Use round(seconds/60) to ensure we are rounding to the closest minute, and not just
    -- discarding the seconds.
    cast(round(timestamp_diff(
        turnover_phases.end_timestamp,
        turnover_phases.start_timestamp,
        second
    ) / 60) as int64)
        as total_duration_minutes,
    case
        when turnover_phases.start_datetime_local < datetime(timestamp '2025-04-01 00:00:00', turnover_phases.timezone)
            then
                cast(round(timestamp_diff(
                    turnover_clean_phases.clean_end_timestamp,
                    turnover_clean_phases.clean_start_timestamp,
                    second
                ) / 60) as int64)
        else
            cast(round(timestamp_diff(
                turnover_clean_v2_phases.clean_end_timestamp,
                turnover_clean_v2_phases.clean_start_timestamp,
                second
            ) / 60) as int64)
    end as clean_duration_minutes,
    cast(round(timestamp_diff(
        turnover_open_phases.open_end_timestamp,
        turnover_open_phases.open_start_timestamp,
        second
    ) / 60) as int64) as open_duration_minutes,
    prev_case_phases.case_id as prev_case_id,
    next_case_phases.case_id as next_case_id,
    (
        prev_case_phases.room_id = next_case_phases.room_id
        and not coalesce(next_case_derived_props.is_in_flip_room, false)
        and coalesce(array_length(prev_case_phases.primary_surgeon_ids), 0) != 0
        and coalesce(array_length(next_case_phases.primary_surgeon_ids), 0) != 0
        and coalesce(
            array_length(
                array(
                    select prev_surgeon_ids from unnest(prev_case_phases.primary_surgeon_ids) as prev_surgeon_ids
                    intersect distinct
                    select next_surgeon_ids from unnest(next_case_phases.primary_surgeon_ids) as next_surgeon_ids
                )
            ), 0
        ) > 0
    ) as is_same_surgeon_same_room,
    case
        when prev_case_phases.case_id is not null then concat('case:', prev_case_phases.case_id)
        when prev_case_phases.id is not null then concat('phase:', prev_case_phases.id)
    end as prev_apella_case_id,
    case
        when next_case_phases.case_id is not null then concat('case:', next_case_phases.case_id)
        when next_case_phases.id is not null then concat('phase:', next_case_phases.id)
    end as next_apella_case_id,
    extract(dayofweek from turnover_phases.start_datetime_local) as day_of_week_number,
    format_date('%A', turnover_phases.start_datetime_local) as day_of_week
from
    turnover_phases
left outer join turnover_open_phases
    on turnover_phases.id = turnover_open_phases.parent_phase_id
left outer join turnover_clean_phases
    on turnover_phases.id = turnover_clean_phases.parent_phase_id
left outer join turnover_clean_v2_phases
    on turnover_phases.id = turnover_clean_v2_phases.parent_phase_id
left outer join case_phases as prev_case_phases
    on turnover_phases.start_event_id = prev_case_phases.end_event_id
left outer join case_phases as next_case_phases
    on turnover_phases.end_event_id = next_case_phases.start_event_id
left outer join {{ api_table_snapshot("bronze", "public_case_derived_properties", "next_case_derived_props") }}
    on next_case_phases.case_id = next_case_derived_props.case_id
