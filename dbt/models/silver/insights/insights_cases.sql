{{ daily_config(produce_latest_snapshot=True) }}

with child_phase_agg_durations as (
    select
        parent_case_phase_id,
        phase_type_id,
        sum(phase_duration_minutes) as phase_duration_minutes
    from {{ ref("core_case_phases") }}
    where
        parent_case_phase_id is not null
        and ds = {{ ds() }}
    group by parent_case_phase_id, phase_type_id
),

child_phase_agg_durations_wide as (
    select *
    from child_phase_agg_durations
    pivot (
        sum(phase_duration_minutes)
        for phase_type_id in (
            'INTRA_OPERATIVE' as intra_op_duration_minutes,
            'PRE_OPERATIVE' as pre_op_duration_minutes,
            'POST_OPERATIVE' as post_op_duration_minutes,
            'ANESTHESIA_PREP' as anesthesia_prep_duration_minutes
        )
    )
),

cases as (
    select
        {{ ds() }} as ds,
        org_id,
        actual_site_id as site_id,
        actual_room_id as room_id,
        apella_case_id as case_id,
        case_phase_id,
        scheduled_duration_minutes,
        actual_duration_minutes,
        is_first_case,
        is_add_on,
        is_in_flip_room,
        customer_case_id,

        case_classification_id,
        service_line_id,
        service_line_name,
        patient_classification_id,
        status,

        timestamp(scheduled_start_datetime_local, site_timezone) as scheduled_start_timestamp,
        scheduled_start_datetime_local,
        scheduled_end_datetime_local,
        timestamp(actual_start_datetime_local, site_timezone) as actual_start_timestamp,
        actual_start_datetime_local,
        actual_end_datetime_local,
        extract(time from actual_start_datetime_local) as actual_start_time_local,
        extract(time from actual_end_datetime_local) as actual_end_time_local,
        format_date('%A', actual_start_datetime_local) as actual_day_of_week,

        -- If the case is early, put a zero in the late column. Truncate to whole minutes
        -- instead of rounding the seconds (product decision).
        greatest(timestamp_diff(
            timestamp(actual_start_datetime_local, site_timezone),
            timestamp(scheduled_start_datetime_local, site_timezone),
            minute
        ), 0)
            as late_minutes,

        actual_duration_minutes - scheduled_duration_minutes as actual_minus_scheduled_duration_minutes
    from {{ ref("core_cases") }}
    where
        ds = {{ ds() }}
        -- Filter out cases that have EHR data, but have not been observed by Apella
        -- (and thus have no corresponding phase)
        and case_phase_id is not null
)

select
    cases.*,
    child_phase_agg_durations_wide.pre_op_duration_minutes,
    child_phase_agg_durations_wide.anesthesia_prep_duration_minutes,
    child_phase_agg_durations_wide.intra_op_duration_minutes,
    child_phase_agg_durations_wide.post_op_duration_minutes,
    child_phase_agg_durations_wide.pre_op_duration_minutes
    - child_phase_agg_durations_wide.anesthesia_prep_duration_minutes
        as surgery_prep_duration_minutes
from
    cases
left outer join child_phase_agg_durations_wide
    on cases.case_phase_id = child_phase_agg_durations_wide.parent_case_phase_id
