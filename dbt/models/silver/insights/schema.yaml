version: 1

models:
  - name: insights_turnovers
    description: >
      This table contains the turnovers to be displayed in the Insights
      pages. Some turnovers will not have preceding or following cases
      if we do not have the EHR data for those, which is why core_turnovers_phases
      table cannot be used for this purpose. Additionally, we are not trying to
      re-compute the turnover phases based on wheels-out and wheels-in, since there
      are edge cases that have already been taken care of in the public_phases table.
    columns:
      - name: ds
        data_type: timestamp
      - name: phase_id
        data_type: string
        description: the id of the phase in `bronze.public_phases`
      - name: org_id
        data_type: string
      - name: site_id
        data_type: string
      - name: room_id
        data_type: string
      - name: start_datetime_local
        data_type: datetime
      - name: start_timestamp
        data_type: timestamp
      - name: end_datetime_local
        data_type: datetime
      - name: start_time_local
        data_type: time
      - name: end_time_local
        data_type: time
      - name: total_duration_minutes
        data_type: int
      - name: clean_duration_minutes
        data_type: int
      - name: open_duration_minutes
        data_type: int
      - name: prev_case_id
        data_type: string
        description: EHR case_id for the preceding case. Can be null.
      - name: next_case_id
        data_type: string
        description: EHR case_id for the following case. Can be null.
      - name: prev_apella_case_id
        data_type: string
        description: Apella's unique identifier of the case or phase that came before the turnover. Can be null.
      - name: next_apella_case_id
        data_type: string
        description: Apella's unique identifier of the case or phase that came after the turnover. Can be null.
      - name: day_of_week_number
        data_type: int
        description: the day of the week number for the date to allow for sorting.
      - name: day_of_week
        data_type: string
        description: the name of the day of the week.
      - name: is_same_surgeon_same_room
        data_type: boolean
        description: true if the preceding and following case are in the same room and have overlapping primary surgeons.

  - name: insights_cases
    description: >
      This table contains the cases to be displayed in the Insights pages.
      All of the cases are guaranteed to have a phase_id (i.e. Apella has
      observed them).
      The table includes canceled cases, which will be filtered out in the Cases
      and FCOTS pages.
      In most duration-related columns, the duration is in minutes, using the
      rounding rules. The exception is the `late_minutes` column, where we
      intentionally truncate the seconds.
    columns:
      - name: ds
        data_type: timestamp
      - name: org_id
        data_type: string
      - name: site_id
        data_type: string
      - name: room_id
        data_type: string
      - name: case_id
        data_type: string
        description: EHR id of the case. Can be null.
      - name: case_phase_id
        data_type: string
        description: the id of the phase. Guaranteed to be non-null.
      - name: scheduled_start_timestamp
        data_type: timestamp
      - name: scheduled_start_datetime_local
        data_type: datetime
      - name: scheduled_end_datetime_local
        data_type: datetime
      - name: scheduled_duration_minutes
        data_type: int
      - name: actual_start_timestamp
        data_type: timestamp
      - name: actual_start_datetime_local
        data_type: datetime
      - name: actual_end_datetime_local
        data_type: datetime
      - name: actual_start_time_local
        data_type: time
      - name: actual_duration_minutes
        data_type: int
      - name: actual_day_of_week
        data_type: string
        description: the name of the day of the week.  
      - name: actual_minus_scheduled_duration_minutes
        data_type: int
        description: the difference in minutes between actual and scheduled duration, may be negative.
      - name: late_minutes
        data_type: int
        description: the number of minutes the case started late, with seconds truncated.
      - name: actual_end_time_local
        data_type: time
      - name: is_first_case
        data_type: boolean
      - name: is_add_on
        data_type: boolean
      - name: is_in_flip_room
        data_type: boolean
      - name: customer_case_id
        data_type: string
      - name: case_classification_id
        data_type: string
      - name: service_line_id
        data_type: string
      - name: service_line_name
        data_type: string
      - name: patient_classification_id
        data_type: string
      - name: status
        data_type: string
      - name: pre_op_duration_minutes
        data_type: int  
      - name: anesthesia_prep_duration_minutes
        data_type: int
      - name: surgery_prep_duration_minutes
        data_type: int 
      - name: intra_op_duration_minutes
        data_type: int
      - name: post_op_duration_minutes  
        data_type: int

  - name: insights_latest_case_events
    description: >
      Table with the latest event of each type for each case_id. It includes both
      Apella and EHR events.

    columns:
      - name: ds
        data_type: "timestamp"
      - name: case_id
        data_type: "string"
        tests:
          - not_null
      - name: event_type_id
        data_type: "string"
      - name: event_time
        data_type: "timestamp"

  - name: insights_first_case_events
    description: >
      Table with the first event of each type for each case_id. It includes both
      Apella and EHR events.

    columns:
      - name: ds
        data_type: "timestamp"
      - name: case_id
        data_type: "string"
        tests:
          - not_null
      - name: event_type_id
        data_type: "string"
      - name: event_time
        data_type: "timestamp"
