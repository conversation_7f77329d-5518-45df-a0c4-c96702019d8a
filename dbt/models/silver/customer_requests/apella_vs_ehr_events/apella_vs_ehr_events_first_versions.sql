{{ daily_config() }}

with prediction_events as (
    -- For the predictions, we will be fetching the first version
    select
        event_type_id,
        org_id,
        site_id,
        room_id,
        id as prediction_id,
        start_time as prediction_event_time,
        updated_time as prediction_updated_time
    from {{ api_table_snapshot("bronze", "public_events_history") }}
    where
        source_type = 'prediction'
        -- select the first version
        and version = 1
),

ground_truth_events as (
    -- For the ground truth, we will be fetching just the latest version of all events that the
    -- annotators have created.  We can easily get by querying the public_events table for
    -- non-deleted human_gt events
    select
        event_type_id,
        org_id,
        site_id,
        room_id,
        id as ground_truth_id,
        start_time as ground_truth_event_time
    from {{ api_table_snapshot("bronze", "public_events") }}
    where
        source_type = 'human_gt'
        -- And don't include any that were deleted
        and deleted_at is null
),

-- And for our obx events, we fetch the first version of the obx, joined with cases to get the
-- case id and procedure name.  We pick the first version because we are trying to compare latency
-- of obx vs latency of predictions.
obx_events as (
    select
        cases.org_id,
        cases.site_id,
        cases.room_id,
        obx.observation_time as obx_event_time,
        obx.recorded_time as obx_recorded_time,
        obx.case_id as case_id,
        cases.external_case_id as external_case_id,
        -- Just get the first procedure name for simplicity
        cases.procedures[offset(0)].procedure_name as procedure_name,
        case
            when obx.type_id = 'OBSERVED_IN_ROOM' then 'patient_wheels_in'
            when obx.type_id = 'OBSERVED_OUT_OF_ROOM' then 'patient_wheels_out'
        end as event_type_id
    from {{ api_table_snapshot("bronze", "public_observations", alias='obx') }}
    inner join {{ ref("ehr_cases_with_procedures") }} as cases
        on cases.ds = {{ ds() }} and obx.case_id = cases.case_id
    where
        obx.type_id in ('OBSERVED_IN_ROOM', 'OBSERVED_OUT_OF_ROOM')
    qualify row_number() over (
        partition by obx.case_id, obx.type_id
        order by obx.recorded_time asc, obx.created_time asc
    ) = 1
),

apella_events_step_1 as (
    -- Now, we assign the predictions to the ground truth by using a matching minutes of 10.
    -- This can match multiple predictions to one ground truth, and vice versa.
    -- This gets fixed in the next CTEs
    select
        -- Ground truth specific information
        ground_truth_events.ground_truth_id,
        ground_truth_events.ground_truth_event_time,
        -- Prediction specific information
        prediction_events.prediction_id,
        prediction_events.prediction_event_time,
        prediction_events.prediction_updated_time,
        -- Context information
        coalesce(ground_truth_events.org_id, prediction_events.org_id) as org_id,
        coalesce(ground_truth_events.site_id, prediction_events.site_id) as site_id,
        coalesce(ground_truth_events.room_id, prediction_events.room_id) as room_id,
        coalesce(ground_truth_events.event_type_id, prediction_events.event_type_id) as event_type_id,
        coalesce(ground_truth_events.ground_truth_event_time, prediction_events.prediction_event_time) as event_time
    from ground_truth_events
    full outer join prediction_events on
        (
            ground_truth_events.room_id = prediction_events.room_id
            and ground_truth_events.event_type_id = prediction_events.event_type_id
            and (prediction_events.prediction_event_time - interval 30 minute)
            <= ground_truth_events.ground_truth_event_time
            and (prediction_events.prediction_event_time + interval 30 minute)
            >= ground_truth_events.ground_truth_event_time
        )
),

apella_events_step_2 as (
    -- Here we remove any duplicate ground truth by partitioning on ground truth and taking
    -- the closest prediction to the ground truth
    select * from apella_events_step_1
    qualify row_number() over (
        partition by ground_truth_id
        order by abs(timestamp_diff(prediction_event_time, ground_truth_event_time, second)) asc, prediction_id asc
    ) = 1
),

apella_events_step_3 as (
    -- Here we remove any duplicate predictions by partitioning on prediction, and taking
    -- the closest prediction to the ground truth
    select * from apella_events_step_2
    qualify row_number() over (
        partition by prediction_id
        order by abs(timestamp_diff(prediction_event_time, ground_truth_event_time, second)) asc, ground_truth_id asc
    ) = 1
),

apella_events as (
    -- Those previous steps completely remove all false positives and false negatives,
    -- and so we want to join them all back together
    select
    -- Ground truth specific information
        ground_truth_events.ground_truth_id,
        ground_truth_events.ground_truth_event_time,
        -- Prediction specific information
        prediction_events.prediction_id,
        prediction_events.prediction_event_time,
        prediction_events.prediction_updated_time,
        -- Context information
        coalesce(ground_truth_events.org_id, prediction_events.org_id) as org_id,
        coalesce(ground_truth_events.site_id, prediction_events.site_id) as site_id,
        coalesce(ground_truth_events.room_id, prediction_events.room_id) as room_id,
        coalesce(ground_truth_events.event_type_id, prediction_events.event_type_id) as event_type_id,
        coalesce(ground_truth_events.ground_truth_event_time, prediction_events.prediction_event_time) as event_time
    from apella_events_step_3 as matched_events
    full outer join prediction_events
        on (
            matched_events.prediction_id = prediction_events.prediction_id
        )
    full outer join ground_truth_events on (
        matched_events.ground_truth_id = ground_truth_events.ground_truth_id
    )
),

obx_matched_to_apella_events_step_1 as (
    -- Similarly, we match all of the OBX events to the apella events using a matching minutes of 30.
    -- This can match multiple OBX events to one apella event, and vice versa.
    -- This gets fixed in the next CTEs
    select
        -- Ground truth specific information
        apella_events.ground_truth_id,
        apella_events.ground_truth_event_time,
        -- Prediction specific information
        apella_events.prediction_event_time,
        apella_events.prediction_id,
        apella_events.prediction_updated_time,
        -- OBX specific information
        obx_events.obx_event_time,
        obx_events.obx_recorded_time,
        obx_events.procedure_name,
        obx_events.case_id,
        obx_events.external_case_id,
        -- Context information
        coalesce(apella_events.org_id, obx_events.org_id) as org_id,
        coalesce(apella_events.site_id, obx_events.site_id) as site_id,
        coalesce(apella_events.room_id, obx_events.room_id) as room_id,
        coalesce(apella_events.event_type_id, obx_events.event_type_id) as event_type_id,
        coalesce(apella_events.event_time, obx_events.obx_event_time) as event_time
    from apella_events
    full outer join obx_events on (
        apella_events.room_id = obx_events.room_id
        and apella_events.event_type_id = obx_events.event_type_id
        and (obx_events.obx_event_time - interval 30 minute) <= apella_events.event_time
        and (obx_events.obx_event_time + interval 30 minute) >= apella_events.event_time
    )
),

obx_matched_to_apella_events_step_2 as (
    -- Here we remove any duplicate OBX events by partitioning on OBX and taking
    -- the closest apella event to the OBX event
    select * from obx_matched_to_apella_events_step_1
    qualify row_number() over (
        partition by event_type_id, external_case_id
        order by abs(timestamp_diff(event_time, obx_event_time, second)) asc, ground_truth_id asc, prediction_id asc
    ) = 1
),

obx_matched_to_apella_events_step_3 as (
    -- Here we remove any duplicate apella events by partitioning on apella ids and taking
    -- the closest apella event to the OBX event
    select * from obx_matched_to_apella_events_step_2
    qualify row_number() over (
        partition by ground_truth_id, prediction_id
        order by abs(timestamp_diff(event_time, obx_event_time, second)) asc, external_case_id asc
    ) = 1
),

obx_matched_to_apella_events as (
    -- Now we combine the obx matches with all that were not matched
    select
        -- Ground truth specific information
        coalesce(matched_events.ground_truth_id, apella_events.ground_truth_id) as ground_truth_id,
        coalesce(matched_events.ground_truth_event_time, apella_events.ground_truth_event_time)
            as ground_truth_event_time,
        -- Prediction specific information
        coalesce(matched_events.prediction_event_time, apella_events.prediction_event_time) as prediction_event_time,
        coalesce(matched_events.prediction_id, apella_events.prediction_id) as prediction_id,
        coalesce(matched_events.prediction_updated_time, apella_events.prediction_updated_time)
            as prediction_updated_time,
        -- OBX specific information
        coalesce(matched_events.obx_event_time, obx_events.obx_event_time) as obx_event_time,
        coalesce(matched_events.obx_recorded_time, obx_events.obx_recorded_time) as obx_recorded_time,
        coalesce(matched_events.procedure_name, obx_events.procedure_name) as procedure_name,
        coalesce(matched_events.case_id, obx_events.case_id) as case_id,
        coalesce(matched_events.external_case_id, obx_events.external_case_id) as external_case_id,
        -- Context information
        coalesce(matched_events.org_id, apella_events.org_id, obx_events.org_id) as org_id,
        coalesce(matched_events.site_id, apella_events.site_id, obx_events.site_id) as site_id,
        coalesce(matched_events.room_id, apella_events.room_id, obx_events.room_id) as room_id,
        coalesce(matched_events.event_type_id, apella_events.event_type_id, obx_events.event_type_id) as event_type_id,
        coalesce(matched_events.event_time, apella_events.event_time, obx_events.obx_event_time) as event_time
    from obx_matched_to_apella_events_step_3 as matched_events
    full outer join obx_events
        on
            (
                matched_events.obx_event_time = obx_events.obx_event_time
                and matched_events.external_case_id = obx_events.external_case_id
                and matched_events.event_type_id = obx_events.event_type_id
                and matched_events.room_id = obx_events.room_id
            )
    full outer join apella_events on
        (
            -- These uses `event_time` primarily to match the events, which is a coalesce
            -- of `ground_truth_event_time`, `prediction_event_time` and `obx_event_time`,
            -- in that order.
            matched_events.event_time = apella_events.event_time
            and matched_events.event_type_id = apella_events.event_type_id
            and matched_events.room_id = apella_events.room_id
            -- But it is possible that there is multiple predictions at the same timestamp,
            -- So we also need to match on the prediction id
            and (
                matched_events.ground_truth_id = apella_events.ground_truth_id
                or matched_events.prediction_id = apella_events.prediction_id
            )
        )
),

-- We have to repeat this filter by annotation tasks
completed_annotation_tasks as (
    select
        tasks.type_id,
        tasks.type_version,
        tasks.room_id,
        tasks.start_time,
        tasks.end_time
    from {{ api_table_snapshot("bronze", "public_annotation_tasks", alias='tasks') }}
    where tasks.status = 'DONE'
),

task_type_history as (
    select
        id as type_id,
        version,
        event_types
    from {{ api_table_snapshot("bronze", "public_annotation_task_types_history") }}
),

completed_annotation_tasks_with_event_types as (
    select
        completed_annotation_tasks.room_id,
        completed_annotation_tasks.start_time,
        completed_annotation_tasks.end_time,
        task_type_history.event_types
    from completed_annotation_tasks
    left outer join task_type_history
        on (
            completed_annotation_tasks.type_id = task_type_history.type_id
            and completed_annotation_tasks.type_version = task_type_history.version
        )
),

events_matched_that_were_reviewed as (
    select distinct events_matched.*
    from obx_matched_to_apella_events as events_matched
    inner join completed_annotation_tasks_with_event_types as completed_tasks
        on (
            events_matched.room_id = completed_tasks.room_id
            and events_matched.event_time >= completed_tasks.start_time
            and events_matched.event_time <= completed_tasks.end_time
            and events_matched.event_type_id in unnest(json_value_array(completed_tasks.event_types))
        )
)

select
    {{ ds() }} as ds,
    org_id,
    site_id,
    room_id,
    event_type_id,
    event_time,
    ground_truth_id,
    ground_truth_event_time,
    prediction_id,
    prediction_event_time,
    prediction_updated_time,
    obx_event_time,
    obx_recorded_time,
    procedure_name,
    case_id,
    external_case_id
from events_matched_that_were_reviewed
