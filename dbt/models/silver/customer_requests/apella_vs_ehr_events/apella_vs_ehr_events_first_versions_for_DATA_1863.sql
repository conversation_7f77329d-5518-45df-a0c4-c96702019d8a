{{ daily_config() }}

with first_versions_data as (
    select *
    from {{ ref("apella_vs_ehr_events_first_versions") }}
    where
        ds = {{ ds() }}
        -- Here we apply our filter specifically for this customer request
        and site_id = 'HMH-WT03'
        and event_time >= '2022-12-01'
        and event_type_id in ('patient_wheels_in', 'patient_wheels_out', 'patient_draped')
)

select
    {{ ds() }} as ds,
    room_id,
    event_type_id,
    event_time as coalesced_event_time,
    procedure_name as procedure_name,
    external_case_id as case_id,
    obx_event_time,
    obx_recorded_time,
    prediction_event_time,
    prediction_updated_time as prediction_recorded_time,
    ground_truth_event_time
from first_versions_data
