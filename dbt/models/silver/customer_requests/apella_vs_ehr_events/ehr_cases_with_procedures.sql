{{ daily_config() }}

with cases as (
    select *
    from {{ api_table_snapshot("bronze", "public_cases") }}
),

-- We want to attach all procedures to the case row
procedures as (
    select *
    from {{ api_table_snapshot("bronze", "public_procedures") }}
),

procedures_with_case_id as (
    select
        procedures.*,
        case_procedures.case_id
    from procedures
    left outer join {{ api_table_snapshot("bronze", "public_case_procedures", alias="case_procedures") }}
        on procedures.id = case_procedures.procedure_id
),

case_ids_with_procedures as (
    select
        case_id,
        array_agg(struct(
            id as id,
            procedures_with_case_id.name as procedure_name
        )) as procedures
    from procedures_with_case_id
    group by case_id
),

cases_with_procedures as (
    select
        cases.*,
        case_ids_with_procedures.procedures
    from cases
    left outer join case_ids_with_procedures
        on cases.case_id = case_ids_with_procedures.case_id
)

select
    {{ ds() }} as ds,
    org_id,
    site_id,
    room_id,
    case_id,
    external_case_id,
    procedures
from cases_with_procedures
