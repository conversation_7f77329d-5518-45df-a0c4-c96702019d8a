{{ daily_config() }}

-- Maybe fold in the false positives

with first_versions_data as (
    select *
    from {{ ref("apella_vs_ehr_events_first_versions_for_DATA_1863") }}
    where
        ds = {{ ds() }}
),

metrics as (
    select
        event_type_id,
        count(*) as total_rows,
        countif(ground_truth_event_time is not null) as total_ground_truth,
        countif(prediction_event_time is not null) as total_predictions,
        countif(obx_event_time is not null) as total_obx,
        countif(ground_truth_event_time is null and prediction_event_time is not null) as total_apella_false_positives,
        countif(ground_truth_event_time is not null and prediction_event_time is null) as total_apella_false_negatives,
        countif(ground_truth_event_time is null and obx_event_time is not null) as total_obx_false_positives,
        countif(ground_truth_event_time is not null and obx_event_time is null) as total_obx_false_negatives,
        avg((timestamp_diff(prediction_recorded_time, ground_truth_event_time, second))) as avg_apella_latency,
        avg((timestamp_diff(obx_recorded_time, ground_truth_event_time, second))) as avg_obx_latency,
        avg(abs((timestamp_diff(ground_truth_event_time, prediction_event_time, second)))) as avg_apella_time_error,
        avg(abs((timestamp_diff(ground_truth_event_time, obx_event_time, second)))) as avg_obx_time_error,
        approx_quantiles(abs((timestamp_diff(ground_truth_event_time, prediction_event_time, second))), 100)[
            offset(50)
        ] as apella_time_error_50p,
        approx_quantiles(abs((timestamp_diff(ground_truth_event_time, prediction_event_time, second))), 100)[
            offset(95)
        ] as apella_time_error_95p,
        approx_quantiles(abs((timestamp_diff(ground_truth_event_time, prediction_recorded_time, second))), 100)[
            offset(50)
        ] as apella_latency_50p,
        approx_quantiles(abs((timestamp_diff(ground_truth_event_time, prediction_recorded_time, second))), 100)[
            offset(95)
        ] as apella_latency_95p,
        approx_quantiles(abs((timestamp_diff(ground_truth_event_time, obx_recorded_time, second))), 100)[offset(50)]
            as obx_latency_50p,
        approx_quantiles(abs((timestamp_diff(ground_truth_event_time, obx_recorded_time, second))), 100)[offset(95)]
            as obx_latency_95p,
        approx_quantiles(abs((timestamp_diff(ground_truth_event_time, obx_event_time, second))), 100)[offset(50)]
            as obx_time_error_50p,
        approx_quantiles(abs((timestamp_diff(ground_truth_event_time, obx_event_time, second))), 100)[offset(95)]
            as obx_time_error_95p
    from first_versions_data
    group by event_type_id
),

total_ground_truth as (
    -- We want to calculate how many ground truth were NOT included, due to filtering with the
    -- annotation tasks.  We could repeat this for predictions and obx, but the numbers are so
    -- similar we probably only need to look at one to get an idea of how many were removed.
    select
        event_type_id,
        count(*) as total_ground_truth_not_filtered
    from {{ api_table_snapshot("bronze", "public_events") }}
    where
        source_type = 'human_gt'
        -- And don't include any that were deleted
        and deleted_at is null
        -- Apply the same filter as in the customer request
        and site_id = 'HMH-WT03'
        and start_time >= '2022-12-01'
        and event_type_id in ('patient_wheels_in', 'patient_wheels_out', 'patient_draped')
    group by event_type_id
),

final_metrics as (
    select
        metrics.*,
        total_ground_truth.total_ground_truth_not_filtered
    from metrics
    full outer join total_ground_truth
        on metrics.event_type_id = total_ground_truth.event_type_id
)

select
    {{ ds() }} as ds,
    event_type_id,
    total_rows,
    total_ground_truth,
    total_predictions,
    total_obx,
    total_apella_false_positives,
    total_apella_false_negatives,
    total_obx_false_positives,
    total_obx_false_negatives,
    avg_apella_latency,
    avg_obx_latency,
    avg_apella_time_error,
    avg_obx_time_error,
    apella_time_error_50p,
    apella_time_error_95p,
    apella_latency_50p,
    apella_latency_95p,
    obx_latency_50p,
    obx_latency_95p,
    obx_time_error_50p,
    obx_time_error_95p,
    total_ground_truth_not_filtered
from final_metrics
