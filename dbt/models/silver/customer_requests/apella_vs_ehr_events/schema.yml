version: 1

models:
  - name: apella_vs_ehr_events_first_versions
    description: >
        This is an intermediate table that compares the first version of the predictions to the
        final version of ground truth and to first version of OBX data.
        
        We pick the first version of OBX data because this customer request was focused on latency,
        so we want compare the lowest latency predictions and lowest latency OBX data.
    columns:
      - name: ds
        data_type: 'timestamp'
      - name: org_id
        data_type: 'string'
      - name: site_id
        data_type: 'string'
      - name: room_id
        data_type: 'string'
      - name: event_type_id
        data_type: 'string'
      - name: event_time
        data_type: 'timestamp'
      - name: ground_truth_id
        data_type: 'string'
      - name: ground_truth_event_time
        data_type: 'timestamp'
      - name: prediction_id
        data_type: 'string'
      - name: prediction_event_time
        data_type: 'timestamp'
      - name: prediction_updated_time
        data_type: 'timestamp'
      - name: obx_event_time
        data_type: 'timestamp'
      - name: obx_recorded_time
        data_type: 'timestamp'
      - name: procedure_name
        data_type: 'string'
      - name: case_id
        data_type: 'string'
      - name: external_case_id
        data_type: 'string'

  - name: apella_vs_ehr_events_first_versions_for_DATA_1863
    description: >
      This filters apella_vs_ehr_events_first_versions by the ones requested in the ticket.
      It only includes patient wheels in, out, and patient draped for WT-03,
      and drops many columns
    columns:
      - name: ds
        data_type: 'timestamp'
      - name: room_id
        data_type: 'string'
      - name: event_type_id
        data_type: 'string'
      - name: coalesced_event_time
        data_type: 'timestamp'
        description: "Renamed from event_time"
      - name: ground_truth_event_time
        data_type: 'timestamp'
      - name: prediction_event_time
        data_type: 'timestamp'
      - name: prediction_recorded_time
        data_type: 'timestamp'
      - name: obx_event_time
        data_type: 'timestamp'
      - name: obx_recorded_time
        data_type: 'timestamp'
      - name: procedure_name
        data_type: 'string'
      - name: case_id
        description: "The external_case_id"
        data_type: 'string'

  - name: apella_vs_ehr_events_first_versions_for_DATA_1863_analysis
    description: >
      This analyzes apella_vs_ehr_events_first_versions_for_DATA_1863_analysis to ensure we 
      like the data that we are sending to the customer.
    columns:
      - name: ds
        data_type: 'timestamp'
      - name: event_type_id
        data_type: 'string'
      - name: total_rows
        data_type: 'int'
      - name: total_ground_truth
        data_type: 'int'
      - name: total_predictions
        data_type: 'int'
      - name: total_obx
        data_type: 'int'
      - name: total_apella_false_positives
        data_type: 'int'
      - name: total_apella_false_negatives
        data_type: 'int'
      - name: total_obx_false_positives
        data_type: 'int'
      - name: total_obx_false_negatives
        data_type: 'int'
      - name: avg_apella_latency
        data_type: 'float'
      - name: avg_obx_latency
        data_type: 'float'
      - name: avg_apella_time_error
        data_type: 'float'
      - name: avg_obx_time_error
        data_type: 'float'
      - name: apella_time_error_95p
        data_type: 'float'
      - name: obx_time_error_95p
        data_type: 'float'
      - name: total_ground_truth_not_filtered
        data_type: 'int'

  - name: ehr_cases_with_procedures
    description: >
      This table joins the case table with the procedures about cases.
    columns:
      - name: ds
        data_type: "timestamp"
      - name: org_id
        data_type: 'string'
      - name: site_id
        data_type: 'string'
      - name: room_id
        data_type: 'string'
      - name: case_id
        data_type: "string"
      - name: external_case_id
        data_type: "string"
      - name: procedures
        data_type: "struct<string, string>"
