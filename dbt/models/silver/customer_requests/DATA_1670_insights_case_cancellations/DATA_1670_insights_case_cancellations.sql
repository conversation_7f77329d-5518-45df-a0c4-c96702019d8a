{{ daily_config() }}

with public_cases as (
    select
        public_cases.case_id,
        public_cases.created_time,
        public_cases.external_case_id,
        public_cases.case_classification_types_id as schedule_type,
        public_cases.patient_class as encounter_class,
        public_cases.room_id as room,
        extract(date from datetime(public_cases.created_time, sites.timezone)) as date_of_cancellation_local,
        extract(date from datetime(public_cases.scheduled_start_time, sites.timezone)) as date_of_surgery_local,
        regexp_replace(to_json_string(public_cases.cancellation_reason), r'[\[\]"]', '') as cancellation_reason,
        -- Question for same day: how to handle cancellation date after date_of_surgery? What does that mean?
        (
            extract(date from datetime(public_cases.scheduled_start_time, sites.timezone))
            = extract(date from datetime(public_cases.created_time, sites.timezone))
        ) as same_day
    from {{ api_table_snapshot("bronze", "public_cases", alias='public_cases') }}
    inner join {{ api_table_snapshot("bronze", "public_sites", "sites") }}
        on public_cases.site_id = sites.id
    where
        public_cases.org_id = 'north_bay'
        and public_cases.status = 'canceled'
        -- Before 2024-03-01 we were not getting cancellation reasons reliably
        and public_cases.created_time >= '2024-03-01'
),

staff as (
    select
        staff.apella_case_id,
        concat(staff.first_name, ' ', staff.last_name) as primary_surgeon
    from {{ ref("core_case_staff") }} as staff
    where
        staff.ds = {{ ds() }}
        and staff.role = 'Primary Surgeon'
        and staff.is_candidate_primary_surgeon = true
),

procedures as (
    select
        procedures.apella_case_id,
        split(procedures.procedure_name, ',')[offset(0)] as primary_procedure
    from {{ ref("core_case_procedures") }} as procedures
    where
        procedures.ds = {{ ds() }}
        and procedures.is_candidate_primary_procedure
)

select
    {{ ds() }} as ds,
    public_cases.external_case_id,
    public_cases.date_of_cancellation_local,
    public_cases.date_of_surgery_local,
    public_cases.schedule_type,
    public_cases.encounter_class,
    public_cases.room,
    staff.primary_surgeon,
    procedures.primary_procedure,
    public_cases.cancellation_reason,
    public_cases.same_day
from public_cases
left outer join staff on public_cases.case_id = staff.apella_case_id
left outer join procedures on public_cases.case_id = procedures.apella_case_id
