version: 1

models:
  - name: schedule_assistant_surgeon_procedure_combos
    description: >
      This table contains the combinations of surgeons and procedures for us in Schedule Assistant.
    columns:
      - name: ds
        data_type: "timestamp"
        description: "The date the data was processed."
      - name: procedure_name
        data_type: "string"
        description: "The cleaned name of the procedure."
      - name: surgeon_id
        data_type: "string"
        description: "The unique identifier for the surgeon."
      - name: surgeon_name
        data_type: "string"
        description: "The full name of the surgeon."
