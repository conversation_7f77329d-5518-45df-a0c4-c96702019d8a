{{ daily_config(produce_latest_snapshot=True) }}

select
    {{ ds() }} as ds,
    case_features.org_id as org_id,
    case_features.first_primary_surgeon_id as surgeon_id,
    case_features.first_primary_surgeon as surgeon_name,
    procedure_name
from {{ ref('forecasting_case_features_combined') }} as case_features,
    unnest(case_features.case_procedure_list) as procedure_name
where case_features.ds = {{ ds() }} and case_features.first_primary_surgeon_id is not null
group by 1, 2, 3, 4, 5
