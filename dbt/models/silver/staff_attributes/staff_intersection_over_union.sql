{{ daily_config() }}

with cases_with_timezone as (
    -- just add timezone to every row
    select
        c.case_id,
        c.room_id,
        c.scheduled_start_time,
        c.scheduled_end_time,
        s.timezone
    from {{ api_table_snapshot("bronze", "public_cases", "c") }}
    inner join {{ api_table_snapshot("bronze", "public_rooms", "r") }}
        on c.room_id = r.id
    inner join {{ api_table_snapshot("bronze", "public_sites", "s") }}
        on c.site_id = s.id
    where c.status = "scheduled"
),

midnight_straddling_cases_first_part as (
    -- this cte takes care of cases that straddle midnight. Cases will be split into 2 cases,
    -- each fully contained in the local date. This cte is just adding a case from scheduled_case_start
    -- to just before midnight. There is a similar cte taking care of the second part.
    -- Because we are using a union_all further down the line, we need columns for all 3 tables
    -- (midnight_straddling_cases_first_part, midnight_straddling_cases_second_part, those_not_straddling_midnight)
    -- to be in the same order, that is why we are excluding scheduled_start_time and adding it
    -- back
    --
    -- midnight:                         |
    -- original_case          -------------------------
    -- in this cte            -----------
    select
        case_id,
        room_id,
        timezone,
        scheduled_start_time,
        timestamp_sub(timestamp(date(scheduled_end_time, timezone)), interval 1 microsecond) as scheduled_end_time
    from cases_with_timezone
    where date(scheduled_start_time, timezone) != date(scheduled_end_time, timezone)
),

midnight_straddling_cases_second_part as (
    -- this is taking care of the 2nd part of those cases straddling midnight. From midnight to
    -- scheduled_end_time
    --
    -- midnight:                         |
    -- original_case          -------------------------
    -- in this cte                       --------------
    select
        case_id,
        room_id,
        timezone,
        scheduled_end_time,
        timestamp(date(scheduled_end_time, timezone)) as scheduled_start_time
    from cases_with_timezone
    where date(scheduled_start_time, timezone) != date(scheduled_end_time, timezone)
),

not_midnight_straddling_cases as (
    --
    -- midnight:                         |
    -- original_case          -------------------------
    -- in this cte                    nothing, this case will not contribute anything to this cte
    select
        case_id,
        room_id,
        timezone,
        scheduled_start_time,
        scheduled_end_time
    from cases_with_timezone
    where date(scheduled_start_time, timezone) = date(scheduled_end_time, timezone)
),

cases_in_one_day as (
    -- thanks to the 3 ctes above (midnight_straddling_cases_first_part, midnight_straddling_cases_second_part,
    -- midnight_straddling_cases_second_part) now each `case` has
    -- date(scheduled_start_time) = -- date(scheduled_end_time)
    select
        case_id,
        room_id,
        timezone,
        scheduled_start_time,
        scheduled_end_time
    from midnight_straddling_cases_first_part
    union all
    select
        case_id,
        room_id,
        timezone,
        scheduled_start_time,
        scheduled_end_time
    from midnight_straddling_cases_second_part
    union all
    select
        case_id,
        room_id,
        timezone,
        scheduled_start_time,
        scheduled_end_time
    from not_midnight_straddling_cases
),

pivoted_cte as (
    -- this pivoted_cte pivots case scheduled_start_time and scheduled_end_time into 2 new columns
    -- scheduled_datetime_local: is the datetime associated with either scheduled_start or scheduled_end
    -- start_or_end: is the name of the column the schedule is coming from, literally
    -- will be either `scheduled_start_time` or `scheduled_end_time`
    --
    -- We also add the prev values of scheduled and start_or_end
    select
        c.case_id,
        c.room_id,
        cs.staff_id,
        c.timezone,
        c.scheduled_time,
        c.start_or_end,
        date(c.scheduled_time, c.timezone) as case_date,
        -- in the following `lag` statements, in the `order by` clause we make use of the fact that
        -- the string `scheduled_start_time` > `scheduled_end_time` when sorted lexicographically.
        -- Since we want the start to appear before the end (in the rare case that we have two
        -- cases for a single surgeon one starting exactly when the other is ending), we sort by
        -- start_or_end using `desc`
        lag(c.scheduled_time)
            over (
                partition by cs.staff_id, date(c.scheduled_time, c.timezone) order by
                    c.scheduled_time asc, c.start_or_end desc
            )
            as prev_scheduled_time,
        lag(c.start_or_end)
            over (
                partition by cs.staff_id, date(c.scheduled_time, c.timezone) order by
                    c.scheduled_time asc, c.start_or_end desc
            )
            as prev_start_or_end
    from cases_in_one_day
    unpivot (scheduled_time for start_or_end in (scheduled_start_time, scheduled_end_time)) as c
    inner join {{ api_table_snapshot("bronze", "public_case_staff", "cs") }}
        on c.case_id = cs.case_id
),

with_concurrent_delta as (
    -- this cte will be used to integrate over time how many concurrent surgeries we have,
    -- it adds 2 columns:
    --      duration_of_segment_s: the amount of time in between consecutive events.
    --      concurrent_delta: is either 1 or -1 and will be used in cumsum to know how many concurrent
    --             surgeries we have at a point in time.
    -- Every time we encounter a `scheduled_start` event we have to increment the count
    -- (the delta is 1) and every time we see a `scheduled_end` we have the reduce the
    -- count (delta is -1)
    --
    -- example:
    --      each scheduled_start/end_time is consider an event. If we have 2 surgeries, one
    --      from 8am to 12pm and one from 10am to 12pm, we'll have 4 rows here
    --      (I simplified column names to make it fit under our linting limit of 120 chars)
    --time      prev_time       start_or_end prev_start_or_end duration_s concurrent_delta  concurrency_cnt
    --8:00am    null -> 8:00am  start        null              0          1                 1
    --10:00am   8:00am          start        start             7200       1                 2
    --12:00am   10:00am         end          start             7200       -1                1
    --12:00am   12:00am         end          end               0          -1                0
    select
        staff_id,
        case_date,
        case_id,
        room_id,
        scheduled_time,
        case
            when prev_scheduled_time is null then 0
            when prev_start_or_end = "scheduled_start_time" then 1
            else -1
        end as concurrent_delta,
        datetime_diff(scheduled_time, coalesce(prev_scheduled_time, scheduled_time), second) as duration_of_segment_s
    from pivoted_cte
),

with_concurrency_cnt as (
    -- this is where the cumsum over concurrent_delta takes place.
    -- concurrency_cnt is the # of scheduled surgeries that have started and not ended at a point in time
    select
        staff_id,
        case_date,
        case_id,
        room_id,
        duration_of_segment_s,
        sum(concurrent_delta) over (
            partition by staff_id, case_date
            order by scheduled_time
            rows between unbounded preceding and current row
        ) as concurrency_cnt
    from with_concurrent_delta
),

union_overlap as (
    select
        staff_id,
        case_date,
        cast(count(distinct case_id) as int) as number_of_cases,
        sum(duration_of_segment_s) as union_seconds,
        array_agg(distinct room_id) as room_ids
    from with_concurrency_cnt
    where concurrency_cnt > 0
    group by staff_id, case_date
),

intersection_overlap as (
    select
        staff_id,
        case_date,
        sum(duration_of_segment_s) as intersection_seconds
    from with_concurrency_cnt
    where concurrency_cnt > 1
    group by staff_id, case_date
)

select
    {{ ds() }} as ds,
    u.staff_id,
    u.case_date,
    coalesce(i.intersection_seconds, 0) as intersection_seconds,
    u.union_seconds,
    coalesce(i.intersection_seconds, 0) / u.union_seconds as iou,
    u.room_ids,
    array_length(u.room_ids) as number_of_rooms,
    u.number_of_cases
from union_overlap as u
left outer join intersection_overlap as i
    on
        u.staff_id = i.staff_id
        and u.case_date = i.case_date
where
    u.union_seconds > 0
    and i.intersection_seconds > 0
