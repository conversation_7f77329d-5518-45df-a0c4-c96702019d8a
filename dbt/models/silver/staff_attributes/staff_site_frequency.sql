{{ daily_config(produce_latest_snapshot=True) }}

select
    {{ ds() }} as ds,
    case_features.site_id as site_id,
    case_features.first_primary_surgeon_id as staff_id,
    procedure_name,
    count(*) as case_count
from {{ ref('forecasting_case_features_combined') }} as case_features,
    unnest(case_features.case_procedure_list) as procedure_name
where
    case_features.ds = {{ ds() }}
    and case_features.first_primary_surgeon_id is not null
    and procedure_name is not null
    and case_features.site_id is not null
group by
    case_features.site_id,
    case_features.first_primary_surgeon_id,
    procedure_name
