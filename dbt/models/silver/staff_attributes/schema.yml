version: 1

models:
  - name: staff_intersection_over_union
    description: >
      surgeon Intersection Over Union metric
      IOU: Intersection Over Union. Every time surgeon is simultaneously scheduled to be in 2 rooms, 
      those minutes count as intersection. The union of all times a surgeon is scheduled 
      to do surgery are the union. IOU is just the ratio: intersection / union
      
      * example_1
      prime_time    start                                                              end
                      |                                                                 |
      room_1:             -----------------------
      room_2:                              -----------------------
      Intersection:                        ------
      Union:              ----------------------------------------
      
      * example_2
      prime_time    start                                                              end
                      |                                                                 |
      room_1:             -----------------------
      room_2:                              -----------------------
      room_3:                                                --------------
      Intersection:                        ------            -----
      Union:              -------------------------------------------------
      
      * example_3
      prime_time    start                                                              end
                      |                                                                 |
      room_1:             ----------------------------
      room_2:                              -----------------------
      room_3:                                 ------
      Intersection:                        -----------                  
      Union:              -----------------------------------------
      
      Notes:
      1. We don't return staff with 0 iou, we could by removing the `and intersection_seconds > 0`
      at the very end. If you need a full list of staff, you will need to do a left join into 
      something with all the staff_id of interest and coalesce
      2. We are currently returning staff that are overlapping in different cases in the same room.
      This looks more like a bad scheduling practice than anything else. We are not filtering
      those cases here. In some cases at least, it is clear that that is happening because the 
      room_ids is just a single room_id
      3. We assume that there are no surgeries that are long enough to straddle 2 nights. There is
      a test that asserts that is the case. If we ever get surgeries that are that long, the test
      will fail and this will need to be addressed
      
      Implementation Note:
      The whole idea is to sort all the scheduled_start/end_times and just keep track of how many
      surgeries are concurrent. Every time a new surgery starts concurrent_cnt increases by 1 and 
      when a surgery ends we decrease it by 1. The intersection is the sum of all the gaps in time
      where concurrent_cnt > 1. The union is the sum of all the gaps in time where concurrent_cnt > 0
    columns:
      - name: ds
        data_type: "timestamp"
      - name: staff_id
        data_type: "string"
      - name: case_date
        data_type: "date"
      - name: intersection_seconds
        data_type: "int"
      - name: union_seconds
        data_type: "int"
      - name: iou
        data_type: "float"
      - name: room_ids
        data_type: "array(string)"
      - name: number_of_rooms
        data_type: "int"
        description: "The total number of rooms the staff_attributes was working on on that date. For some of the rooms, there may be no overlap with other rooms"
      - name: number_of_cases
        data_type: "int"
        description: "The total number of cases the staff_attributes was working on on that date. For some of the cases, there may be no overlap with other cases"
  - name: staff_site_frequency
    description: >
      Most frequent site for each staff
      This is a simple aggregation of the number of cases per site per staff
      The most frequent site is the one with the most cases
    columns:
      - name: ds
        data_type: "timestamp"
      - name: staff_id
        data_type: "string"
        tests:
          - not_null
      - name: site_id
        data_type: "string"
        tests:
          - not_null
      - name: procedure_name
        data_type: "string"
        tests:
          - not_null
      - name: case_count
        data_type: "int"
        tests:
          - not_null
          - accepted_range:
              min_value: 0
