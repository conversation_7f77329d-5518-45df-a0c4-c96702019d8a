{{ daily_config() }}
with events_to_subscribers as (
    select
        event_to_case.event_id,
        event_to_case.phase_id,
        event_to_case.case_id,
        event.event_type_id,
        senci.id as senci_id
    from {{ ref("notifications_event_to_case") }} as event_to_case
    inner join {{ api_table_snapshot("bronze", "public_events", "event") }}
        on event_to_case.event_id = event.id
    inner join {{ api_table_snapshot("bronze", "public_case_staff", "case_staff") }}
        on
            event_to_case.case_id = case_staff.case_id
            and event.start_time between case_staff.created_time and
            coalesce(case_staff.archived_time, current_timestamp())
    inner join {{ api_table_snapshot("bronze", "public_staff_event_notification_contact_information", "senci") }}
        on
            event.event_type_id = senci.event_type_id
            -- if a surgeons unsubscribes to notifications from the event_type, senci.archived_time
            -- will be set
            and event.start_time between senci.created_time and coalesce(senci.archived_time, current_timestamp())
            and case_staff.staff_id = senci.staff_id
    where event_to_case.ds = {{ ds() }}
)

select
    {{ ds() }} as ds,
    event_id
from events_to_subscribers
group by event_id
