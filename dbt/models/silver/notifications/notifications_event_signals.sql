{{ daily_config(produce_latest_snapshot=True) }}

-- This is every distinct Event signal that we will want to analyze
with max_event_times as (
    select
        events.*,
        most_recent_event.deleted_at as final_deleted_at,
        -- The max time to check is when the event version was updated or the event was deleted
        -- So we only care about the stats for this event version until the max time to check
        case
            when most_recent_event.source_type = 'human_gt' then most_recent_event.start_time
        end as actual_start_time,
        coalesce(next_event.updated_time, events.deleted_at, current_timestamp()) as max_time_to_check
    from {{ api_table_snapshot('bronze', 'public_events_history', 'events') }}
    left outer join {{ api_table_snapshot('bronze', 'public_events_history', 'next_event') }}
        on events.id = next_event.id and events.version + 1 = next_event.version
    left outer join {{ api_table_snapshot('bronze', 'public_events', 'most_recent_event') }}
        on
            events.id = most_recent_event.id
    where
        events.source_type in ('prediction', 'human_gt')
        and (events.deleted_at <= events.updated_time or events.deleted_at is null)
),

phase_versions_to_index_map as (
    select
        phases.id,
        phases.version,
        phases.updated_time,
        row_number() over (
            partition by phases.id
            order by phases.version asc
        ) as version_index
    from {{ api_table_snapshot('bronze', 'public_phases_history', 'phases') }}
),

all_valid_phase_events as (
    select
        phases.case_id,
        phases.id,
        phases.start_event_id as event_id,
        phases.updated_time,
        phase_versions_to_index_map.version_index,
        phases.source_type,
        phases.status,
        phases.type_id
    from {{ api_table_snapshot('bronze', 'public_phases_history', 'phases') }}
    inner join phase_versions_to_index_map
        on
            phases.id = phase_versions_to_index_map.id
            and phases.version = phase_versions_to_index_map.version
    union all
    select
        phases.case_id,
        phases.id,
        phases.end_event_id as event_id,
        phases.updated_time,
        phase_versions_to_index_map.version_index,
        phases.source_type,
        phases.status,
        phases.type_id
    from {{ api_table_snapshot('bronze', 'public_phases_history', 'phases') }}
    inner join phase_versions_to_index_map
        on
            phases.id = phase_versions_to_index_map.id
            and phases.version = phase_versions_to_index_map.version
    where
        phases.end_event_id is not null

),

max_phase_times as (
    -- Delays in phase processing can cause the phase to be updated after the event
    -- and there are edge cases where an event will have it's type updated after creation
    -- so we need to do coalescing
    -- see event with id '4120bca9-03e1-484a-8239-47d231b2e5a3' for an example
    -- where our invariant is violated without this logic
    select
        all_valid_phase_events.case_id,
        all_valid_phase_events.id,
        all_valid_phase_events.event_id as event_id,
        all_valid_phase_events.type_id as type_id,
        min(all_valid_phase_events.updated_time) as min_time_to_check,
        -- The max time to check is when the phase version was updated or the phase was deleted
        -- So we only care about the stats for this phase version until the max time to check
        coalesce(max(next_phase.updated_time), current_timestamp()) as max_time_to_check
    from all_valid_phase_events
    left outer join phase_versions_to_index_map as next_phase
        on
            all_valid_phase_events.id = next_phase.id
            and all_valid_phase_events.version_index + 1 = next_phase.version_index
    where
        all_valid_phase_events.source_type = 'unified'
        and all_valid_phase_events.status = 'VALID'
        and all_valid_phase_events.case_id is not null
    group by
        all_valid_phase_events.id,
        all_valid_phase_events.case_id,
        all_valid_phase_events.event_id,
        all_valid_phase_events.type_id
)

select
    {{ ds() }} as ds,
    max_event_times.id as event_id,
    max_phase_times.id as phase_id,
    -- Take the max of the min time to check and the updated time of the event
    -- This is the earliest time we can consider the event to be valid
    greatest(max_phase_times.min_time_to_check, max_event_times.updated_time) as min_valid_time,
    -- Take the min of the max time to check and the updated time of the event
    -- This is the latest time we can consider the event to be valid
    least(max_phase_times.max_time_to_check, max_event_times.max_time_to_check) as max_valid_time,
    max_phase_times.min_time_to_check as phase_min_time,
    max_phase_times.max_time_to_check as phase_max_time_check,
    max_phase_times.case_id as case_id,
    max_event_times.site_id as site_id,
    max_event_times.room_id as room_id,
    max_event_times.org_id as org_id,
    max_event_times.start_time as event_start_time,
    max_event_times.updated_time as event_updated_time,
    max_event_times.max_time_to_check as event_max_time_to_check,
    max_event_times.event_type_id as event_type_id,
    max_event_times.version as event_version,
    max_event_times.deleted_at,
    max_event_times.created_time as event_created_time,
    max_event_times.actual_start_time as event_actual_start_time,
    max_event_times.confidence as event_confidence,
    max_event_times.source_type as event_source_type,
    max_event_times.final_deleted_at as event_final_deleted_at,
    max_phase_times.type_id as phase_type_id
from max_event_times
inner join max_phase_times
    on
        max_event_times.id = max_phase_times.event_id
        -- This will match events that are within the active phase time range and vice versa
        and (
            max_event_times.updated_time < max_phase_times.max_time_to_check
            and max_event_times.max_time_to_check > max_phase_times.min_time_to_check
        )
