version: 1

models:
  - name: notifications_event_to_case
    description: >
      Table that links events to all the CASE phases they were ever associated with and their 
      corresponding case_id.
      When case matching fails, we will have overlapping phases of type CASE with different
      case_ids. In those cases, all events overlapping a problematic phase and room should be linked
      to all phases and case_ids. We filter out those `phases` generated by `forecasting`.
    columns:
      - name: ds
        data_type: "timestamp"
      - name: event_id
        data_type: "string"
      - name: phase_id
        data_type: "string"
      - name: case_id
        data_type: "string"

  - name: notifications_events_with_subscribers
    description: >
      Table to select which events from public_events should have notifications (at least one).
      This is somewhat tricky because we have to go through the surgeon, and that assumes
      correct case matching. In the case of case matching issues, the same event will be
      associate with more than a case_id. We are going to take all cases ever associated with
      an event, and match all possible surgeons
    columns:
      - name: ds
        data_type: "timestamp"
      - name: event_id
        data_type: "string"
      - name: phase_id
        data_type: "string"
      - name: case_id
        data_type: "string"
      - name: senci_id
        data_type: "string"

  - name: notifications_event_signals
    description: >
      A table that tracks every distinct notification signal we generate. This table is used to
      track the quality of notifications and to generate the notifications dashboard.
    columns:
      - name: ds
        data_type: "timestamp"
      - name: event_id
        data_type: "string"
      - name: phase_id
        data_type: "string"
      - name: min_valid_time
        data_type: "timestamp"
      - name: max_valid_time
        data_type: "timestamp"
      - name: phase_min_time
        data_type: "timestamp"
      - name: phase_max_time_check
        data_type: "timestamp"
      - name: case_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: org_id
        data_type: "string"
      - name: event_start_time
        data_type: "timestamp"
      - name: event_updated_time
        data_type: "timestamp"
      - name: event_max_time_to_check
        data_type: "timestamp"
      - name: event_type_id
        data_type: "string"
      - name: event_version
        data_type: "integer"
      - name: deleted_at
        data_type: "timestamp"
      - name: event_created_time
        data_type: "timestamp"
      - name: event_actual_start_time
        data_type: "timestamp"
      - name: event_confidence
        data_type: "float"
      - name: event_source_type
        data_type:  "string"
