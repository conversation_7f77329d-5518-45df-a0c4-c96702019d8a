{{ daily_config() }}

with case_phases_with_times_and_room as (
    select
        phases.id,
        start_e.org_id,
        start_e.site_id,
        start_e.room_id,
        phases.case_id,
        start_e.start_time,
        end_e.start_time as end_time
    from {{ api_table_snapshot("bronze", "public_phases_history", "phases") }}
    inner join {{ api_table_snapshot("bronze", "public_events", "start_e") }}
        on phases.start_event_id = start_e.id
    inner join {{ api_table_snapshot("bronze", "public_events", "end_e") }}
        on phases.end_event_id = end_e.id
    where
        phases.type_id = 'CASE'
        and phases.case_id is not null
        and phases.source_type not in ('forecasting')
),

case_phases as (
    -- now group by phase.id and case_id and keep the min(start_time) and the max(end_time)
    -- Every time we have a case matching error we'll end up with two (or more) rows
    -- for the same start/end_time and room_id
    select
        id,
        case_id,
        any_value(org_id) as org_id,
        any_value(site_id) as site_id,
        any_value(room_id) as room_id,
        min(start_time) as start_time,
        max(end_time) as end_time
    from case_phases_with_times_and_room
    group by id, case_id
)

select
    {{ ds() }} as ds,
    events.id as event_id,
    case_phases.id as phase_id,
    case_phases.case_id
from {{ api_table_snapshot("bronze", "public_events", "events") }}
inner join case_phases
    on
        events.org_id = case_phases.org_id
        and events.site_id = case_phases.site_id
        and events.room_id = case_phases.room_id
        and events.start_time between case_phases.start_time and case_phases.end_time
where events.source_type not in ('forecasting')
