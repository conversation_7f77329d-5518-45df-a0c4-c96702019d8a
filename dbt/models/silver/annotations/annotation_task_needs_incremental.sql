{{ daily_config(expiration_days=None, require_partition_filter=False) }}

-- This table runs daily and captures the annotation tasks created today
-- So, we first find all the annotation tasks created today.
with annotation_tasks_created_today as (
    select
        tasks.id as task_id,
        tasks.room_id as room_id,
        tasks.start_time as task_start_time,
        tasks.end_time as task_end_time,
        tasks.created_time as task_created_time,
        json_value_array(task_types_history.event_types) as task_event_types
    from {{ api_table_snapshot("bronze", "public_annotation_tasks_history", alias="tasks") }}
    inner join {{ api_table_snapshot("bronze", "public_annotation_task_types_history", alias="task_types_history") }}
        on
            tasks.type_id = task_types_history.id
            and tasks.type_version = task_types_history.version
    where
        -- Subtract 1 day, we calculate the data for the previous day
        date(tasks.created_time, "America/Los_Angeles") = {{ ds() }} - interval 1 day
),

-- We will be joining those with the annotation needs history table,
annotation_needs_created_recently as (
    select *
    from {{ api_table_snapshot("annotation_needs", "annotation_needs_history", alias="needs") }}
    -- This table requires a partition filter, and so we filter to the previous 2 days.
    -- We could technically filter by the last ~25 hours (because this table is populated hourly)
    -- but its simpler and safer to keep it at 2 days, incase there is a temporary outage.
    where
        needs.run_timestamp >= timestamp({{ ds() }}) - interval 2 day
        and needs.run_timestamp < timestamp({{ ds() }})
),

-- We don't technically need this table, but it makes the joins much easier.  This table
-- includes one row per run of the annotation needs dagster job.
annotation_needs_runs_created_recently as (
    select
        run_id,
        run_timestamp
    from {{ api_table_snapshot("annotation_needs", "annotation_needs_run_info", alias="run_info") }}
    -- This table requires a partition filter, and so we filter to the previous 2 days.
    -- We could technically filter by the last ~25 hours (because this table is populated hourly)
    -- but its simpler and safer to keep it at 2 days, incase there is a temporary outage.
    where
        run_timestamp >= timestamp({{ ds() }}) - interval 2 day
        and run_timestamp < timestamp({{ ds() }})
),

-- Then we join the annotation tasks with the annotation needs run info, to find
-- the most recent run for that task, which is the run that would have affected
-- the task's annotation status / needs.
annotation_tasks_joined_with_annotation_needs_run_info as (
    select
        -- Task info
        tasks.task_id,
        tasks.room_id,
        tasks.task_start_time,
        tasks.task_end_time,
        tasks.task_event_types,
        -- Run info
        run_info.run_id,
        run_info.run_timestamp
    from annotation_tasks_created_today as tasks
    inner join annotation_needs_runs_created_recently as run_info
        -- Find any run that happened before the task was created
        on task_created_time > run_info.run_timestamp
        -- And look back at most 1 day
        and task_created_time <= run_info.run_timestamp + interval 1 day
    qualify
        row_number() over (
            partition by task_id
            order by
                run_info.run_timestamp desc,
                run_info.run_id desc -- tie breaker
        ) = 1
),

-- Then, we join the that with the annotation needs table, to find all needs for each task.
annotation_tasks_joined_with_annotation_needs as (
    select
        -- Task Info
        tasks.task_id,
        tasks.room_id,
        tasks.task_start_time,
        tasks.task_end_time,
        tasks.task_event_types,
        -- Run info
        tasks.run_id,
        tasks.run_timestamp,
        -- Needs info
        needs.category as needs_category,
        needs.start_time as needs_start_time,
        needs.end_time as needs_end_time,
        needs.priority as needs_priority,
        needs.description as needs_description,
        needs.event_type_id as needs_event_type_id
    from annotation_tasks_joined_with_annotation_needs_run_info as tasks
    inner join annotation_needs_created_recently as needs
        -- Join by run_id and timestamp to find just the specified needs from the run
        on
            tasks.run_timestamp = needs.run_timestamp
            and tasks.run_id = needs.run_id
            -- Filter by room_id for this task
            and tasks.room_id = needs.room_id
            -- Find any overlapping needs
            -- This is exclusive (< instead of <=) because a need that ends the moment a task starts
            -- should not trigger the task to be annotated.
            and tasks.task_start_time < needs.end_time
            and tasks.task_end_time > needs.start_time
            -- We only want the needs that are relevant to the task
            and (needs.event_type_id in unnest(task_event_types) or needs.event_type_id = "all")
)

select
    {{ ds() }} as ds,
    task_id,
    room_id,
    task_start_time,
    task_end_time,
    task_event_types,
    run_id,
    run_timestamp,
    needs_category,
    needs_start_time,
    needs_end_time,
    needs_priority,
    needs_description
from annotation_tasks_joined_with_annotation_needs
