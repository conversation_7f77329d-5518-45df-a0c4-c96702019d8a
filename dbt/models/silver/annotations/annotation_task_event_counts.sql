{{ daily_config() }}

with events as (
    select
        id,
        room_id,
        start_time,
        source_type
    from {{ api_table_snapshot("bronze", "public_events") }}
),

events_for_annotation_tasks as (
    select
        annotation_tasks.id as annotation_task_id,
        events.source_type
    from {{ api_table_snapshot("bronze", "public_annotation_tasks", "annotation_tasks") }}
    left outer join events
        on (
            annotation_tasks.room_id = events.room_id
            and annotation_tasks.start_time <= events.start_time
            and annotation_tasks.end_time > events.start_time
        )
)

select
    {{ ds() }} as ds,
    annotation_task_id,
    count_forecasting,
    count_human_gt,
    count_prediction
from (
    select
        annotation_task_id,
        source_type
    from events_for_annotation_tasks
)
pivot (
    count(source_type) as count for source_type in (
        'forecasting', 'human_gt', 'prediction'
    )
)
