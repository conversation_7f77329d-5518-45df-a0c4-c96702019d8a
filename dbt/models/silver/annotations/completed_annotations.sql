{{ daily_config() }}

with task_history_with_next_status as (
    select
        id,
        version,
        updated_time,
        status,
        annotator_user_id,
        reviewer_user_id,
        lead(version) over (partition by id order by version) as next_version,
        lead(updated_time) over (partition by id order by version) as next_updated_time,
        lead(status) over (partition by id order by version) as next_status
    from {{ api_table_snapshot("bronze", "public_annotation_tasks_history") }}
)

-- annotation_finish_time = latest time a task was updated from
-- IN_PROGRESS to READY_FOR_REVIEW, DONE, or CANCELLED
-- OR task was moved from BLOCKED to DONE, CANCELLED, or READY_FOR_REVIEW and there is no reviewer user
select
    {{ ds() }} as ds,
    id as annotation_task_id,
    max(next_updated_time) as annotation_finish_time
from task_history_with_next_status
where
    (
        status in ('IN_PROGRESS')
        and next_status in ('READY_FOR_REVIEW', 'DONE', 'CANCELLED')
        and annotator_user_id is not null
    ) or (
        status in ('BLOCKED')
        and next_status in ('READY_FOR_REVIEW', 'DONE', 'CANCELLED')
        and annotator_user_id is not null
        and reviewer_user_id is null
    )
group by id
