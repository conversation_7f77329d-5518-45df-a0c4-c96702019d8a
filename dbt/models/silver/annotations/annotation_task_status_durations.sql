{{ daily_config() }}

with history_with_next_update as (
    -- add the next updated_time so we can calculate time spent in each status
    select
        history.id,
        history.start_time,
        history.end_time,
        history.status,
        history.updated_time,
        history.type_id,
        history.created_time,
        lead(history.updated_time)
            over (
                partition by history.id
                order by history.version
            )
            as next_updated_time
    from {{ api_table_snapshot("bronze", "public_annotation_tasks_history", "history") }}
),

history_with_status_times_raw as (
    -- calculate status start and end times
    select
        id,
        start_time,
        end_time,
        status,
        updated_time,
        type_id,
        created_time,
        updated_time as status_start_time,
        -- DONE never really has an end time
        if(status = 'DONE', null, next_updated_time) as status_end_time
    from history_with_next_update
)

select
    {{ ds() }} as ds,
    id as annotation_task_id,
    status,
    min(status_start_time) as first_start_time,
    max(status_end_time) as last_end_time,
    sum(timestamp_diff(status_end_time, status_start_time, second))
        as duration_seconds,
    count(*) as records_count
from history_with_status_times_raw
group by
    annotation_task_id,
    status
