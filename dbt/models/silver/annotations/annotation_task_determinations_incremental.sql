-- Since the annotation_task_needs table only calculates data for one day at a time,
-- this table will only have data for one day at a time.  So we need to not expire the partitions.
{{ daily_config(expiration_days=None, require_partition_filter=False) }}

with task_needs as (
    select
        task_id,
        room_id,
        task_start_time,
        task_end_time,
        task_event_types,
        run_id,
        run_timestamp,
        needs_category,
        needs_start_time,
        needs_end_time,
        needs_priority,
        needs_description
    from {{ ref("annotation_task_needs_incremental") }}
    where
        ds = {{ ds() }}
        -- needs with priority 0 are just for record keeping and should not affect
        -- determination of task status
        and needs_priority > 0
),

tasks_with_determinations as (
    select
        task_id,
        room_id,
        task_start_time,
        task_end_time,
        task_event_types,
        -- If we have any needs, then we need to annotate this task
        countif(needs_category is not null) > 0 as needs_annotation,
        -- If we have a QA need, then it's a QA task.
        countif(needs_category = 'Quality Assurance') > 0 as is_qa
    from task_needs
    group by task_id, room_id, task_start_time, task_end_time, task_event_types
)

select
    {{ ds() }} as ds,
    task_id,
    room_id,
    task_start_time,
    task_end_time,
    task_event_types,
    needs_annotation,
    is_qa
from tasks_with_determinations
