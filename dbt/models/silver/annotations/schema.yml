version: 3

models:
  - name: annotation_task_event_counts
    columns:
      - name: annotation_task_id
        data_type: "string"
      - name: count_forecasting
        data_type: "int"
      - name: count_human_gt
        data_type: "int"
      - name: count_prediction
        data_type: "int"

  - name: annotation_task_status_durations
    columns:
      - name: annotation_task_id
        data_type: "string"
      - name: status
        data_type: "string"
      - name: first_start_time
        data_type: "timestamp"
      - name: last_end_time
        data_type: "timestamp"
      - name: duration_seconds
        data_type: "int"
      - name: records_count
        data_type: "int"

  - name: completed_annotations
    description: >
      This table attempts to define a "finish time" at which annotation was
      completed by a human.
    columns:
      - name: annotation_task_id
        data_type: "string"
      - name: annotation_finish_time
        data_type: "timestamp"

  - name: completed_reviews
    description: >
      This table attempts to define a "finish time" at which review was
      completed by a human.
    columns:
      - name: annotation_task_id
        data_type: "string"
      - name: review_finish_time
        data_type: "timestamp"

  - name: annotation_task_needs_incremental
    description: >
      This table joins the tasks with the annotation needs that determined the task status.
      It produces one row per need.  This table is incremental, which means that each partition (ds)
      only includes information about tasks that were created that day.
    columns:
      - name: ds
        data_type: 'timestamp'
      - name: task_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: task_start_time
        data_type: "timestamp"
      - name: task_end_time
        data_type: "timestamp"
      - name: task_event_types
        data_type: 'array<string>'
      - name: run_id
        data_type: "string"
      - name: run_timestamp
        data_type: "timestamp"
      - name: needs_category
        data_type: "string"
      - name: needs_start_time
        data_type: "timestamp"
      - name: needs_end_time
        data_type: "timestamp"
      - name: needs_priority
        data_type: "string"
      - name: needs_description
        data_type: "string"


  - name: annotation_task_determinations_incremental
    description: >
      This table aggregates the annotation_task_needs table and provides a determination
      of if this task needs annotation, is a qa task, or is an idle task.
    columns:
      - name: ds
        data_type: 'timestamp'
      - name: task_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: task_start_time
        data_type: "timestamp"
      - name: task_end_time
        data_type: "timestamp"
      - name: task_event_types
        data_type: 'array<string>'
      - name: needs_annotation
        data_type: "boolean"
      - name: is_qa
        data_type: "boolean"
