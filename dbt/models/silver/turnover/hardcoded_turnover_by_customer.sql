{{ daily_config() }}

{% set tampa_id = '"tampa_general"' %}

with tgh_cte_1 as (
    -- these are the hardcoded values for TGH as of July 9, 2024
    select
        "Anesthesiology" as service_line_name,
        20 as turnover_in_minutes
    union all
    select
        "Cardiothoracic Surgery" as service_line_name,
        51 as turnover_in_minutes
    union all
    select
        "Cardiology" as service_line_name,
        38 as turnover_in_minutes
    union all
    select
        "Dentistry" as service_line_name,
        25 as turnover_in_minutes
    union all
    select
        "General Surgery" as service_line_name,
        33 as turnover_in_minutes
    union all
    select
        "Gynecology" as service_line_name,
        33 as turnover_in_minutes
    union all
    select
        "Neurological Surgery" as service_line_name,
        43 as turnover_in_minutes
    union all
    select
        "Obstetrics" as service_line_name,
        30 as turnover_in_minutes
    union all
    select
        "Ophthalmology" as service_line_name,
        21 as turnover_in_minutes
    union all
    select
        "Oral & Maxillofacial Surgery" as service_line_name,
        30 as turnover_in_minutes
    union all
    select
        "Orthopaedics" as service_line_name,
        31 as turnover_in_minutes
    union all
    select
        "Otolaryngology, Head & Neck (ENT)" as service_line_name,
        29 as turnover_in_minutes
    union all
    select
        "Pain Medicine" as service_line_name,
        20 as turnover_in_minutes
    union all
    select
        "Parathyroid" as service_line_name,
        20 as turnover_in_minutes
    union all
    select
        "Pediatric Surgery" as service_line_name,
        32 as turnover_in_minutes
    union all
    select
        "Plastic Surgery" as service_line_name,
        30 as turnover_in_minutes
    union all
    select
        "Podiatry" as service_line_name,
        30 as turnover_in_minutes
    union all
    select
        "Robotic Surgery" as service_line_name,
        39 as turnover_in_minutes
    union all
    select
        "Thyroid" as service_line_name,
        34 as turnover_in_minutes
    union all
    select
        "Transplant" as service_line_name,
        41 as turnover_in_minutes
    union all
    select
        "Trauma Surgery" as service_line_name,
        36 as turnover_in_minutes
    union all
    select
        "Urology" as service_line_name,
        30 as turnover_in_minutes
    union all
    select
        "Vascular Surgery" as service_line_name,
        49 as turnover_in_minutes
),

tgh_cte_2 as (
    -- for TGH, add org_id and the service_line_id
    -- in the future, if we have other hardcoded files, we might need more intermediate steps
    -- probably one per customer, then combine all customers, then add service_line_id
    select
        {{ tampa_id }} as org_id,
        tgh_cte_1.*,
        sl.id as service_line_id
    from tgh_cte_1
    inner join {{ api_table_snapshot("bronze", "public_service_lines", "sl") }}
        on
            tgh_cte_1.service_line_name = sl.name
            and {{ tampa_id }} = sl.org_id
)

select
    {{ ds() }} as ds,
    tgh_cte_2.*
from tgh_cte_2
