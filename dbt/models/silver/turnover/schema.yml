version: 1

models:

  - name: turnover_stats_by_service_line
    description: >
      This table contains the average and median time in minutes it takes a service line to 
      perform a turn over.
      The only criteria to include a turn over into the computation are:
      1. Both the prev and next case have to be done by the same service line
      2. Both prev and next case have to be done on the same local date
      3. The turn over has to be somewhat recent (to slowly pick up changes)
    columns:
      - name: ds
        data_type: "timestamp"
      - name: org_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: phase_type_id,
        data_type: "string"
      - name: service_line_id
        data_type: "string"
      - name: service_line_name
        data_type: "string"
      - name: average_minutes_3_months
        description: Average time in minutes over the last 3 months
        data_type: "int"
      - name: average_minutes_28_days
        description: Average time in minutes over the last 1 month
        data_type: "int"
      - name: median_minutes_3_months
        description: Median time in minutes over the last 3 months
        data_type: "int"
      - name: median_minutes_28_days
        description: Median time in minutes over the last 1 month
        data_type: "int"

  - name: hardcoded_turnover_by_customer
    description: >
      This table contains hardcoded turn overs some customers have given us.
      They use these numbers in their computations (block utilization, scheduling, maybe in other
      ways as well). 
      In some of our products, we need to use these numbers to compare our computations
      with theirs (block utilization for example). In others, for customers like TGH that bake in
      turnover time into the case schedule, we need these numbers to reverse engineer what they did.
      We expect these numbers to change over time (some are likely averages they are computing
      over a given time window)
      At some point, once we finish all the work we are doing in turnovers and they start using
      the number we provide for their computations, these hardcoded numbers might become irrelevant.
      Not clear if at that point in time we'll be able to delete this table since we might always have a
      flow of new customers that bake in turnover time into their schedule like TGH
      
      Source files (we are copy/pasting values from these files)
      TGH, https://docs.google.com/document/d/1sm3viUt13pHbSfQyNA6g9oyTFinEjD-d/edit
    columns:
      - name: ds
        data_type: "timestamp"
      - name: org_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: service_line_id
        data_type: "string"
      - name: service_line_name
        data_type: "string"
      - name: turnover_in_minutes
        description: Time in minutes. This is the whole turnover, they don't split it in prep and cleaning
        data_type: "int"
