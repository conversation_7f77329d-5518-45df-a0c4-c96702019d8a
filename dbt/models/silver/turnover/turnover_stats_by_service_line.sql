{{ daily_config() }}

{% set num_days_ago = 'date_diff(current_date(), date(t.phase_start_datetime_local), day)' %}

with cte as (
    select
        t.org_id,
        t.site_id,
        prev_sl.id as service_line_id,
        prev_sl.name as service_line_name,
        t.phase_type_id,
        t.phase_duration_minutes,
        if({{ num_days_ago }} < 28, true, false) as in_last_month,
        if({{ num_days_ago }} < 28 * 3, true, false) as in_last_3_months
    from {{ ref("core_turnover_phases") }} as t
    inner join {{ api_table_snapshot("bronze", "public_cases", alias="prev_c") }}
        on t.prev_apella_case_id = prev_c.case_id
    inner join {{ api_table_snapshot("bronze", "public_cases", alias="next_c") }}
        on t.next_apella_case_id = next_c.case_id
    inner join {{ api_table_snapshot("bronze", "public_service_lines", alias="prev_sl") }}
        on prev_c.service_line_id = prev_sl.id
    inner join {{ api_table_snapshot("bronze", "public_service_lines", alias="next_sl") }}
        on next_c.service_line_id = next_sl.id
    where
        t.ds = {{ ds() }}
        and next_sl.id = prev_sl.id
        and date(t.phase_start_datetime_local) = date(t.phase_end_datetime_local)
        and date(t.phase_start_datetime_local) > date_sub(current_date(), interval 28 * 3 day)
),

last_28_days_avg as (
    select
        org_id,
        site_id,
        phase_type_id,
        service_line_id,
        service_line_name,
        avg(phase_duration_minutes) as average_minutes_28_days,
        approx_quantiles(phase_duration_minutes, 100)[50] as median_minutes_28_days
    from cte
    where in_last_month is true
    group by org_id, site_id, phase_type_id, service_line_id, service_line_name
),

last_3_months_avg as (
    select
        org_id,
        site_id,
        phase_type_id,
        service_line_id,
        service_line_name,
        avg(phase_duration_minutes) as average_minutes_3_months,
        approx_quantiles(phase_duration_minutes, 100)[50] as median_minutes_3_months
    from cte
    where in_last_3_months is true
    group by org_id, site_id, phase_type_id, service_line_id, service_line_name
)

select
    {{ ds() }} as ds,
    last_28_days_avg.*,
    last_3_months_avg.average_minutes_3_months,
    last_3_months_avg.median_minutes_3_months
from last_28_days_avg
inner join last_3_months_avg
    on
        last_28_days_avg.org_id = last_3_months_avg.org_id
        and last_28_days_avg.site_id = last_3_months_avg.site_id
        and last_28_days_avg.phase_type_id = last_3_months_avg.phase_type_id
        and last_28_days_avg.service_line_id = last_3_months_avg.service_line_id
        and last_28_days_avg.service_line_name = last_3_months_avg.service_line_name
