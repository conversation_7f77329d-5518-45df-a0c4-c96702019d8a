version: 1

models:
  - name: historical_cases
    description: "Historical cases table that has any known erroneous data removed."
    columns:
      - name: ds
        data_type: "timestamp"
      - name: org_id
        data_type: "string"
      - name: file_case_id
        data_type: "string"
      - name: room
        data_type: "string"
      - name: site
        data_type: "string"
      - name: scheduled_start_datetime_local
        data_type: "timestamp"
      - name: scheduled_end_datetime_local
        data_type: "timestamp"
      - name: actual_start_datetime_local
        data_type: "timestamp"
      - name: actual_end_datetime_local
        data_type: "timestamp"
      - name: case_class
        data_type: "string"
      - name: patient_class
        data_type: "string"
      - name: is_add_on
        data_type: "boolean"
      - name: primary_surgeon
        data_type: "string"
      - name: primary_procedure_name
        data_type: "string"
        description: Cleaned string of the primary procedure name.
      - name: full_procedure_name
        data_type: "string"
        description: Original string of the primary procedure name, as provided by the customer.
      - name: all_procedure_names
        data_type: "array<string>"
        description: A list containing all procedures, in order as provided by the customer. If null, the customer has not provided more procedure info outside of the primary procedure or the provided procedure info could not be normalized.
      - name: procedure_count
        data_type: "bigint"
      - name: source_file_date_utc
        data_type: "timestamp"
      - name: service_line
        data_type: "string"
      - name: source_file_gcs_path
        data_type: "string"
      - name: cancellation_reason
        data_type: "string"
        description:
          "represents rescheduling or modifying a case rather than cancelling the entire case.
          Cases with a non-null cancellation_reason usually have actual start and end time. See the following relevant slack thread:
          https://apella-workspace.slack.com/archives/C04KQAMHNTV/p1734129500891859"

  - name: historical_cases_normalized
    description: "Historical cases table that is normalized to look like the API data."
    columns:
      - name: ds
        data_type: "timestamp"
      - name: org_id
        data_type: "string"
      - name: file_case_id
        data_type: "string"
      - name: customer_room_name
        data_type: "string"
        description: The room as provided by the customer in the historical data file.
      - name: apella_room_id
        data_type: "string"
        description: The normalized room ID. If null, was unable to normalize.
      - name: customer_site_name
        data_type: "string"
        description: The site as provided by the customer in the historical data file.
      - name: apella_site_name
        data_type: "string"
        description: The normalized site name. If null, was unable to normalize.
      - name: apella_site_id
        data_type: "string"
        description: The normalized site ID. If null, was unable to normalize.
      - name: scheduled_start_datetime_local
        data_type: "timestamp"
      - name: scheduled_end_datetime_local
        data_type: "timestamp"
      - name: actual_start_datetime_local
        data_type: "timestamp"
      - name: actual_end_datetime_local
        data_type: "timestamp"
      - name: case_class
        data_type: "string"
      - name: case_classification_types_id
        data_type: "string"
        description: Normalized case classification ID, if provided.
      - name: patient_class
        data_type: "string"
      - name: is_add_on
        data_type: "boolean"
      - name: primary_surgeon
        data_type: "string"
      - name: primary_procedure_name
        data_type: "string"
        description: Cleaned string of the primary procedure name.
      - name: full_procedure_name
        data_type: "string"
        description: Original string of the primary procedure name, as provided by the customer.
      - name: all_procedure_names
        data_type: "array<string>"
        description: A list containing all procedures, in order as provided by the customer. If null, the customer has not provided more procedure info outside of the primary procedure or the provided procedure info could not be normalized.
      - name: procedure_count
        data_type: "bigint"
      - name: source_file_date_utc
        data_type: "timestamp"
      - name: service_line
        data_type: "string"
      - name: source_file_gcs_path
        data_type: "string"
      - name: cancellation_reason
        data_type: "string"
      - name: primary_surgeon_staff_id
        data_type: "string"
        description: The staff_id of the primary_surgeon. (As of Oct 2024, primary_surgeon_staff_id is only available for tampa_general)
      - name: apella_staff_id
        data_type: "string"
        description: The normalized staff ID. If null, was unable to match on the primary_surgeon_staff_id or the primary_surgeon's name.

  - name: scheduled_cases_asof_the_day_before_surgery
    description: "The schema is the same as in api table cases_history. However, in this table, there is only one row per case_id. This table tries to represent the view of the scheduled that they had either the night before or at 5-6AM before prime time starts. We only have cases that were scheduled the day before surgery, and we only have the last version available for each of such cases at that point in time"
    columns:
      - name: ds
        data_type: "timestamp"
      - name: created_time
        data_type: "timestamp"
      - name: updated_time
        data_type: "timestamp"
      - name: case_id
        data_type: "string"
      - name: external_case_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: case_classification_data_types_id
        data_type: "string"
      - name: scheduled_start_time
        data_type: "timestamp"
      - name: scheduled_end_time
        data_type: "timestamp"
      - name: status
        data_type: "string"
      - name: service_line_id
        data_type: "string"
      - name: is_add_on
        data_type: "boolean"
      - name: org_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: version
        data_type: "int"
      - name: patient_class
        data_type: "string"
      - name: cancellation_reason
        data_type: "json"
      - name: case_matching_status
        data_type: "string"
