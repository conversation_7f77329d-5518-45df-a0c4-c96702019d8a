-- We filter out erroneous cases:
--    Overlapping cases in the same room.
--      See https://linear.app/apella/issue/DATA-1822/overlapping-cases-in-the-same-room
--    Cases without procedure names.

{{ daily_config() }}

with latest_standardized_historical_cases as (
    select
        org_id,
        file_case_id,
        room,
        site,
        scheduled_start_datetime_local,
        scheduled_end_datetime_local,
        actual_start_datetime_local,
        actual_end_datetime_local,
        case_class,
        patient_class as patient_class,
        is_add_on,
        primary_surgeon,
        primary_procedure_name,
        full_procedure_name,
        all_procedure_names,
        procedure_count,
        source_file_date_utc,
        service_line,
        source_file_gcs_path,
        cancellation_reason,
        primary_surgeon_staff_id
    from {{ source("silver", "historical_cases_merged") }}
    where
        ds = {{ ds() }}
        and full_procedure_name is not null
        and not (org_id = 'lifebridge' and procedure_count = 0)
),

case_ids_with_overlapping_cases_in_file as (
    select
        historical_cases.file_case_id,
        historical_cases.source_file_gcs_path
    from
        latest_standardized_historical_cases as historical_cases
    inner join
        latest_standardized_historical_cases as overlapping_cases
        on
            historical_cases.file_case_id != overlapping_cases.file_case_id
            and historical_cases.room = overlapping_cases.room
            and historical_cases.source_file_gcs_path = overlapping_cases.source_file_gcs_path
            and historical_cases.site = overlapping_cases.site
            and historical_cases.org_id = overlapping_cases.org_id
            and (
                historical_cases.actual_start_datetime_local < overlapping_cases.actual_end_datetime_local
                and historical_cases.actual_end_datetime_local > overlapping_cases.actual_start_datetime_local
            )
)

select
    {{ ds() }} as ds,
    historical_cases.org_id,
    historical_cases.file_case_id,
    historical_cases.room,
    historical_cases.site,
    historical_cases.scheduled_start_datetime_local,
    historical_cases.scheduled_end_datetime_local,
    historical_cases.actual_start_datetime_local,
    historical_cases.actual_end_datetime_local,
    historical_cases.case_class,
    historical_cases.patient_class,
    historical_cases.is_add_on,
    historical_cases.primary_surgeon,
    historical_cases.primary_procedure_name,
    historical_cases.full_procedure_name,
    historical_cases.all_procedure_names,
    historical_cases.procedure_count,
    historical_cases.source_file_date_utc,
    historical_cases.service_line,
    historical_cases.source_file_gcs_path,
    historical_cases.cancellation_reason,
    historical_cases.primary_surgeon_staff_id
from latest_standardized_historical_cases as historical_cases
left outer join case_ids_with_overlapping_cases_in_file
    on
        historical_cases.file_case_id = case_ids_with_overlapping_cases_in_file.file_case_id
        and historical_cases.source_file_gcs_path
        = case_ids_with_overlapping_cases_in_file.source_file_gcs_path
where case_ids_with_overlapping_cases_in_file.file_case_id is null
