{{ daily_config() }}

with latest_historical_cases as (
    select
        org_id,
        file_case_id,
        room,
        site,
        scheduled_start_datetime_local,
        scheduled_end_datetime_local,
        actual_start_datetime_local,
        actual_end_datetime_local,
        case_class,
        patient_class,
        is_add_on,
        primary_surgeon,
        primary_procedure_name,
        full_procedure_name,
        all_procedure_names,
        procedure_count,
        source_file_date_utc,
        service_line,
        source_file_gcs_path,
        cancellation_reason,
        primary_surgeon_staff_id,
        case
            when
                source_file_gcs_path
                in (
                    'prod-data-warehouse/historical_cases/'
                    || 'houston_methodist/Apella_Historical_Cases_HMSJ_LD_20240308.csv',
                    'prod-data-warehouse/historical_cases/houston_methodist/'
                    || 'Apella_Historical_Cases_HMW_LD_20240308.csv',
                    'prod-data-warehouse/historical_cases/houston_methodist/'
                    || 'Apella_Historical_Cases_HMTW_LD_20240308.csv',
                    'prod-data-warehouse/historical_cases/houston_methodist/'
                    || 'Apella_Historical_Cases_HMWB_LD_20240308.csv'
                )
                then site || ' ' || room
            else room
        end as room_lookup
    from {{ ref("historical_cases") }}
    where
        ds = {{ ds() }}
),

normalized_id_mapping as (
    select
        mapping.internal_id,
        mapping.external_id,
        replace(mapping.external_id_type, '::room_mapping', '') as org_id
    from {{ api_table_snapshot("bronze", "public_identifier_mapping", 'mapping') }}
    where
        mapping.external_id_type like '%::room_mapping'
),

historical_cases_normalized as (
    select
        {{ ds() }} as ds,
        latest_historical_cases.org_id,
        latest_historical_cases.file_case_id,
        latest_historical_cases.room as customer_room_name,
        normalized_id_mapping.internal_id as apella_room_id,
        latest_historical_cases.site as customer_site_name,
        sites.name as apella_site_name,
        rooms.site_id as apella_site_id,
        latest_historical_cases.scheduled_start_datetime_local,
        latest_historical_cases.scheduled_end_datetime_local,
        latest_historical_cases.actual_start_datetime_local,
        latest_historical_cases.actual_end_datetime_local,
        latest_historical_cases.case_class,
        case
            when latest_historical_cases.case_class = 'emergency'
                then 'CASE_CLASSIFICATION_EMERGENT'
            else concat('CASE_CLASSIFICATION_', upper(latest_historical_cases.case_class))
        end as case_classification_types_id,
        latest_historical_cases.patient_class,
        latest_historical_cases.is_add_on,
        latest_historical_cases.primary_surgeon,
        upper(latest_historical_cases.primary_procedure_name) as primary_procedure_name,
        latest_historical_cases.full_procedure_name,
        array(
            select upper(procedure_name)
            from unnest(latest_historical_cases.all_procedure_names) as procedure_name
        ) as all_procedure_names,
        latest_historical_cases.procedure_count,
        latest_historical_cases.source_file_date_utc,
        latest_historical_cases.service_line,
        latest_historical_cases.source_file_gcs_path,
        latest_historical_cases.cancellation_reason,
        latest_historical_cases.primary_surgeon_staff_id
    from latest_historical_cases
    left outer join normalized_id_mapping
        on
            latest_historical_cases.org_id = normalized_id_mapping.org_id
            and latest_historical_cases.room_lookup = normalized_id_mapping.external_id
    left outer join {{ api_table_snapshot("bronze", "public_rooms", "rooms") }}
        on normalized_id_mapping.internal_id = rooms.id
    left outer join {{ api_table_snapshot("bronze", "public_sites", "sites") }}
        on
            rooms.site_id = sites.id
),

public_staff as (
    select
        id,
        org_id,
        external_staff_id,
        updated_time,
        trim(lower(first_name)) as first_name,
        trim(lower(last_name)) as last_name
    from
        {{ source('bronze', 'public_staff') }}
    where
        archived_time is null
),

ranked_staff as (
    select
        id,
        org_id,
        first_name,
        last_name,
        external_staff_id,
        row_number() over (partition by org_id, first_name, last_name order by updated_time desc) as ranking
    from public_staff
),

unique_external_id_staff as (
    select
        id,
        org_id,
        first_name,
        last_name,
        external_staff_id
    from ranked_staff where ranking = 1
),

-- First try matching on org_id and external staff ID
matched_by_id as (
    select
        h.*,
        p.id as id_matched_apella_staff_id
    from
        historical_cases_normalized as h
    left outer join unique_external_id_staff as p
        on
            h.org_id = p.org_id
            and h.primary_surgeon_staff_id = p.external_staff_id
    where h.ds = {{ ds() }}
),

-- For records without a match, try matching on name
matched_by_name as (
    select
        h.*,
        p.id as name_matched_apella_staff_id
    from
        matched_by_id as h
    left outer join unique_external_id_staff as p
        on
            h.org_id = p.org_id
            and lower(h.primary_surgeon.first_name) = p.first_name
            and lower(h.primary_surgeon.last_name) = p.last_name
    where h.id_matched_apella_staff_id is null
),

-- Combine the matches, preferring external_staff_id matches over name matches
combined as (
    select
        h.org_id,
        h.file_case_id,
        h.customer_room_name,
        h.apella_room_id,
        h.customer_site_name,
        h.apella_site_name,
        h.apella_site_id,
        h.scheduled_start_datetime_local,
        h.scheduled_end_datetime_local,
        h.actual_start_datetime_local,
        h.actual_end_datetime_local,
        h.case_class,
        h.case_classification_types_id,
        h.patient_class,
        h.is_add_on,
        h.primary_surgeon,
        h.primary_procedure_name,
        h.full_procedure_name,
        h.all_procedure_names,
        h.procedure_count,
        h.source_file_date_utc,
        h.service_line,
        h.source_file_gcs_path,
        h.cancellation_reason,
        h.primary_surgeon_staff_id,
        h.id_matched_apella_staff_id as apella_staff_id
    from
        matched_by_id as h
    where
        h.id_matched_apella_staff_id is not null
    union all
    select
        m.org_id,
        m.file_case_id,
        m.customer_room_name,
        m.apella_room_id,
        m.customer_site_name,
        m.apella_site_name,
        m.apella_site_id,
        m.scheduled_start_datetime_local,
        m.scheduled_end_datetime_local,
        m.actual_start_datetime_local,
        m.actual_end_datetime_local,
        m.case_class,
        m.case_classification_types_id,
        m.patient_class,
        m.is_add_on,
        m.primary_surgeon,
        m.primary_procedure_name,
        m.full_procedure_name,
        m.all_procedure_names,
        m.procedure_count,
        m.source_file_date_utc,
        m.service_line,
        m.source_file_gcs_path,
        m.cancellation_reason,
        m.primary_surgeon_staff_id,
        m.name_matched_apella_staff_id as apella_staff_id
    from
        matched_by_name as m
)

select
    {{ ds() }} as ds,
    *
from combined
