{{ daily_config() }}

with last_version_the_day_before_surgery as (
    -- this CTE removes all rows from cases_history where the case was updated on the
    -- scheduled day or after
    select
        ch.case_id,
        max(ch.version) as version_
    from {{ api_table_snapshot("bronze", "public_cases_history", "ch") }}
    inner join {{ api_table_snapshot("bronze", "public_sites", "s") }}
        on ch.site_id = s.id
    where
        date(ch.updated_time, s.timezone) < date(ch.scheduled_start_time, s.timezone)
    group by ch.case_id
)

select
    {{ ds() }} as ds,
    ch.*
from {{ api_table_snapshot("bronze", "public_cases_history", "ch") }}
inner join last_version_the_day_before_surgery
    on
        ch.case_id = last_version_the_day_before_surgery.case_id
        and ch.version = last_version_the_day_before_surgery.version_
        and ch.status = "scheduled"
