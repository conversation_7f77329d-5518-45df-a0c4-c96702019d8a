{{ daily_config() }}

-- This unions the models that calculate notification events

with all_notifications as (
    select
        environment,
        id,
        event_type_id,
        confidence,
        org_id,
        site_id,
        room_id,
        start_time,
        created_time,
        updated_time,
        prediction_version
    from {{ ref("event_model_notification_events_before_wait_for_high_confidence_heuristic") }}
    where ds = {{ ds() }}
    union all
    select
        environment,
        id,
        event_type_id,
        confidence,
        org_id,
        site_id,
        room_id,
        start_time,
        created_time,
        updated_time,
        prediction_version
    from {{ ref("event_model_notification_events_after_wait_for_high_confidence_heuristic") }}
    where ds = {{ ds() }}
)

select
    {{ ds() }} as ds,
    environment,
    id,
    event_type_id,
    confidence,
    org_id,
    site_id,
    room_id,
    start_time,
    created_time,
    updated_time,
    prediction_version
from all_notifications
