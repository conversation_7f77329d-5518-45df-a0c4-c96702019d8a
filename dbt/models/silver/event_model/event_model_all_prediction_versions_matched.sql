{{ daily_config() }}

with prediction_events as (
    select
        *,
        'notification_event' as slo_category
    from {{ ref("event_model_notification_events") }}
    where ds = {{ ds() }}
    union all
    select
        *,
        'schedule_event' as slo_category
    from {{ ref("event_model_schedule_events") }}
    where ds = {{ ds() }}
),

ground_truth_events as (
    -- For the ground truth, we will be fetching just the latest version of all events that the
    -- annotators have created.  We can easily get by querying the public_events table for
    -- non-deleted human_gt events
    select
        environment,
        slo_category,
        event_type_id,
        org_id,
        site_id,
        room_id,
        start_time,
        id
    from {{ api_table_snapshot("bronze", "public_events") }}
    -- And we cross join with the environment because we want to do the same analysis for
    -- both the production and the shadow environments
    cross join unnest(['{{ target.name }}', 'shadow']) as environment
    cross join unnest(['notification_event', 'schedule_event']) as slo_category
    where
        source_type = 'human_gt'
        and deleted_at is null
),

events_matched as (
    -- Now, we match all versions of the event predictions to the final ground truth using
    -- a matching_minutes (or temporal allowance) of 10
    select
        -- Ground truth specific information
        ground_truth_events.id as ground_truth_id,
        ground_truth_events.start_time as ground_truth_event_time,
        -- Prediction specific information
        prediction_events.start_time as prediction_event_time,
        prediction_events.updated_time as prediction_updated_time,
        prediction_events.confidence as confidence,
        prediction_events.id as prediction_id,
        prediction_events.prediction_version as prediction_version,
        -- Context information
        coalesce(ground_truth_events.environment, prediction_events.environment) as environment,
        coalesce(ground_truth_events.slo_category, prediction_events.slo_category) as slo_category,
        coalesce(ground_truth_events.org_id, prediction_events.org_id) as org_id,
        coalesce(ground_truth_events.site_id, prediction_events.site_id) as site_id,
        coalesce(ground_truth_events.room_id, prediction_events.room_id) as room_id,
        coalesce(ground_truth_events.event_type_id, prediction_events.event_type_id) as event_type_id,
        coalesce(ground_truth_events.start_time, prediction_events.start_time) as event_time,
        -- The time error is the absolute difference between the ground truth and prediction times
        -- if they are both present
        case
            when ground_truth_events.id is not null and prediction_events.id is not null
                then abs(timestamp_diff(ground_truth_events.start_time, prediction_events.start_time, second))
        end as time_error,
        -- The latency is the time it took the prediction to be updated after the ground truth
        -- OR, if there is no ground truth, then the FIRST time this prediction id was made
        case
            when ground_truth_events.id is not null and prediction_events.id is not null
                then timestamp_diff(prediction_events.updated_time, ground_truth_events.start_time, second)
            when ground_truth_events.id is null
                then timestamp_diff(prediction_events.updated_time, prediction_events.created_time, second)
        end as latency
    from prediction_events
    full outer join ground_truth_events on (
        prediction_events.environment = ground_truth_events.environment
        and prediction_events.slo_category = ground_truth_events.slo_category
        and prediction_events.room_id = ground_truth_events.room_id
        and prediction_events.event_type_id = ground_truth_events.event_type_id
        and (prediction_events.start_time - interval 10 minute) <= ground_truth_events.start_time
        and (prediction_events.start_time + interval 10 minute) >= ground_truth_events.start_time
    )
),

-- The above matching can match multiple predictions to the same ground truth.
-- To prevent this we rank each match by how close they are to each other
events_matched_ranked as (
    select
        *,
        row_number()
            over (
                partition by ground_truth_id, environment, slo_category
                order by
                    time_error asc,
                    /* for tie breaking */
                    prediction_updated_time asc,
                    prediction_id asc,
                    prediction_version asc
            )
            as gt_rank,
        row_number()
            over (
                partition by prediction_id, environment, slo_category
                order by
                    time_error asc,
                    /* for tie breaking */
                    prediction_updated_time asc,
                    ground_truth_id asc,
                    prediction_version asc
            )
            as pred_rank
    from events_matched
),

-- Then, the true positives are only the ones where the ground truth and prediction
-- are the closest pairs (rank 1 for both)
true_positives as (
    select distinct
        environment,
        slo_category,
        org_id,
        site_id,
        room_id,
        event_type_id,
        event_time,
        ground_truth_id,
        ground_truth_event_time,
        prediction_id,
        prediction_event_time,
        prediction_updated_time,
        prediction_version,
        confidence,
        time_error,
        latency,
        'true_positive' as prediction_class
    from events_matched_ranked
    where
        prediction_id is not null
        and ground_truth_id is not null
        and gt_rank = 1
        and pred_rank = 1
),

false_positives as (
    select distinct
        events_matched.environment,
        events_matched.slo_category,
        events_matched.org_id,
        events_matched.site_id,
        events_matched.room_id,
        events_matched.event_type_id,
        events_matched.prediction_event_time as event_time,
        cast(null as string) as ground_truth_id,
        cast(null as timestamp) as ground_truth_event_time,
        events_matched.prediction_id,
        events_matched.prediction_event_time,
        events_matched.prediction_updated_time,
        events_matched.prediction_version,
        events_matched.confidence,
        cast(null as int64) as time_error,
        cast(null as int64) as latency,
        'false_positive' as prediction_class
    from events_matched
    full outer join true_positives
        on
            events_matched.prediction_id = true_positives.prediction_id
            and events_matched.environment = true_positives.environment
            and events_matched.slo_category = true_positives.slo_category
    where
        -- False positives are predictions that not found in true positives
        events_matched.prediction_id is not null
        and true_positives.prediction_id is null
),

false_negatives as (
    select distinct
        events_matched.environment,
        events_matched.slo_category,
        events_matched.org_id,
        events_matched.site_id,
        events_matched.room_id,
        events_matched.event_type_id,
        events_matched.ground_truth_event_time as event_time,
        events_matched.ground_truth_id,
        events_matched.ground_truth_event_time,
        cast(null as string) as prediction_id,
        cast(null as timestamp) as prediction_event_time,
        cast(null as timestamp) as prediction_updated_time,
        cast(null as int64) as prediction_version,
        cast(null as float64) as confidence,
        cast(null as int64) as time_error,
        cast(null as int64) as latency,
        'false_negative' as prediction_class
    from events_matched
    full outer join true_positives
        on
            events_matched.ground_truth_id = true_positives.ground_truth_id
            and events_matched.environment = true_positives.environment
            and events_matched.slo_category = true_positives.slo_category
    where
        -- False negatives are ground_truth that are not found in true positives
        events_matched.ground_truth_id is not null
        and true_positives.ground_truth_id is null
),

events_classified as (
    select * from true_positives
    union all
    select * from false_positives
    union all
    select * from false_negatives
)

select
    {{ ds() }} as ds,
    environment,
    slo_category,
    org_id,
    site_id,
    room_id,
    event_type_id,
    event_time,
    ground_truth_id,
    ground_truth_event_time,
    prediction_id,
    prediction_event_time,
    prediction_updated_time,
    prediction_version,
    confidence,
    time_error,
    latency,
    prediction_class,
    case
        when time_error is not null then abs(time_error)
        else 9000
    end as abs_time_error_with_null_as_9000
from events_classified
