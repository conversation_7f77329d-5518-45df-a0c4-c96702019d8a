{{ daily_config() }}

with misclassified_events as (
    select id
    from {{ api_table_snapshot("bronze", "public_events_history") }}
    group by id
    having count(distinct event_type_id) > 1
),

event_id_changes as (
    select
        eh.id,
        eh.event_type_id,
        eh.version,
        lag(
            eh.version) over
        (partition by eh.id order by eh.version asc) as preceding_version,
        lag(
            eh.event_type_id) over
        (partition by eh.id order by eh.version asc) as preceding_event_type_id
    from misclassified_events
    left outer join {{ api_table_snapshot("bronze", "public_events_history", 'eh') }}
        on misclassified_events.id = eh.id
)

select
    {{ ds() }} as ds,
    id,
    event_type_id,
    version,
    preceding_version,
    preceding_event_type_id
from event_id_changes
where
    version = 1
    or
    event_type_id != preceding_event_type_id
