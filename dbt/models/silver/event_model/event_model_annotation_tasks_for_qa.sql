{{ daily_config() }}

-- First, we find all completed annotation tasks
with completed_annotation_tasks as (
    select
        tasks.id,
        tasks.type_id,
        tasks.type_version,
        tasks.room_id,
        tasks.start_time,
        tasks.end_time
    from {{ api_table_snapshot("bronze", "public_annotation_tasks", alias='tasks') }}
    where tasks.status = 'DONE'
),

-- Next, we get all the task type history
task_type_history as (
    select
        id as type_id,
        version,
        optimize_tasks,
        -- Parse the event_types as an array
        json_value_array(event_types) as event_types
    from {{ api_table_snapshot("bronze", "public_annotation_task_types_history") }}
),

-- We join with the task with the type info from that version of the task
completed_annotation_tasks_with_type_info as (
    select
        completed_annotation_tasks.id,
        completed_annotation_tasks.room_id,
        completed_annotation_tasks.start_time,
        completed_annotation_tasks.end_time,
        task_type_history.event_types,
        task_type_history.optimize_tasks is true as optimized
    from completed_annotation_tasks
    left outer join task_type_history
        on (
            completed_annotation_tasks.type_id = task_type_history.type_id
            and completed_annotation_tasks.type_version = task_type_history.version
        )
),

-- Then, join with our annotation_task_determinations table to see if the task is a qa task
completed_annotation_tasks_with_determination as (
    select
        tasks.id,
        tasks.room_id,
        tasks.start_time,
        tasks.end_time,
        tasks.event_types,
        tasks.optimized,
        task_determinations.is_qa as is_qa
    from completed_annotation_tasks_with_type_info as tasks
    left outer join {{ ref("annotation_task_determinations_incremental") }} as task_determinations
    -- Since the source table is incremental, but this table is not, we do not
        -- filter by ds.
        on tasks.id = task_determinations.task_id
),

-- We then use that determine if this task is for QA or not.
completed_tasks_with_qa_info as (
    select
        tasks.id,
        tasks.room_id,
        tasks.start_time,
        tasks.end_time,
        tasks.event_types,
        tasks.optimized,
        case
            -- If we have found this task in our task determination table, then we can use
            -- that to determine if it is for QA
            when tasks.is_qa is not null then is_qa
            -- Else, we need to fall back to the older logic for determining if a task is for QA
            -- which is from this PR:
            -- https://github.com/Apella-Technology/annotation-task-optimizer/pull/20
            -- The original compares if any part of the task is in a QA day, this model only
            -- compares if the start time of the task is in a QA day.  This will effectively filter
            -- out any data where the task starts before midnight (in the site's timezone) on a
            -- non-qa day, and ends after midnight on a qa day.  Luckily, there are few events
            -- that fall into that category, and so the effect will be negligible.
            else
                mod(
                    abs(
                        farm_fingerprint(
                            concat(
                                tasks.room_id,
                                cast(
                                    timestamp(
                                        date(tasks.start_time), sites.timezone
                                    ) as string
                                )
                            )
                        )
                    ),
                    -- 100 allows us to reduce this down to 1%, if needed.
                    100
                ) < 10 -- we used 10% for QA
        end as for_qa
    from completed_annotation_tasks_with_determination as tasks
    left outer join {{ api_table_snapshot("bronze", "public_rooms", alias='rooms') }}
        on tasks.room_id = rooms.id
    left outer join {{ api_table_snapshot("bronze", "public_sites", alias='sites') }}
        on rooms.site_id = sites.id
),

-- Finally, in order to calculate unbiased metrics, we calculate a "metric multiplier".
-- This allows QA tasks to be counted 10x as much as non-QA tasks, so that non-optimized sites
-- don't skew the metrics.
tasks_with_metric_weight as (
    select
        tasks.id,
        tasks.room_id,
        tasks.start_time,
        tasks.end_time,
        tasks.event_types,
        -- To undo the bias caused by non-optimized task types, we assign a metric_weight
        case
            -- When a task is optimized and for QA, then we can use it for metrics, but since
            -- we have 10% of the tasks for QA, we need to multiply it by 10 to get the same
            -- totals.
            -- NOTE: We cannot change our QA %, because this 10x is hard-coded.  We would need
            -- to record what % we sampled for QA.
            when optimized and for_qa then 10.0
            -- When a task is not optimized and for QA, then we can use it for metrics, since it
            -- is not biased by the optimization.
            when not optimized then 1.0
            -- Else, the task must be optimized, but not a QA task, which means that it is
            -- inherently biased.  So we have a multiplier of 0.
            else 0.0
        end as metric_weight
    from completed_tasks_with_qa_info as tasks
)

select
    {{ ds() }} as ds,
    id,
    room_id,
    start_time,
    end_time,
    event_types,
    metric_weight
from tasks_with_metric_weight
