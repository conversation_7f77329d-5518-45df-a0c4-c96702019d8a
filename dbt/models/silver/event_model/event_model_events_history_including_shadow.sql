{{ daily_config() }}

-- We want to also analyze shadow, so we need to get the shadow predictions from a pubsub to bigquery
-- table, and union them with the predictions from the public_events_history table.

select
    {{ ds() }} as ds,
    'shadow' as environment,
    id,
    event_type_id,
    confidence,
    org_id,
    site_id,
    room_id,
    start_time,
    created_time,
    updated_time,
    deleted_at,
    prediction_version
from {{ ref("event_model_shadow_prediction_events") }}
where ds = {{ ds() }}
union all
select
    {{ ds() }} as ds,
    '{{ target.name }}' as environment,
    id,
    event_type_id,
    confidence,
    org_id,
    site_id,
    room_id,
    start_time,
    created_time,
    updated_time,
    deleted_at,
    version as prediction_version
from {{ api_table_snapshot("bronze", "public_events_history") }}
where
    source_type = 'prediction'
