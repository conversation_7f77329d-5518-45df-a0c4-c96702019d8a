{{ daily_config() }}

with shadow_prediction_events as (
    -- We select all the fields from the event updates table, but then rename them as if they
    -- came from the events_history table, for consistency.  However, this does not have 'created_time',
    -- so we add that in the next CTE.
    select
        event_id as id,
        event_name as event_type_id,
        confidence,
        org_id,
        site_id,
        room_id,
        event_timestamp as start_time,
        process_timestamp as updated_time,
        row_number() over (partition by event_id order by process_timestamp asc) as prediction_version,
        case when update_action = 'delete' then process_timestamp end as deleted_at
    from {{ api_table_snapshot("ml_project", "event_prediction_event_updates-shadow",
        alias="image_processing_output") }}
),

shadow_prediction_events_with_created_time as (
    select
        shadow_prediction_events.*,
        first_prediction.updated_time as created_time
    from shadow_prediction_events as shadow_prediction_events
    left outer join shadow_prediction_events as first_prediction
        on shadow_prediction_events.id = first_prediction.id and first_prediction.prediction_version = 1
)

select
    {{ ds() }} as ds,
    id,
    event_type_id,
    confidence,
    org_id,
    site_id,
    room_id,
    start_time,
    updated_time,
    created_time,
    prediction_version,
    deleted_at
from shadow_prediction_events_with_created_time
