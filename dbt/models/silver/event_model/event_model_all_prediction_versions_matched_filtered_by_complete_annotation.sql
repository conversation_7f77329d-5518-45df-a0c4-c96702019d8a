{{ daily_config() }}


with events_matched as (
    select
        environment,
        slo_category,
        org_id,
        site_id,
        room_id,
        event_type_id,
        event_time,
        ground_truth_id,
        ground_truth_event_time,
        prediction_id,
        prediction_event_time,
        prediction_updated_time,
        prediction_version,
        confidence,
        time_error,
        latency,
        prediction_class,
        abs_time_error_with_null_as_9000
    from {{ ref("event_model_all_prediction_versions_matched") }}
    where ds = {{ ds() }}
),

events_matched_that_were_reviewed as (
    select distinct
        events_matched.*,
        -- If there are multiple completed tasks for the same event, and they have different
        -- metric weights, we do not want to double count the event.
        -- So, we take the maximum metric weight for this event type using
        -- qualify by below.
        metric_weight
    from events_matched
    inner join {{ ref("event_model_annotation_tasks_for_qa") }} as completed_tasks
        on (
            events_matched.room_id = completed_tasks.room_id
            and events_matched.event_time >= completed_tasks.start_time
            and events_matched.event_time <= completed_tasks.end_time
            and events_matched.event_type_id in unnest(completed_tasks.event_types)
            and completed_tasks.metric_weight > 0
            and completed_tasks.ds = {{ ds() }}
        )
    qualify
        row_number() over (
            partition by ground_truth_id, prediction_id, slo_category
            order by metric_weight desc
        ) = 1
)

select
    {{ ds() }} as ds,
    environment,
    slo_category,
    org_id,
    site_id,
    room_id,
    event_type_id,
    event_time,
    ground_truth_id,
    ground_truth_event_time,
    prediction_id,
    prediction_event_time,
    prediction_updated_time,
    prediction_version,
    confidence,
    time_error,
    latency,
    prediction_class,
    abs_time_error_with_null_as_9000,
    metric_weight
from events_matched_that_were_reviewed
