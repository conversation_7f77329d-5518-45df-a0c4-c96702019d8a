{{ daily_config() }}

-- We used to have a heuristic in the notification system that would wait up to 3 minutes
-- for low confidence events before sending the notification.  This heuristic was moved from
-- the notification system to the event model.  So to get accurate metrics for notification events
-- before the heuristic, we simulate that heuristic here.

-- That heuristic was deployed at '2024-07-01T19:30:00+00:00'

with high_confidence_predictions as (
    select distinct
        environment,
        id,
        event_type_id,
        confidence,
        org_id,
        site_id,
        room_id,
        start_time,
        created_time,
        updated_time,
        deleted_at,
        prediction_version
    from {{ ref("event_model_events_history_including_shadow") }}
    where
        ds = {{ ds() }}
        and start_time < '2024-07-01T19:30:00+00:00'
        and confidence > 0.8
        and deleted_at is null
    qualify
        -- Get the first version
        row_number() over (
            partition by id
            order by prediction_version asc
        ) = 1
),

three_minute_predictions as (
    select distinct
        environment,
        id,
        event_type_id,
        confidence,
        org_id,
        site_id,
        room_id,
        start_time,
        created_time,
        updated_time,
        deleted_at,
        prediction_version
    from {{ ref("event_model_events_history_including_shadow") }}
    where
        ds = {{ ds() }}
        and start_time < '2024-07-01T19:30:00+00:00'
        -- Maybe this should account for the timestamp that notification system runs at
        -- but that is probably overly complicated
        and timestamp_diff(updated_time, created_time, second) <= 180
    qualify
        -- Get the LAST version before 3 minutes
        row_number() over (
            partition by id
            order by prediction_version desc
        ) = 1
),

predictions_unioned as (
    select * from high_confidence_predictions
    union all
    select * from three_minute_predictions
    where deleted_at is null
),

first_prediction_that_meets_notification_criteria_per_id as (
    select distinct * from predictions_unioned
    qualify
        row_number() over (
            partition by id
            order by updated_time asc
        ) = 1
)

select
    {{ ds() }} as ds,
    environment,
    id,
    event_type_id,
    confidence,
    org_id,
    site_id,
    room_id,
    start_time,
    created_time,
    updated_time,
    prediction_version
from first_prediction_that_meets_notification_criteria_per_id
where deleted_at is null
