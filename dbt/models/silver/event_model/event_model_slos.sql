{{ daily_config(produce_latest_snapshot=True) }}

-- So that we only have to write the metric roll up once,
-- we union our different events by slo category
with all_events_matched as (
    -- These are the events grouped by just latency, across all sites
    select
        environment,
        slo_category,
        'all' as org_id,
        'all' as site_id,
        'all' as room_id,
        event_type_id,
        event_time,
        ground_truth_id,
        ground_truth_event_time,
        prediction_id,
        prediction_event_time,
        prediction_updated_time,
        prediction_version,
        confidence,
        time_error,
        abs_time_error_with_null_as_9000,
        latency,
        prediction_class,
        metric_weight
    from
        {{ ref("event_model_all_prediction_versions_matched_filtered_by_complete_annotation") }}
            as category_based_events
    where
        category_based_events.ds = {{ ds() }}
        -- We only want SLOs for prod
        and category_based_events.environment = '{{ target.name }}'
    -- We union them in again, but this time with the site id in the slo_category, to get the site level metrics
    union all
    select
        environment,
        slo_category,
        org_id,
        site_id,
        'all' as room_id,
        event_type_id,
        event_time,
        ground_truth_id,
        ground_truth_event_time,
        prediction_id,
        prediction_event_time,
        prediction_updated_time,
        prediction_version,
        confidence,
        time_error,
        abs_time_error_with_null_as_9000,
        latency,
        prediction_class,
        metric_weight
    from
        {{ ref("event_model_all_prediction_versions_matched_filtered_by_complete_annotation") }}
            as site_and_category_based_events
    where
        site_and_category_based_events.ds = {{ ds() }}
        -- We only want SLOs for prod
        and site_and_category_based_events.environment = '{{ target.name }}'
    -- We union them again, this time including room_id, to get room level metrics
    union all
    select
        environment,
        slo_category,
        org_id,
        site_id,
        room_id,
        event_type_id,
        event_time,
        ground_truth_id,
        ground_truth_event_time,
        prediction_id,
        prediction_event_time,
        prediction_updated_time,
        prediction_version,
        confidence,
        time_error,
        abs_time_error_with_null_as_9000,
        latency,
        prediction_class,
        metric_weight
    from
        {{ ref("event_model_all_prediction_versions_matched_filtered_by_complete_annotation") }}
            as room_and_category_based_events
    where
        room_and_category_based_events.ds = {{ ds() }}
        -- We only want SLOs for prod
        and room_and_category_based_events.environment = '{{ target.name }}'
),

all_events_with_time_windows as (
    select * from all_events_matched
    cross join unnest(['day', 'week', 'month', 'last_28_days']) as slo_time_granularity
),

all_events_classified as (
    -- Now we can classify those as true positive, false negative, false positive,
    -- and decide an "event_time" for rolling up the metrics
    select
        *,
        case
            when slo_time_granularity = 'day' then timestamp_trunc(event_time, day)
            when slo_time_granularity = 'week' then timestamp_trunc(event_time, week)
            when slo_time_granularity = 'month' then timestamp_trunc(event_time, month)
            when slo_time_granularity = 'last_28_days' then timestamp({{ ds() }})
        end as date_bucket
    from all_events_with_time_windows
    where
        slo_time_granularity in ('day', 'week', 'month')
        or (
            slo_time_granularity = 'last_28_days'
            and date(event_time) >= {{ ds() }} - interval 28 day
            and date(event_time) <= {{ ds() }}
        )
),


base_metrics as (
    -- Now we roll up the metrics per day
    select
        slo_category,
        slo_time_granularity,
        date_bucket,
        org_id,
        site_id,
        room_id,
        event_type_id,
        -- Use the metric_weight to weight the count metrics
        sum(if(prediction_class = 'true_positive', metric_weight, 0)) as true_positives,
        sum(if(prediction_class = 'false_positive', metric_weight, 0)) as false_positives,
        sum(if(prediction_class = 'false_negative', metric_weight, 0)) as false_negatives,
        -- Do not weight for the total count of events, so that this can still be accurate.
        -- So these two metrics should not be used when calculating other derived metrics.
        countif(prediction_class = 'true_positive' or prediction_class = 'false_negative') as total_ground_truth,
        countif(prediction_class = 'true_positive' or prediction_class = 'false_positive') as total_predictions,
        -- There isn't a way to use approx_quantiles with weights
        approx_quantiles(abs_time_error_with_null_as_9000, 100)[offset(95)] as time_error_for_95p_correctness,
        approx_quantiles(abs_time_error_with_null_as_9000, 100)[offset(99)] as time_error_for_99p_correctness,
        approx_quantiles(abs(time_error), 100)[offset(50)] as time_error_p50,
        approx_quantiles(abs(time_error), 100)[offset(95)] as time_error_p95,
        approx_quantiles(abs(time_error), 100)[offset(99)] as time_error_p99,
        approx_quantiles(latency, 100)[offset(50)] as latency_p50,
        approx_quantiles(latency, 100)[offset(95)] as latency_p95,
        approx_quantiles(latency, 100)[offset(99)] as latency_p99
    from all_events_classified
    group by slo_category, slo_time_granularity, date_bucket, event_type_id, org_id, site_id, room_id
),

precision_recall_metrics as (
    select
        *,
        case
            when true_positives + false_positives = 0 then 0
            else true_positives / (true_positives + false_positives)
        end as prec,
        case
            when true_positives + false_negatives = 0 then 0
            else true_positives / (true_positives + false_negatives)
        end as recall
    from base_metrics
),

correctness_f1_metrics as (
    select
        *,
        case
            when prec + recall = 0 then 0
            else (2 * prec * recall) / (prec + recall)
        end as f1_score,
        case
            when
                true_positives + false_positives + false_negatives = 0 then 0
            else true_positives / (true_positives + false_positives + false_negatives)
        end as correctness
    from precision_recall_metrics
)

select
    {{ ds() }} as ds,
    slo_category,
    slo_time_granularity,
    date_bucket,
    org_id,
    site_id,
    room_id,
    event_type_id,
    total_ground_truth,
    total_predictions,
    f1_score,
    correctness,
    time_error_for_95p_correctness,
    time_error_for_99p_correctness,
    prec,
    recall,
    time_error_p50,
    time_error_p95,
    time_error_p99,
    latency_p50,
    latency_p95,
    latency_p99
from correctness_f1_metrics
