{{ daily_config() }}

-- The schedule events are just the LAST prediction version of a prediction event

with last_version as (
    select distinct *
    from {{ ref("event_model_events_history_including_shadow") }}
    where
        ds = {{ ds() }}
        -- We can ignore any events versions older than 30 minutes
        -- since those may be from a backfill
        and updated_time - start_time < interval 30 hour
    qualify row_number() over (partition by id order by prediction_version desc) = 1
)


select
    {{ ds() }} as ds,
    environment,
    id,
    event_type_id,
    confidence,
    org_id,
    site_id,
    room_id,
    start_time,
    created_time,
    updated_time,
    prediction_version
from last_version
where
    deleted_at is null
