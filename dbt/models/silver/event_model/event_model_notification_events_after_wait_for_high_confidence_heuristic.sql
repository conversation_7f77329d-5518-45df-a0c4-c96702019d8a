{{ daily_config() }}

-- We used to have a heuristic in the notification system that would wait up to 3 minutes
-- for low confidence events before sending the notification.  This heuristic was moved from
-- the notification system to the event model.  The event model now waits to create an event,
-- so we just use the first version of predictions after the heuristic was deployed

-- That heuristic was deployed at '2024-07-01T19:30:00+00:00'

select distinct
    {{ ds() }} as ds,
    environment,
    id,
    event_type_id,
    confidence,
    org_id,
    site_id,
    room_id,
    start_time,
    created_time,
    updated_time,
    prediction_version
from {{ ref("event_model_events_history_including_shadow") }}
where
    ds = {{ ds() }}
    and start_time > '2024-07-01T19:30:00+00:00'
    and prediction_version = 1
