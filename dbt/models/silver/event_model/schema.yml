version: 1

models:
  - name: event_model_shadow_prediction_events
    description: >
        This is an intermediate table that pulls in all of the shadow prediction updates,
        and converts them to the same format as the event_history api table, so that they
        can easily be unioned in the event_model_all_prediction_versions_matched model.
    columns:
        - name: ds
          data_type: 'timestamp'
        - name: id
          data_type: 'string'
        - name: event_type_id
          data_type: 'string'
        - name: confidence
          data_type: 'float'
        - name: org_id
          data_type: 'string'
        - name: site_id
          data_type: 'string'
        - name: room_id
          data_type: 'string'
        - name: start_time
          data_type: 'timestamp'
        - name: created_time
          data_type: 'timestamp'
        - name: updated_time
          data_type: 'timestamp'
        - name: prediction_version
          data_type: 'int'
        - name: deleted_at
          data_type: 'timestamp'

  - name: event_model_events_history_including_shadow
    description: >
      This combines the event_model_shadow_prediction_events with the event_history table
      to create a single table that includes both shadow and prod events, with different
      values for 'environment' column
    columns:
      - name: ds
        data_type: 'timestamp'
      - name: environment
        data_type: 'string'
      - name: id
        data_type: 'string'
      - name: event_type_id
        data_type: 'string'
      - name: confidence
        data_type: 'float'
      - name: org_id
        data_type: 'string'
      - name: site_id
        data_type: 'string'
      - name: room_id
        data_type: 'string'
      - name: start_time
        data_type: 'timestamp'
      - name: created_time
        data_type: 'timestamp'
      - name: updated_time
        data_type: 'timestamp'
      - name: prediction_version
        data_type: 'int'
      - name: deleted_at
        data_type: 'timestamp'

  - name: event_model_notification_events_before_wait_for_high_confidence_heuristic
    description: >
      This is the subset of event versions from event_model_events_history_including_shadow
      that would have triggered notifications before we moved the wait_for_high_confidence heuristic
      from the notification system to the event model.
    columns:
      - name: ds
        data_type: 'timestamp'
      - name: environment
        data_type: 'string'
      - name: id
        data_type: 'string'
      - name: event_type_id
        data_type: 'string'
      - name: confidence
        data_type: 'float'
      - name: org_id
        data_type: 'string'
      - name: site_id
        data_type: 'string'
      - name: room_id
        data_type: 'string'
      - name: start_time
        data_type: 'timestamp'
      - name: created_time
        data_type: 'timestamp'
      - name: updated_time
        data_type: 'timestamp'
      - name: prediction_version
        data_type: 'int'


  - name: event_model_notification_events_after_wait_for_high_confidence_heuristic
    description: >
      This is the subset of event versions from event_model_events_history_including_shadow
      that would have triggered notifications AFTER we moved the wait_for_high_confidence heuristic
      from the notification system to the event model.
    columns:
      - name: ds
        data_type: 'timestamp'
      - name: environment
        data_type: 'string'
      - name: id
        data_type: 'string'
      - name: event_type_id
        data_type: 'string'
      - name: confidence
        data_type: 'float'
      - name: org_id
        data_type: 'string'
      - name: site_id
        data_type: 'string'
      - name: room_id
        data_type: 'string'
      - name: start_time
        data_type: 'timestamp'
      - name: created_time
        data_type: 'timestamp'
      - name: updated_time
        data_type: 'timestamp'
      - name: prediction_version
        data_type: 'int'

  - name: event_model_notification_events
    description: >
      This is all of the events that would have triggered notifications.  It is the union of
      event_model_notification_events_before_wait_for_high_confidence_heuristic and
      event_model_notification_events_after_wait_for_high_confidence_heuristic
    columns:
      - name: ds
        data_type: 'timestamp'
      - name: environment
        data_type: 'string'
      - name: id
        data_type: 'string'
      - name: event_type_id
        data_type: 'string'
      - name: confidence
        data_type: 'float'
      - name: org_id
        data_type: 'string'
      - name: site_id
        data_type: 'string'
      - name: room_id
        data_type: 'string'
      - name: start_time
        data_type: 'timestamp'
      - name: created_time
        data_type: 'timestamp'
      - name: updated_time
        data_type: 'timestamp'
      - name: prediction_version
        data_type: 'int'

  - name: event_model_schedule_events
    description: >
      This is the subset of event versions from event_model_events_history_including_shadow
      that would affect how the schedule looks.  It is essentially the last version of each
      event.
    columns:
      - name: ds
        data_type: 'timestamp'
      - name: environment
        data_type: 'string'
      - name: id
        data_type: 'string'
      - name: event_type_id
        data_type: 'string'
      - name: confidence
        data_type: 'float'
      - name: org_id
        data_type: 'string'
      - name: site_id
        data_type: 'string'
      - name: room_id
        data_type: 'string'
      - name: start_time
        data_type: 'timestamp'
      - name: created_time
        data_type: 'timestamp'
      - name: updated_time
        data_type: 'timestamp'
      - name: prediction_version
        data_type: 'int'

  - name: event_model_all_prediction_versions_matched
    description: >
      This table that compares both schedule events and notification events
      to the ground truth.  The two types of events are separated by the column "slo_category".
      They are categorized into "prediction_class" of "true_positive", "false_positive", and
      "false_negative".
    columns:
      - name: ds
        data_type: 'timestamp'
      - name: environment
        data_type: 'string'
      - name: slo_category
        data_type: 'string'
      - name: org_id
        data_type: 'string'
      - name: site_id
        data_type: 'string'
      - name: room_id
        data_type: 'string'
      - name: event_type_id
        data_type: 'string'
      - name: event_time
        data_type: 'timestamp'
      - name: ground_truth_id
        data_type: 'string'
      - name: ground_truth_event_time
        data_type: 'timestamp'
      - name: prediction_id
        data_type: 'string'
      - name: prediction_event_time
        data_type: 'timestamp'
      - name: prediction_updated_time
        data_type: 'timestamp'
      - name: prediction_version
        data_type: 'int'
      - name: confidence
        data_type: 'float'
      - name: time_error
        data_type: 'int'
      - name: latency
        data_type: 'int'
      - name: prediction_class
        data_type: 'string'
      - name: abs_time_error_with_null_as_9000
        data_type: 'int'
        description: Upstream calculation for correctness 95 and 99. Gets abs_time_error but applies a value to nulls so they can be included in the distribution

  - name: event_model_annotation_tasks_for_qa
    description: >
      This table is used to determine the tasks that can be used to determine model quality.
      Specifically, these are tasks that are completed (marked DONE), and are marked with is
      for_qa = true.  If one of these is true:
      * from a non-optimized task type
      * from an optimized task type AND part of the 10% QA
      Where optimized is if that task_type_id has `optimized` = `true`, which is where we use
      the annotation-task-optimizer to pick a subset of tasks to annotate with different criteria.
      One of those criteria is a random 10% that will be used for QA.
    columns:
      - name: ds
        data_type: 'timestamp'
      - name: id
        data_type: 'string'
      - name: room_id
        data_type: 'string'
      - name: start_time
        data_type: 'timestamp'
      - name: end_time
        data_type: 'timestamp'
      - name: event_types
        description: >
          The event types that are annotated in this task.  This is a list of event_type_ids
        data_type: 'array<string>'
      - name: metric_weight
        description: >
          How much to weight the metrics for events in this task type to remove bias from our SLOs
        data_type: 'float'

  - name: event_model_all_prediction_versions_matched_filtered_by_complete_annotation
    description: >
      This table filters the event_model_all_prediction_versions_matched by the time periods
      in which we have complete annotation tasks for those events.  This is to filter out
      events that could not be annotated due to a video outage.
    columns:
      - name: ds
        data_type: 'timestamp'
      - name: environment
        data_type: 'string'
      - name: slo_category
        data_type: 'string'
      - name: org_id
        data_type: 'string'
      - name: site_id
        data_type: 'string'
      - name: room_id
        data_type: 'string'
      - name: event_type_id
        data_type: 'string'
      - name: event_time
        data_type: 'timestamp'
      - name: ground_truth_id
        data_type: 'string'
      - name: ground_truth_event_time
        data_type: 'timestamp'
      - name: prediction_id
        data_type: 'string'
      - name: prediction_event_time
        data_type: 'timestamp'
      - name: prediction_updated_time
        data_type: 'timestamp'
      - name: prediction_version
        data_type: 'int'
      - name: confidence
        data_type: 'float'
      - name: time_error
        data_type: 'int'
      - name: latency
        data_type: 'int'
      - name: prediction_class
        data_type: 'string'
      - name: abs_time_error_with_null_as_9000
        data_type: 'int'
        description: Upstream calculation for correctness 95 and 99. Gets abs_time_error but applies a value to nulls so they can be included in the distribution
      - name: metric_weight
        description: >
          How much to weight the metrics for this event to remove bias from our SLOs
        data_type: 'float'

  - name: event_model_slos
    description: >
      These are the final SLO metrics for the 'prod' environment, for each 'slo_category'.  It also
      splits by 'slo_time_granularity', with values like 'day', 'week', and 'month'.   
      This also calculates metrics across all orgs/sites separately from each individual site, 
      and you should either filter by site_id = 'all' or site_id = 'your site id'
    columns:
      - name: ds
        data_type: 'timestamp'
      - name: slo_category
        data_type: 'string'
      - name: slo_time_granularity
        data_type: 'string'
      - name: date_bucket
        data_type: 'timestamp'
      - name: org_id
        data_type: 'string'
      - name: site_id
        data_type: 'string'
      - name: room_id
        data_type: 'string'
      - name: event_type_id
        data_type: 'string'
      - name: total_ground_truth
        data_type: 'int'
      - name: total_predictions
        data_type: 'int'
      - name: f1_score
        data_type: 'float'
      - name: correctness
        data_type: 'float'
      - name: time_error_for_95p_correctness
        data_type: 'float'
        description: Can be thought of as events are detected with in this time error 95% of the time with a value of 9000 meaning we have a correctness of below 95%
      - name: time_error_for_99p_correctness
        data_type: 'float'
        description: Can be thought of as events are detected with in this time error 99% of the time with a value of 9000 meaning we have a correctness of below 99%
      - name: prec
        data_type: 'float'
      - name: recall
        data_type: 'float'
      - name: time_error_p50
        data_type: 'float'
      - name: time_error_p95
        data_type: 'float'
      - name: time_error_p99
        data_type: 'float'
      - name: latency_p50
        data_type: 'float'
      - name: latency_p95
        data_type: 'float'
      - name: latency_p99
        data_type: 'float'

  - name: event_model_misclassified_events
    description: >
      These are events that have more than one type of event_type_id associated with them.
      Only events for which we have more than one event_type_id are in this table
      We just keep the rows with versions where the event_type_id changed.
    columns:
      - name: ds
        data_type: 'timestamp'
      - name: id
        data_type: 'string'
      - name: event_type_id
        data_type: 'string'
      - name: version
        data_type: 'int'
      - name: previous_event_type_id
        data_type: 'string'
      - name: previous_version
        data_type: 'int'
