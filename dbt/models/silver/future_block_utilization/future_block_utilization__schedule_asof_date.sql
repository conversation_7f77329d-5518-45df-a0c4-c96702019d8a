{{ daily_config() }}

{% set max_days_to_surgery = 30 %}
{% set future_days = 60 %}
{% set history_size_in_days = 3 * 364 %}

with cases_asof_date as (
    -- this is just a copy of history that discards all the intermediate changes to a case made
    -- in a single date, room. We only keep the last version of the case_history for each
    -- (date, room)
    select ch.*
    from {{ api_table_snapshot('bronze', 'public_cases_history', 'ch') }}
    inner join {{ api_table_snapshot('bronze', 'public_sites', 'sites') }}
        on ch.site_id = sites.id
    where
        -- todo, once ticket OBS-1449 is resolved, we should be able to remove this start_time < end_time
        ch.scheduled_start_time < ch.scheduled_end_time
    -- if a case is cancelled on date d1 and rescheduled on a different date, we need both rows
    -- if a case is cancelled on date d1 and rescheduled on d1, we only need the latest one
    qualify row_number() over (
        partition by
            ch.case_id,
            ch.room_id,
            date(ch.scheduled_start_time, sites.timezone),
            -- FIXME we don't want updated_time in the next line because when we backfill
            --   updated_time will be the backfill time. What we want here is the hl7 reception
            --   time and the statement should be date(ch.hl7_time, sites.timezone)
            date(ch.updated_time, sites.timezone)
        order by ch.version desc
    ) = 1
),

room_id_surgery_date_and_asof_date as (
    -- here we have every combination of room_id, surgery_date (in the last N months) and days_to_surgery
    -- (from 0 to max_days_to_surgery)
    -- FIXME: this does not have data in the future, which is probably wrong.
    select
        r.id as room_id,
        days_to_surgery,
        date_sub(
            date_add(current_date(), interval {{ future_days }} day),
            interval sched_date_offset day
        ) as surgery_date,
        date_sub(
            date_add(current_date(), interval {{ future_days }} day),
            interval sched_date_offset + days_to_surgery day
        ) as asof_date
    from unnest(generate_array(0, {{ max_days_to_surgery }})) as days_to_surgery
    cross join unnest(generate_array(0, {{ history_size_in_days }})) as sched_date_offset
    cross join {{ api_table_snapshot("bronze", "public_rooms", "r") }}
),


daily_scheduled_and_cancelled_cases as (
    select
        cases.*,
        asof.surgery_date,
        asof.asof_date,
        asof.days_to_surgery
    from cases_asof_date as cases
    inner join {{ api_table_snapshot("bronze", "public_sites", "sites") }}
        on cases.site_id = sites.id
    inner join room_id_surgery_date_and_asof_date as asof
        on
            cases.room_id = asof.room_id
            and date(cases.scheduled_start_time, sites.timezone) = asof.surgery_date
            and date(cases.updated_time) < asof.asof_date
    -- currently based on the above join, each case_id, surgery_date, room_id and asof_date has
    -- many updates (all updates prior to asof_date). We have to keep just the last one
    qualify
        row_number()
            over (
                partition by cases.case_id, asof.asof_date, asof.surgery_date
                order by cases.version desc
            )
        = 1
)

-- finally, just get rid of all the cancelled cases.
select
    {{ ds() }} as ds,
    daily_scheduled_and_cancelled_cases.*
from daily_scheduled_and_cancelled_cases
where status = 'scheduled'
