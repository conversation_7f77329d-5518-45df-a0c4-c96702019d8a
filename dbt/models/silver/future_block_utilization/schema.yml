version: 1

models:
  - name: future_block_utilization__schedule_asof_date
    description: >
      This is a view of the cases `asof` a certain date (not at arbitrary resolution though. 
      We simplify the problem by assuming that we want to look only at the schedule asof midnight)
      Apella cases can be rescheduled to different rooms and dates or just cancelled.
      Many times, when they are rescheduling a case, a lot of messages come in rapid succession.
      The cases_history will have multiple rows reflecting intra-day changes that we are not
      interested in here.
      Here, we are just interested in the way the schedule looked at each day (over the last
      6 months or so), N days into the future (N ~ 30) 
      Schema should be the same as in cases with only 3 additions (surgery_date, asof_date, days_to_surgery)
      - name: ds
        data_type: "timestamp"
      - name: updated_time
        date_type: "timestamp"
      - name: case_id
        data_type: "string"
      - name: status
        date_type: "string"
      - name: version
        data_type: "int"
      - name: site_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: scheduled_start_time
        date_type: "timestamp"
      - name: scheduled_end_time
        date_type: "timestamp"
      - name: days_to_surgery
        data_type: "int"
      - name: surgery_date
        date_type: "date"
      - name: asof_date
        data_type: "date"
