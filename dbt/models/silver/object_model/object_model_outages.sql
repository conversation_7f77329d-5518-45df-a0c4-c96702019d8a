{{ daily_config() }}

with

--create array of timestamps
timestamp_array as (
    select *
    from unnest(generate_timestamp_array(
        timestamp((select timestamp('2024-01-01 00:00:00 UTC'))),
        timestamp((select timestamp_add(datetime_trunc(current_timestamp(), minute), interval -1 minute))),
        interval 1 minute
    )) as date_time
),

--get list of rooms
rooms as (
    select
        org_id,
        id as room_id
    from {{ api_table_snapshot("bronze", "public_rooms") }}
    where org_id not in ('apella_internal_0', 'greys_anatomy', 'scrubs')
),

--join timestamps with rooms
timestamp_array_rooms as (
    select
        t.date_time,
        r.org_id,
        r.room_id
    from timestamp_array as t
    cross join rooms as r
),

--count image uploads per minute for each room
image_counts as (
    select
        org_id,
        room_id,
        datetime_trunc(publish_time, minute) as publish_time_minute_trunc,
        count(publish_time) as image_count
    from {{ source("ml_project", "image_processing_output") }}
    group by
        org_id,
        room_id,
        publish_time_minute_trunc
),

--join image count to timestamp list
timestamp_array_room_image_counts as (
    select
        t.*,
        coalesce(f.image_count, 0) as image_count
    from timestamp_array_rooms as t
    left outer join image_counts as f
        on
            t.org_id = f.org_id
            and t.room_id = f.room_id
            and t.date_time = f.publish_time_minute_trunc
),

--find timestamp of first image to remove prior dates in next CTE
inactive_datetimes as (
    select
        t.*,
        min(case when image_count > 0 then date_time end) over (partition by room_id) as start_date_time
    from timestamp_array_room_image_counts as t
),

--identify minutes where room had 0% image capture
active_date_times as (
    select * from inactive_datetimes
    where
        date_time >= start_date_time
        and image_count = 0
),

--define delays where there is a difference of 10+ minutes
delay_marker as (
    select
        date_time,
        org_id,
        room_id,
        case
            when
                datetime_diff(date_time, lag(date_time) over (partition by org_id, room_id order by date_time), minute)
                >= 10
                then 1
            else 0
        end as delay
    from active_date_times
),

delay_label as (
    select
        delay_marker.*,
        sum(delay) over (partition by org_id, room_id order by date_time) as room_delay_label
    from
        delay_marker
),

--determine delay datetime window
delay_window as (
    select
        org_id,
        room_id,
        room_delay_label,
        min(date_time) as start_time,
        max(date_time) as end_time
    from
        delay_label
    group by
        org_id, room_id, room_delay_label
),

--determine delay duration in minutes
delay_duration as (
    select
        delay_window.*,
        datetime_diff(end_time, start_time, minute) as delay_duration_minutes
    from delay_window
),

--filter to outages (delays >= 10+ minutes)
final as (
    select
        {{ ds() }} as ds,
        start_time,
        end_time,
        room_id,
        org_id,
        delay_duration_minutes
    from delay_duration
    where delay_duration_minutes >= 10
    order by start_time desc, end_time desc
)

select * from final
