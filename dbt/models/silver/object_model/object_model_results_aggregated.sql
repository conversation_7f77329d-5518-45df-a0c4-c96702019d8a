{{ daily_config(expiration_days=5) }}

-- to limit size of table
-- we will rarely need object model results prior to 2023
with start_time as (
    select timestamp('2024-01-01 00:00:00 UTC') as dt
),

-- only select frames up through the previous day
-- otherwise there are lots of null counts as current day is underway
end_time as (
    select timestamp_add({{ ds(as_date=False) }}, interval -1 day) as dt
),

-- generate array of timestamps
timestamp_array as (
    select *
    from unnest(generate_timestamp_array(
        timestamp((select dt from start_time)),
        timestamp((select dt from end_time)),
        interval 1 minute
    )) as date_time
),

-- get all room ids
rooms as (
    select
        org_id,
        site_id,
        id as room_id
    from {{ api_table_snapshot("bronze", "public_rooms") }}
    where org_id not in ('apella_internal_0', 'greys_anatomy', 'scrubs')
),

-- full outer join all timestamps to all room ids
-- this will be the backbone of the ultimate outputted table
-- ensures that even minutes with zero image frames
-- are kept as null values when we later join to object stats
timestamp_array_rooms as (
    select
        t.date_time,
        r.org_id,
        r.room_id
    from timestamp_array as t
    full outer join rooms as r
        on 1 = 1
),

-- some rows in image processing output are duplicates
-- this leads to inflated object counts and frame counts
-- drop rows in this table by taking just 1 row per frame_url
image_processing_output_deduped as (
    select
        frame_url,
        any_value(frame_time) as frame_time,
        any_value(org_id) as org_id,
        any_value(room_id) as room_id,
        any_value(camera_id) as camera_id,
        any_value(object_model_output) as object_model_output
    from {{ api_table_snapshot("ml_project", "image_processing_output") }}
    where
        frame_time >= (select dt from start_time)
        and frame_time < (select timestamp_add(dt, interval 1 minute) from end_time)
    group by frame_url
),

-- count frames per room per minute
frame_counts as (
    select
        org_id,
        room_id,
        datetime_trunc(frame_time, minute) as frame_time_minute_trunc,
        count(*) as frame_count
    from image_processing_output_deduped
    group by
        org_id,
        room_id,
        frame_time_minute_trunc
),

-- join timestamp array to frame counts
timestamp_array_rooms_frame_counts as (
    select
        t.*,
        coalesce(f.frame_count, 0) as frame_count
    from timestamp_array_rooms as t
    left outer join frame_counts as f
        on
            t.org_id = f.org_id
            and t.room_id = f.room_id
            and t.date_time = f.frame_time_minute_trunc
),

-- unnest the model output array
one_object_per_row as (
    select
        frame_url,
        frame_time,
        org_id,
        room_id,
        camera_id,
        object_model_output,
        objects_column.label_display_name as object_type
    from image_processing_output_deduped,
        unnest(object_model_output) as objects_column
),

-- count objects in each frame
object_counts as (
    select
        frame_url,
        frame_time,
        org_id,
        room_id,
        camera_id,
        object_type,
        count(*) as object_ct
    from one_object_per_row
    group by
        frame_url,
        frame_time,
        org_id,
        room_id,
        camera_id,
        object_type
),

-- pivot table to make columns for each object type
object_counts_pivot as (
    select
        frame_url,
        frame_time,
        org_id,
        room_id,
        camera_id,
        coalesce(scrubbed, 0) as scrubbed,
        coalesce(unscrubbed, 0) as unscrubbed,
        coalesce(bed_occupied, 0) as bed_occupied,
        coalesce(bed_free, 0) as bed_free,
        coalesce(or_table_occupied, 0) as or_table_occupied,
        coalesce(or_table_free, 0) as or_table_free,
        coalesce(back_table_unprepared, 0) as back_table_unprepared,
        coalesce(back_table_open, 0) as back_table_open,
        coalesce(patient_draped, 0) as patient_draped,
        coalesce(mop, 0) as mop,
        coalesce(mop_head, 0) as mop_head,
        coalesce(mop_bucket, 0) as mop_bucket,
        coalesce(endo_pack_open, 0) as endo_pack_open
    from object_counts
    pivot (
        max(object_ct) for object_type in (
            'scrubbed',
            'unscrubbed',
            'bed_occupied',
            'bed_free',
            'or_table_occupied',
            'or_table_free',
            'back_table_unprepared',
            'back_table_open',
            'patient_draped',
            'mop',
            'mop_head',
            'mop_bucket',
            'endo_pack_open'
        )
    )
),

-- aggregate across cameras
camera_agg as (
    select
        frame_time,
        org_id,
        room_id,
        datetime_trunc(frame_time, minute) as frame_time_minute_trunc,
        max(scrubbed) as scrubbed_ct_max_across_cameras,
        max(unscrubbed) as unscrubbed_ct_max_across_cameras,
        max(bed_occupied) as bed_occupied_ct_max_across_cameras,
        max(bed_free) as bed_free_ct_max_across_cameras,
        max(or_table_occupied) as or_table_occupied_ct_max_across_cameras,
        max(or_table_free) as or_table_free_ct_max_across_cameras,
        max(back_table_unprepared) as back_table_unprepared_ct_max_across_cameras,
        max(back_table_open) as back_table_open_ct_max_across_cameras,
        max(patient_draped) as patient_draped_ct_max_across_cameras,
        max(mop) as mop_ct_max_across_cameras,
        max(mop_head) as mop_head_ct_max_across_cameras,
        max(mop_bucket) as mop_bucket_ct_max_across_cameras,
        max(endo_pack_open) as endo_pack_open_ct_max_across_cameras
    from object_counts_pivot
    group by
        frame_time,
        org_id,
        room_id
),

-- aggregate by minute
minute_aggregated as (
    select
        c.org_id,
        c.room_id,
        c.frame_time_minute_trunc,
        avg(scrubbed_ct_max_across_cameras) as scrubbed_ct_camera_max_minute_mean,
        avg(unscrubbed_ct_max_across_cameras) as unscrubbed_ct_camera_max_minute_mean,
        max(scrubbed_ct_max_across_cameras) as scrubbed_ct_camera_max_minute_max,
        max(unscrubbed_ct_max_across_cameras) as unscrubbed_ct_camera_max_minute_max,
        avg(unscrubbed_ct_max_across_cameras)
        + avg(scrubbed_ct_max_across_cameras) as total_occupancy_camera_max_minute_mean,
        max(unscrubbed_ct_max_across_cameras)
        + max(scrubbed_ct_max_across_cameras) as total_occupancy_camera_max_minute_max,
        avg(bed_occupied_ct_max_across_cameras) as bed_occupied_ct_camera_max_minute_mean,
        avg(bed_free_ct_max_across_cameras) as bed_free_ct_camera_max_minute_mean,
        avg(or_table_occupied_ct_max_across_cameras) as or_table_occupied_ct_camera_max_minute_mean,
        avg(or_table_free_ct_max_across_cameras) as or_table_free_ct_camera_max_minute_mean,
        avg(back_table_unprepared_ct_max_across_cameras) as back_table_unprepared_ct_camera_max_minute_mean,
        avg(back_table_open_ct_max_across_cameras) as back_table_open_ct_camera_max_minute_mean,
        avg(patient_draped_ct_max_across_cameras) as patient_draped_ct_camera_max_minute_mean,
        avg(mop_ct_max_across_cameras) as mop_ct_camera_max_minute_mean,
        avg(mop_head_ct_max_across_cameras) as mop_head_ct_camera_max_minute_mean,
        avg(mop_bucket_ct_max_across_cameras) as mop_bucket_ct_camera_max_minute_mean,
        avg(endo_pack_open_ct_max_across_cameras) as endo_pack_open_ct_camera_max_minute_mean
    from camera_agg as c
    group by
        org_id,
        room_id,
        frame_time_minute_trunc
),

-- join timestamp array to minute agg so minutes with zero frames are not lost
timestamp_array_rooms_minute_agg as (
    select
        t.date_time,
        t.org_id,
        t.room_id,
        m.frame_time_minute_trunc,
        t.frame_count,
        m.scrubbed_ct_camera_max_minute_mean,
        m.unscrubbed_ct_camera_max_minute_mean,
        m.scrubbed_ct_camera_max_minute_max,
        m.unscrubbed_ct_camera_max_minute_max,
        m.total_occupancy_camera_max_minute_mean,
        m.total_occupancy_camera_max_minute_max,
        m.bed_occupied_ct_camera_max_minute_mean,
        m.bed_free_ct_camera_max_minute_mean,
        m.or_table_occupied_ct_camera_max_minute_mean,
        m.or_table_free_ct_camera_max_minute_mean,
        m.back_table_unprepared_ct_camera_max_minute_mean,
        m.back_table_open_ct_camera_max_minute_mean,
        m.patient_draped_ct_camera_max_minute_mean,
        m.mop_ct_camera_max_minute_mean,
        m.mop_head_ct_camera_max_minute_mean,
        m.mop_bucket_ct_camera_max_minute_mean,
        m.endo_pack_open_ct_camera_max_minute_mean
    from timestamp_array_rooms_frame_counts as t
    left outer join minute_aggregated as m
        on
            t.date_time = m.frame_time_minute_trunc
            and t.org_id = m.org_id
            and t.room_id = m.room_id
),

local_timestamps as (
    select
        t.*,
        r.site_id,
        datetime(t.date_time, sites.timezone) as date_time_local
    from timestamp_array_rooms_minute_agg as t
    left outer join rooms as r
        on t.room_id = r.room_id
    left outer join {{ api_table_snapshot("bronze", "public_sites", "sites") }}
        on r.site_id = sites.id
),

final_output as (
    select
        {{ ds() }} as ds,
        org_id,
        site_id,
        room_id,
        date_time as frame_time_utc_minute_trunc,
        date_time_local as frame_time_local_minute_trunc,
        frame_count,
        (frame_count > 0) as has_frames_bool,
        total_occupancy_camera_max_minute_mean as total_occupancy,
        total_occupancy_camera_max_minute_mean,
        total_occupancy_camera_max_minute_max,
        scrubbed_ct_camera_max_minute_mean,
        unscrubbed_ct_camera_max_minute_mean,
        scrubbed_ct_camera_max_minute_max,
        unscrubbed_ct_camera_max_minute_max,
        bed_occupied_ct_camera_max_minute_mean,
        bed_free_ct_camera_max_minute_mean,
        or_table_occupied_ct_camera_max_minute_mean,
        or_table_free_ct_camera_max_minute_mean,
        back_table_unprepared_ct_camera_max_minute_mean,
        back_table_open_ct_camera_max_minute_mean,
        patient_draped_ct_camera_max_minute_mean,
        mop_ct_camera_max_minute_mean,
        mop_head_ct_camera_max_minute_mean,
        mop_bucket_ct_camera_max_minute_mean,
        endo_pack_open_ct_camera_max_minute_mean
    from local_timestamps
)

select *
from final_output
