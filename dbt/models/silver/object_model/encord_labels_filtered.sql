{{ daily_config() }}

select
    id as encord_id,
    annotation_task_status,
    last_edited_at,
    object_json,
    project_hash,
    project_name,
    custom_metadata,
    object_labels,
    classifications,
    -- The ds field in the source table is not the same as the ds we use for the target table
    -- because the source table is updated the night before we run this model
    {{ ds() }} as ds,
    json_value(encord_labels_daily.custom_metadata, "$.org_id_enum") as org_id,
    json_value(encord_labels_daily.custom_metadata, "$.site_id_enum") as site_id,
    json_value(encord_labels_daily.custom_metadata, "$.image_bin_type") as image_bin_type,
    json_value(encord_labels_daily.custom_metadata, "$.anonymized_image_id") as anonymized_image_id,
    json_value(encord_labels_daily.custom_metadata, "$.anonymized_image_uri") as anonymized_image_uri,
    date(json_value(encord_labels_daily.custom_metadata, "$.frame_time_period")) as frame_time_period
from {{ source('bronze', 'encord_labels_daily') }} as encord_labels_daily
where
    -- Encord labels are exported the night before we run this model
    encord_labels_daily.ds = date_sub({{ ds() }}, interval 1 day)
    and parse_json(json_query(encord_labels_daily.classifications, "$.discard_reason")) is null
