version: 1

models:
  - name: object_model_results_aggregated
    description: >
      Aggregated counts from the object model. Includes minute-level counts
      of all object types.
    columns:
      - name: org_id
        data_type: "str"
      - name: site_id
        data_type: "str"
      - name: room_id
        data_type: "str"
      - name: frame_time_utc_minute_trunc
        data_type: "datetime"
        description: Object results are truncated to the minute-level window in UTC
      - name: frame_time_local_minute_trunc
        data_type: "datetime"
        description: Object results are truncated to the minute-level window in local timezone
      - name: frame_count
        data_type: "int"
        description: >
          Number of frames captured during the minute-level window in room. We expect
          to see 48 frames per room per minute for rooms that have 4 cameras
      - name: has_frames_bool
        data_type: "bool"
        description: True if frame_count is greater than zero; False otherwise
      - name: total_occupancy
        data_type: "float"
        description: The sum of unscrubbed_ct_camera_max_minute_mean and scrubbed_ct_camera_max_minute_mean
      - name: total_occupancy_camera_max_minute_mean
        data_type: "float"
        description: The sum of unscrubbed_ct_camera_max_minute_mean and scrubbed_ct_camera_max_minute_mean
      - name: total_occupancy_camera_max_minute_max
        data_type: "float"
        description: The sum of unscrubbed_ct_camera_max_minute_max and scrubbed_ct_camera_max_minute_max
      - name: scrubbed_ct_camera_max_minute_mean
        data_type: "float"
        description: >
          The count of scrubbed objects with the following aggregation method:
          First, count the number of detected scrubbed objects.
          Then, take the max scrubbed count across the four cameras
          Finally, take the average of this "cross-camera-max" per frame_time_local_minute_trunc
      - name: unscrubbed_ct_camera_max_minute_mean
        data_type: "float"
        description: >
          The count of unscrubbed objects with the following aggregation method:
          First, count the number of detected unscrubbed objects.
          Then, take the max unscrubbed count across the four cameras
          Finally, take the average of this "cross-camera-max" per frame_time_local_minute_trunc
      - name: scrubbed_ct_camera_max_minute_max
        data_type: "int"
        description: >
          The count of scrubbed objects with the following aggregation method:
          First, count the number of detected scrubbed objects.
          Then, take the max scrubbed count across the four cameras
          Finally, take the max of this "cross-camera-max" per frame_time_local_minute_trunc
      - name: unscrubbed_ct_camera_max_minute_max
        data_type: "int"
        description: >
          The count of unscrubbed objects with the following aggregation method:
          First, count the number of detected unscrubbed objects.
          Then, take the max unscrubbed count across the four cameras
          Finally, take the max of this "cross-camera-max" per frame_time_local_minute_trunc
      - name: bed_occupied_ct_camera_max_minute_mean
        data_type: "float"
        description: >
          The count of bed_occupied with the following aggregation method:
          First, count the number of detected bed_occupied objects.
          Then, take the max bed_occupied count across the four cameras
          Finally, take the average of this "cross-camera-max" per frame_time_local_minute_trunc
      - name: bed_free_ct_camera_max_minute_mean
        data_type: "float"
        description: >
          The count of bed_free with the following aggregation method:
          First, count the number of detected bed_free objects.
          Then, take the max bed_free count across the four cameras
          Finally, take the average of this "cross-camera-max" per frame_time_local_minute_trunc
      - name: or_table_occupied_ct_camera_max_minute_mean
        data_type: "float"
        description: >
          The count of or_table_occupied with the following aggregation method:
          First, count the number of detected or_table_occupied objects.
          Then, take the max or_table_occupied count across the four cameras
          Finally, take the average of this "cross-camera-max" per frame_time_local_minute_trunc
      - name: or_table_free_ct_camera_max_minute_mean
        data_type: "float"
        description: >
          The count of or_table_free with the following aggregation method:
          First, count the number of detected or_table_free objects.
          Then, take the max or_table_free count across the four cameras
          Finally, take the average of this "cross-camera-max" per frame_time_local_minute_trunc
      - name: back_table_unprepared_ct_camera_max_minute_mean
        data_type: "float"
        description: >
          The count of back_table_unprepared with the following aggregation method:
          First, count the number of detected back_table_unprepared objects.
          Then, take the max back_table_unprepared count across the four cameras
          Finally, take the average of this "cross-camera-max" per frame_time_local_minute_trunc
      - name: back_table_open_ct_camera_max_minute_mean
        data_type: "float"
        description: >
          The count of back_table_open with the following aggregation method:
          First, count the number of detected back_table_open objects.
          Then, take the max back_table_open count across the four cameras
          Finally, take the average of this "cross-camera-max" per frame_time_local_minute_trunc
      - name: patient_draped_ct_camera_max_minute_mean
        data_type: "float"
        description: >
          The count of patient_draped with the following aggregation method:
          First, count the number of detected patient_draped objects.
          Then, take the max patient_draped count across the four cameras
          Finally, take the average of this "cross-camera-max" per frame_time_local_minute_trunc
      - name: mop_ct_camera_max_minute_mean
        data_type: "float"
        description: >
          The count of mop with the following aggregation method:
          First, count the number of detected mop objects.
          Then, take the max mop count across the four cameras
          Finally, take the average of this "cross-camera-max" per frame_time_local_minute_trunc
      - name: mop_head_ct_camera_max_minute_mean
        data_type: "float"
        description: >
          The count of mop_head with the following aggregation method:
          First, count the number of detected mop_head objects.
          Then, take the max mop_head count across the four cameras
          Finally, take the average of this "cross-camera-max" per frame_time_local_minute_trunc
      - name: mop_bucket_ct_camera_max_minute_mean
        data_type: "float"
        description: >
          The count of mop_bucket with the following aggregation method:
          First, count the number of detected mop_bucket objects.
          Then, take the max mop_bucket count across the four cameras
          Finally, take the average of this "cross-camera-max" per frame_time_local_minute_trunc
      - name: endo_pack_open_ct_camera_max_minute_mean
        data_type: "float"
        description: >
          The count of endo_pack_open with the following aggregation method:
          First, count the number of detected endo_pack_open objects.
          Then, take the max endo_pack_open count across the four cameras
          Finally, take the average of this "cross-camera-max" per frame_time_local_minute_trunc
  - name: patient_blur_static_bounding_box
    description: >
      The weekly average of patient bounding boxes over the past 7 days. These
      bounding boxes represent the 95% confidence interval of the points in
      objects containing the patient while undergoing surgery, namely
      `patient_draped` and `or_table_occuplied`.

      Consumers of this data will likely want to use the largest bounding box
      containing the patient, which would be the bounding box defined by the
      points (x1_ci_lower, y1_ci_lower) and (x2_ci_upper, y2_ci_upper).
    columns:
      - name: org_id
        data_type: "str"
      - name: room_id
        data_type: "str"
      - name: camera_id
        data_type: "str"
      - name: x1_mean
        tests:
          - not_null
          - accepted_range:
              min_value: 0
              max_value: 1
        data_type: "float"
        description: The mean x1 value of the patient bounding box.
      - name: y1_mean
        tests:
          - not_null
          - accepted_range:
              min_value: 0
              max_value: 1
        data_type: "float"
        description: The mean y1 value of the patient bounding box.
      - name: x2_mean
        tests:
          - not_null
          - accepted_range:
              min_value: 0
              max_value: 1
        data_type: "float"
        description: The mean x2 value of the patient bounding box.
      - name: y2_mean
        tests:
          - not_null
          - accepted_range:
              min_value: 0
              max_value: 1
        data_type: "float"
        description: The mean y2 value of the patient bounding box.
      - name: x1_std
        tests:
          - not_null
          - accepted_range:
              min_value: 0
              max_value: 1
        data_type: "float"
        description: The standard deviation of the x1 value of the patient bounding box.
      - name: y1_std
        tests:
          - not_null
          - accepted_range:
              min_value: 0
              max_value: 1
        data_type: "float"
        description: The standard deviation of the y1 value of the patient bounding box.
      - name: x2_std
        tests:
          - not_null
          - accepted_range:
              min_value: 0
              max_value: 1
        data_type: "float"
        description: The standard deviation of the x2 value of the patient bounding box.
      - name: y2_std
        tests:
          - not_null
          - accepted_range:
              min_value: 0
              max_value: 1
        data_type: "float"
        description: The standard deviation of the y2 value of the patient bounding box.
      - name: total
        tests:
          - not_null
          - accepted_range:
              min_value: 0
        data_type: "int"
        description: The total number of patient bounding boxes.
      - name: x1_ci_lower
        tests:
          - not_null
          - accepted_range:
              min_value: 0
              max_value: 1
        data_type: "float"
        description: The lower bound of the 95% confidence interval for the x1 value of the patient bounding box.
      - name: x1_ci_upper
        tests:
          - not_null
          - accepted_range:
              min_value: 0
              max_value: 1
        data_type: "float"
        description: The upper bound of the 95% confidence interval for the x1 value of the patient bounding box.
      - name: y1_ci_lower
        tests:
          - not_null
          - accepted_range:
              min_value: 0
              max_value: 1
        data_type: "float"
        description: The lower bound of the 95% confidence interval for the y1 value of the patient bounding box.
      - name: y1_ci_upper
        tests:
          - not_null
          - accepted_range:
              min_value: 0
              max_value: 1
        data_type: "float"
        description: The upper bound of the 95% confidence interval for the y1 value of the patient bounding box.
      - name: x2_ci_lower
        tests:
          - not_null
          - accepted_range:
              min_value: 0
              max_value: 1
        data_type: "float"
        description: The lower bound of the 95% confidence interval for the x2 value of the patient bounding box.
      - name: x2_ci_upper
        tests:
          - not_null
          - accepted_range:
              min_value: 0
              max_value: 1
        data_type: "float"
        description: The upper bound of the 95% confidence interval for the x2 value of the patient bounding box.
      - name: y2_ci_lower
        tests:
          - not_null
          - accepted_range:
              min_value: 0
              max_value: 1
        data_type: "float"
        description: The lower bound of the 95% confidence interval for the y2 value of the patient bounding box.
      - name: y2_ci_upper
        tests:
          - not_null
          - accepted_range:
              min_value: 0
              max_value: 1
        data_type: "float"
        description: The upper bound of the 95% confidence interval for the y2 value of the patient bounding box.
  - name: object_model_outages
    description: Outages (defined as a 10+ minute interval with no image uploads) by room.
    columns:
      - name: room_id
        data_type: "str"
      - name: org_id
        data_type: "str"
      - name: start_time
        data_type: "datetime"
        description: Start time of outage in UTC
      - name: end_time
        data_type: "datetime"
        description: End time of outage in UTC
      - name: delay_duration_minutes
        data_type: "int"
        description: Outage duration in minutes
  - name: encord_labels_filtered
    description: >
      Encord labels with any images that have been discarded by annotation filtered out
    columns:
      - name: encord_id
        description: The id of the label row assigned by encord
        data_type: "str"
        tests:
          - not_null
      - name: annotation_task_status
        description: How far through the annotation process the image is
        data_type: "str"
      - name: last_edited_at
        description: When the label was last edited
        data_type: "datetime"
        tests:
          - not_null
      - name: object_json
        description: The json string for the encord object
        data_type: "str"
      - name: project_hash
        description: The hash of the encord project the image is from
        data_type: "str"
      - name: project_name
        description: The name of the project the image is from
        data_type: "str"
      - name: custom_metadata
        description: The custom metadata JSON string for the image
        data_type: "str"
      - name: object_labels
        description: The labels for the image
        data_type: "Repeated<Struct>"
      - name: classifications
        description: The classifications of the image, this table is filtered on discard reasons
        data_type: "str"
      - name: ds
        description: The ds partition
        data_type: "date"
      - name: org_id
        data_type: "str"
      - name: site_id
        data_type: "str"
      - name: anonymized_image_id
        data_type: "str"
        description: The anonymized image id assigned by our image selection pipeline
      - name: anonymized_image_uri
        data_type: "str"
        description: The uri of the actual image for use by our training pipeline
      - name: image_bin_type
        data_type: "str"
      - name: frame_time_period
        data_type: "datetime"
        description: The quarter the image was taken in
  - name: encord_object_model_combined_labels
    description: >
      Encord labels for training the object model, after combining labels from multiple projects
    columns:
      - name: encord_id
        description: The id of the label row assigned by encord
        data_type: "str"
        tests:
          - not_null
      - name: ds
        description: The ds partition
        data_type: "date"
      - name: org_id
        data_type: "str"
      - name: site_id
        data_type: "str"
      - name: image_bin_type
        data_type: "str"
      - name: anonymized_image_id
        data_type: "str"
        description: The anonymized image id assigned by our image selection pipeline
      - name: anonymized_image_uri
        data_type: "str"
        description: The uri of the actual image for use by our training pipeline
      - name: frame_time_period
        data_type: "datetime"
        description: The quarter the image was taken in
      - name: project_hashes
        description: The hash of the encord projects the image is from
        data_type: "ARRAY<STRING>"
      - name: project_names
        description: The name of the projects the image is from
        data_type: "ARRAY<STRING>"
      - name: object_labels
        description: The labels for the image
        data_type: "Repeated<Struct>"
  - name: encord_object_model_labels
    description: >
      Encord labels for training the object model. Keeps only images that have been labeled in ALL projects (ignore partially labeled images)
    columns:
      - name: encord_id
        description: The id of the label row assigned by encord
        data_type: "str"
        tests:
          - not_null
      - name: ds
        description: The ds partition
        data_type: "date"
      - name: org_id
        data_type: "str"
      - name: site_id
        data_type: "str"
      - name: image_bin_type
        data_type: "str"
      - name: anonymized_image_id
        data_type: "str"
        description: The anonymized image id assigned by our image selection pipeline
      - name: anonymized_image_uri
        data_type: "str"
        description: The uri of the actual image for use by our training pipeline
      - name: frame_time_period
        data_type: "datetime"
        description: The quarter the image was taken in
      - name: project_hashes
        description: The hash of the encord projects the image is from
        data_type: "ARRAY<STRING>"
      - name: project_names
        description: The name of the projects the image is from
        data_type: "ARRAY<STRING>"
      - name: object_labels
        description: The labels for the image
        data_type: "Repeated<Struct>"