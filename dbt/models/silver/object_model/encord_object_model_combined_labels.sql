{{ daily_config() }}

select
    encord_id,
    ds,
    org_id,
    site_id,
    image_bin_type,
    anonymized_image_id,
    anonymized_image_uri,
    frame_time_period,
    array_agg(project_hash) as project_hashes,
    array_agg(project_name) as project_names,
    array_concat_agg(object_labels) as object_labels
from {{ ref('encord_labels_filtered') }} as encord_labels_filtered
where
    encord_labels_filtered.ds = {{ ds() }}
    -- only take images from the projects we use for training the object model
    and project_hash in (
        "e45a828c-4ace-45c0-af17-e58a54815328",  -- Main Objects Dev
        "dbb68bff-3985-48f6-a3b5-63dfb96f0dc1",  -- Secondary Objects Dev
        "f8a3f441-e328-4f5f-b1af-b63d5c51cd5e",  -- Apella Non-Personnel Prod
        "e4b5c374-0258-40de-b6fc-bf7c06f827c9"   -- Apella Personnel Prod
    )
group by encord_id, ds, org_id, site_id, image_bin_type, anonymized_image_id, anonymized_image_uri, frame_time_period
