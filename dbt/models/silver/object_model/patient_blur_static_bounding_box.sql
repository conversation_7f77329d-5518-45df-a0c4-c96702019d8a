{{ daily_config(produce_latest_snapshot=True, expiration_days=28) }}

-- to reduce the amount of object model results, filter to only
-- times where turnovers happened

with patient_objects as (
    select
        org_id,
        room_id,
        camera_id,
        objects.x1,
        objects.y1,
        objects.x2,
        objects.y2
    from {{ source("ml_project", "image_processing_output") }},
        unnest(object_model_output) as objects
    where
        frame_time > date_sub(timestamp({{ ds(as_date=False) }}), interval 7 day)
        and objects.label_display_name in ('or_table_occupied', 'patient_draped')
        and objects.confidence >= 0.9
),

patient_bounding_box as (
    select
        {{ ds() }} as ds,
        org_id,
        room_id,
        camera_id,
        avg(x1) as x1_mean,
        avg(y1) as y1_mean,
        avg(x2) as x2_mean,
        avg(y2) as y2_mean,
        stddev(x1) as x1_std,
        stddev(y1) as y1_std,
        stddev(x2) as x2_std,
        stddev(y2) as y2_std,
        count(*) as total
    from patient_objects
    group by org_id, room_id, camera_id
)

select
    ds,
    org_id,
    room_id,
    camera_id,
    -- the mean and stddev values are kept for posterity
    x1_mean,
    y1_mean,
    x2_mean,
    y2_mean,
    x1_std,
    y1_std,
    x2_std,
    y2_std,
    total,
    -- calculate the 95% confidence intervals on both sides of each point
    -- the consumer will likely want to use the bounding box corresponding to
    -- the points (x1_ci_lower, y1_ci_lower) and (x2_ci_upper, y2_ci_upper)
    greatest(x1_mean - 1.96 * (x1_std / sqrt(total)), 0) as x1_ci_lower,
    least(x1_mean + 1.96 * (x1_std / sqrt(total)), 1) as x1_ci_upper,
    greatest(y1_mean - 1.96 * (y1_std / sqrt(total)), 0) as y1_ci_lower,
    least(y1_mean + 1.96 * (y1_std / sqrt(total)), 1) as y1_ci_upper,
    greatest(x2_mean - 1.96 * (x2_std / sqrt(total)), 0) as x2_ci_lower,
    least(x2_mean + 1.96 * (x2_std / sqrt(total)), 1) as x2_ci_upper,
    greatest(y2_mean - 1.96 * (y2_std / sqrt(total)), 0) as y2_ci_lower,
    least(y2_mean + 1.96 * (y2_std / sqrt(total)), 1) as y2_ci_upper
from patient_bounding_box
-- In order to have a standard deviation, we need more than one data point
where total > 2
