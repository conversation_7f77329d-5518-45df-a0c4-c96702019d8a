{{ daily_config(produce_latest_snapshot=True) }}

select
    encord_id,
    ds,
    org_id,
    site_id,
    image_bin_type,
    anonymized_image_id,
    anonymized_image_uri,
    frame_time_period,
    project_hashes,
    project_names,
    object_labels
from {{ ref('encord_object_model_combined_labels') }} as encord_object_model_combined_labels
where
    encord_object_model_combined_labels.ds = {{ ds() }}
    -- only take images that were labeled in all projects (there should be 2)
    and array_length(project_hashes) = 2
