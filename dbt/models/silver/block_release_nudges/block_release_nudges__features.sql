{{ daily_config() }}

with schedule_asof_date_with_staff as (
    -- this is going to blow up core_cases a bit, repeating each row for each staff_id listed in the case,
    -- but the next query uses staff_id and at most one row per case will be kept, where the staff_id matches
    -- the block owner (we are limiting blocks to those with just one owner)
    select
        s_asof.case_id,
        s_asof.room_id,
        s_asof.asof_date as features_date,
        s_asof.surgery_date,
        s_asof.scheduled_start_time,
        s_asof.scheduled_end_time,
        cs.staff_id
    from {{ ref("future_block_utilization__schedule_asof_date") }} as s_asof
    inner join {{ api_table_snapshot("bronze", "public_case_staff", "cs") }}
        on s_asof.case_id = cs.case_id
    where s_asof.ds = {{ ds() }}
),

staff_iou as (
    select * from
        {{ ref("staff_intersection_over_union") }}
    where ds = {{ ds() }}
)

select
    {{ ds() }} as ds,
    ff.block_id,
    ff.block_time_id,
    ff.room_id,
    ff.block_time_date,
    ff.features_date,
    ff.days_to_surgery,
    -- in the following line, any aggregation method 'min/max/any_value' will work. We are grouping
    -- by block_time_id, and they only have a single start/end timestamp
    timestamp_diff(min(ff.block_time_end_timestamp), min(ff.block_time_start_timestamp), minute) as block_size_minutes,
    sum(coalesce(timestamp_diff(s_asof.scheduled_end_time, s_asof.scheduled_start_time, minute), 0))
        as total_scheduled_minutes,
    count(distinct s_asof.case_id) as case_count,
    -- next line is the size of the first gap, which is just first_case_start_timestamp - block_time_start_timestamp
    -- with a few caveats
    -- 1. if it doesn't exist (we have no cases), we coalesce to 0
    -- 2. we have to add a few min/max because we are not grouping by these times. Note that block times
    --   are constant given that we are grouping by block_time_id
    coalesce(
        timestamp_diff(min(s_asof.scheduled_start_time), min(ff.block_time_start_timestamp), minute), 0
    ) as first_gap_minutes,
    -- next line is the size of the last gap, which is just  block_time_end_timestamp - last_case_start_timestamp
    -- with a few caveats
    -- 1. if it doesn't exist (we have no cases), we coalesce to the block size
    -- 2. we have to add a few min/max because we are not grouping by these times. Note that block times
    --   are constant given that we are grouping by block_time_id
    coalesce(
        timestamp_diff(max(ff.block_time_end_timestamp), max(s_asof.scheduled_end_time), minute),
        timestamp_diff(max(ff.block_time_end_timestamp), max(ff.block_time_start_timestamp), minute)
    ) as last_gap_minutes,
    coalesce(max(staff_iou.iou), 0) as staff_iou
from {{ ref("block_release_nudges__for_features") }} as ff
-- left join because in the case where we have no cases, we still want to compute features
left outer join schedule_asof_date_with_staff as s_asof
    on
        ff.room_id = s_asof.room_id
        and ff.features_date = s_asof.features_date
        and ff.staff_id = s_asof.staff_id
        -- we need overlap in time (this is a very loose overlap)
        and not (
            ff.block_time_start_timestamp > s_asof.scheduled_end_time
            or ff.block_time_end_timestamp < s_asof.scheduled_start_time
        )
left outer join staff_iou
    on
        ff.staff_id = staff_iou.staff_id
        and ff.features_date = staff_iou.case_date
where ff.ds = {{ ds() }}
group by ff.block_id, ff.block_time_id, ff.room_id, ff.block_time_date, ff.features_date, ff.days_to_surgery
