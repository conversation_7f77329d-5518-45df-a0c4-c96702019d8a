{{ daily_config() }}

with block_averages_cte as (
    select
        run_id,
        run_timestamp,
        block_id,
        days_to_surgery,
        avg(if(y_true = y_hat, 1, 0)) as accuracy,
        count(*) as cnt,
        avg(if(y_true, 1, 0)) as avg_ytrue,
        avg(if(y_hat, 1, 0)) as avg_yhat
    from {{ api_table_snapshot("ml_analytics", "block_release_cross_validation_samples") }}
    group by run_id, run_timestamp, block_id, days_to_surgery
)

select
    {{ ds() }} as ds,
    initcap(s.last_name) as last_name,
    initcap(s.first_name) as first_name,
    s.org_id,
    ba.days_to_surgery,
    ba.accuracy,
    ba.cnt,
    ba.avg_ytrue,
    ba.avg_yhat,
    ba.block_id,
    ba.run_id,
    ba.run_timestamp
from block_averages_cte as ba
inner join `bronze.public_block_surgeons` as bs
    on ba.block_id = bs.block_id
inner join `bronze.public_staff` as s
    on bs.staff_id = s.id
