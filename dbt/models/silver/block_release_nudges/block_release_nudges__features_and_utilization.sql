{{ daily_config() }}

select
    {{ ds() }} as ds,
    features.block_id,
    features.block_time_date,
    features.features_date,
    features.days_to_surgery,
    features.total_scheduled_minutes,
    features.case_count,
    utilization_cte.scheduled_adjusted_utilization,
    utilization_cte.actual_adjusted_utilization
from {{ ref("block_release_nudges__features") }} as features
left outer join {{ ref("block_time_utilization_all_customers") }} as utilization_cte
    on
        features.block_id = utilization_cte.block_id
        and features.block_time_date = utilization_cte.block_time_date
where
    features.ds = {{ ds() }}
    and utilization_cte.ds = {{ ds() }}
