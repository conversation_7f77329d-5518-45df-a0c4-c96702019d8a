{{ daily_config() }}

with blocks_with_one_owner as (
    -- there is only one staff_id in the group, selecting any_value is just
    -- selecting the only value
    select
        block_id,
        any_value(staff_id) as staff_id
    from {{ api_table_snapshot("bronze", "public_block_surgeons") }}
    group by block_id
    having count(*) = 1
)

select
    {{ ds() }} as ds,
    bt.block_id,
    bt.id as block_time_id,
    bt.room_id,
    date(bt.start_time, s.timezone) as block_time_date,
    date_sub(date(bt.start_time, s.timezone), interval days_to_surgery day) as features_date,
    days_to_surgery,
    bt.start_time as block_time_start_timestamp,
    bt.end_time as block_time_end_timestamp,
    bwoo.staff_id
from blocks_with_one_owner as bwoo
left outer join {{ api_table_snapshot("bronze", "public_block_times", "bt") }}
    on bwoo.block_id = bt.block_id
inner join {{ api_table_snapshot("bronze", "public_rooms", "r") }}
    on bt.room_id = r.id
inner join {{ api_table_snapshot("bronze", "public_sites", "s") }}
    on r.site_id = s.id
cross join unnest(generate_array({{ min_days_to_nudge() }}, {{ max_days_to_nudge() }})) as days_to_surgery
where bt.id not in (select block_time_id from {{ api_table_snapshot("bronze", "public_block_time_releases") }})
