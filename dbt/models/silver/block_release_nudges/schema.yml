version: 1

models:
  - name: block_release_nudges__for_features
    description: >
      This dataframe collects information about block_times for which we need to compute features for,
      and the date when these features should be computed for.
      
      We have 4 timestamps to understand. Referring to them by some useful name has proven difficult, 
      I'm trying t1-t4, explained in the schema below
                                                                                                now                     block we might nudge about
               t1                                      t2                                        t3                                  t4
               |-------- days to surgery --------------|--- ... irrelevant amount of time ... ---|---------- days to surgery --------|
         features from here                  utilization from here                     features from here                  utilization from here
                \____We train the model with these____/                                           \___We use the model with these___/
      
      where:
      * t2 - t1 = t4 - t3
      * A given surgeon, may have multiple t4 today (a block in 8 days, a block in 12 days, a block in 22 days, ...)
      We have multiple copies of t1 and t2 for each t4
      * We need features at t1 and t3
      * in the code I refer to t1 and t3 as featuers_date
      * in the code I refer to t2 and t4 as surgery_date or block_time_date

    columns:
      - name: ds
        data_type: "timestamp"
      - name: block_id
        data_type: "string"
      - name: block_time_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: block_time_date
        data_type: "date"
      - name: features_date
        data_type: "date"
      - name: days_to_surgery
        data_type: "int"
      - name: block_time_start_timestamp
        data_type: "timestamp"
      - name: block_time_end_timestamp
        data_type: "timestamp"
      - name: staff_id
        data_type: "string"

  - name: block_release_nudges__features
    description: >
      These are the features we are using in the model. Currently we only compute the total number
      of scheduled minutes and the number of cases. We also add some columns to help debug
    columns:
      - name: ds
        data_type: "timestamp"
      - name: block_id
        data_type: "string"
      - name: block_time_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: block_time_date
        data_type: "date"
      - name: features_date
        data_type: "date"
      - name: days_to_surgery
        data_type: "int"
      - name: block_size_minutes
        data_type: "int"
      - name: total_scheduled_minutes,
        data_type: "int"
      - name: case_count
        data_type: "int"
      - name: first_gap_minutes
        data_type: "int"
        description: >
          number of minutes between the block starting and the scheduled first case starting. 
          Should never be null and could be negative if first case starts before the block.
          If there are no cases, the value should be 0
      - name: last_gap_minutes
        data_type: "int"
        description: >
          number of minutes between the scheduled end of the last surgery and end of the block. 
          Should never be null and could be negative if last case runs over the end of block time. 
          If there are no cases, the value should be block length in minutes.

  - name: block_release_nudges__features_and_utilization
    description: >
      just join features with utilization
    columns:
      - name: ds
        data_type: "timestamp"
      - name: block_id
        data_type: "string"
      - name: block_time_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: block_time_date
        data_type: "date"
      - name: features_date
        data_type: "date"
      - name: days_to_surgery
        data_type: "int"
      - name: total_scheduled_minutes,
        data_type: "int"
      - name: case_count
        data_type: "int"
      - name: scheduled_adjusted_utilization,
        data_type: "float"
      - name: actual_adjusted_utilization,
        data_type: "float"

  - name: block_release_nudges__cross_validation_stats
    description: >
      Aggregate all cross validation results for a given block_id and a run_id
    columns:
      - name: ds
        data_type: "timestamp"
      - name: block_id
        data_type: "string"
      - name: last_name
        data_type: "string"
      - name: first_name
        data_type: "string"
      - name: org_id
        data_type: "string"
      - name: days_to_surgery
        data_type: "int"
      - name: accuracy
        data_type: "float"
      - name: case_count
        data_type: "int"
      - name: avg_ytrue
        data_type: "float"
      - name: avg_yhat
        data_type: "float"
