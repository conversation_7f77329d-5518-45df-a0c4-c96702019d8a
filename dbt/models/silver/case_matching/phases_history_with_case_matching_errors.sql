{{ daily_config() }}

-- this first CTE extract the id of phases associated with more than one case_id
-- these are the phases that were incorrectly case matched. We just extract the id here
-- in the next query we use them to select all data from them
with case_matching_errors as (
    select pph.id
    from {{ source("bronze", "public_phases_history") }} as pph
    where case_id is not null
    group by pph.id
    having count(distinct case_id) > 1
)

select
    {{ ds() }} as ds,
    pph.id,
    pph.version,
    pph.created_time,
    pph.updated_time,
    pph.org_id,
    pph.site_id,
    pph.room_id,
    pph.case_id,
    pph.type_id,
    pph.start_event_id,
    pph.end_event_id,
    pph.event_matching_status,
    pph.source_type,
    pph.status,
    pph.invalidation_reason
from {{ source("bronze", "public_phases_history") }} as pph
inner join case_matching_errors
    on pph.id = case_matching_errors.id
