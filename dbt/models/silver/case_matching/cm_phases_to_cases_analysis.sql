{{ daily_config(produce_latest_snapshot=True) }}

with cases as (
    select *
    from {{ ref('cm_cases_with_obx_times') }}
    where ds = {{ ds() }}
),

phases as (
    select *
    from {{ ref('cm_phases_with_times_and_history') }}
    where ds = {{ ds() }}
),

metrics as (
    select
        phases.phase_id,
        count(distinct cases.case_id) as agreed_cases_count,
        {{ cm_calculate_valid_matches() }}
    from phases
    left outer join cases
        on
            phases.room_id = cases.room_id
            and cases.observed_in_room_time_utc between phases.phase_start_time_utc
            - interval 15 minute and phases.phase_start_time_utc
            + interval 15 minute
            and cases.observed_out_of_room_time_utc between phases.phase_end_time_utc
            - interval 15 minute and phases.phase_end_time_utc
            + interval 15 minute
    group by phases.phase_id
)


select
    metrics.phase_id,
    metrics.agreed_cases_count,
    metrics.is_valid_match_now,
    metrics.was_valid_match_immediate,
    metrics.was_valid_match_three_minutes,
    metrics.was_valid_match_five_minutes,
    phases.ds,
    phases.current_case_id,
    phases.has_history,
    phases.immediate_case_id,
    phases.three_minute_case_id,
    phases.five_minute_case_id,
    phases.phase_start_time_utc,
    phases.phase_end_time_utc,
    phases.room_id,
    phases.site_id
from phases
inner join metrics
    on phases.phase_id = metrics.phase_id
where phases.ds = {{ ds() }}
