{{ daily_config(produce_latest_snapshot=True) }}

with cases as (
    select *
    from {{ ref('cm_cases_with_obx_times') }}
    where ds = {{ ds() }}
),

phases as (
    select *
    from {{ ref('cm_phases_with_times_and_history') }}
    where ds = {{ ds() }}
),

metrics as (
    select
        cases.case_id,
        count(distinct phases.phase_id) as agreed_phases_count,
        {{ cm_calculate_valid_matches() }}
    from cases
    left outer join phases
        on
            cases.room_id = phases.room_id
            and phases.phase_start_time_utc between cases.observed_in_room_time_utc
            - interval 15 minute and cases.observed_in_room_time_utc
            + interval 15 minute
            and phases.phase_end_time_utc between cases.observed_out_of_room_time_utc
            - interval 15 minute and cases.observed_out_of_room_time_utc
            + interval 15 minute
    group by cases.case_id
)

select
    metrics.case_id,
    metrics.agreed_phases_count,
    metrics.is_valid_match_now,
    metrics.was_valid_match_immediate,
    metrics.was_valid_match_three_minutes,
    metrics.was_valid_match_five_minutes,
    cases.ds,
    cases.room_id,
    cases.external_case_id,
    cases.site_id,
    cases.observed_in_room_time_utc,
    cases.observed_out_of_room_time_utc
from cases
inner join metrics
    on cases.case_id = metrics.case_id
where cases.ds = {{ ds() }}
