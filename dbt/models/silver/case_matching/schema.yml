version: 1

models:
  - name: phases_history_with_case_matching_errors
    description: >
      This table selects all the phases that have ever been identified with more than one case_id 
      (AKA: a case matching error)
    columns:
      - name: ds
        data_type: "timestamp"
      - name: id
        data_type: "string"
      - name: version
        data_type: "integer"
      - name: created_time
        data_type: "timestamp"
      - name: updated_time
        data_type: "timestamp"
      - name: org_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: case_id
        data_type: "string"
      - name: type_id
        data_type: "string"
      - name: start_event_id
        data_type: "string"
      - name: end_event_id
        data_type: "string"
      - name: event_matching_status
        data_type: "string"
      - name: source_type
        data_type: "string"
      - name: status
        data_type: "string"
      - name: invalidation_reason
        data_type: "string"
  - name: cm_cases_with_obx_times
    description: >
      This table contains all of our EHR Cases joined with the OBX in and out of room times
      If the case has no OBX times, it will have NULL values for the OBX times
    columns:
      - name: ds
        data_type: "timestamp"
      - name: case_id
        data_type: "string"
      - name: observed_in_room_time_utc
        data_type: "timestamp"
      - name: observed_out_of_room_time_utc
        data_type: "timestamp"
      - name: room_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: external_case_id
        data_type: "string"
  - name: cm_phases_with_times_and_history
    description: >
        This table contains all of Event Model Case Phases with their start and end times
        and the Case id that it had immediately, within 3 minutes of the start,
        within 5 minutes of the start, and now
    columns:
      - name: ds
        data_type: "timestamp"
      - name: phase_id
        data_type: "string"
      - name: current_case_id
        data_type: "string"
      - name: has_history
        data_type: "string"
      - name: immediate_case_id
        data_type: "string"
        description: >
          The first case_id that was associated with the phase within 5 minutes of the start event
          occurring
      - name: three_minute_case_id
        data_type: "string"
        description: >
          The last case_id that was associated with the phase within 3 minutes of the start event
            occurring
      - name: five_minute_case_id
        data_type: "string"
        description: >
          The last case_id that was associated with the phase within 5 minutes of the start event
            occurring
      - name: phase_start_time_utc
        data_type: "timestamp"
      - name: phase_end_time_utc
        data_type: "timestamp"
      - name: room_id
        data_type: "string"
      - name: site_id
        data_type: "string"
  - name: cm_cases_to_phases_analysis
    description: >
      This table contains an analysis of how good our case to phase matching was for each scenario
      immediately after it started, within 3 minutes of the start,
      within 5 minutes of the start, and now
    columns:
      - name: ds
        data_type: "timestamp"
      - name: case_id
        data_type: "string"
      - name: observed_in_room_time
        data_type: "timestamp"
      - name: observed_out_of_room_time
        data_type: "timestamp"
      - name: room_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: external_case_id
        data_type: "string"
      - name: agreed_phases_count
        data_type: "integer"
        description: >
          The number of phases that are in agreement with the case on start and end times
      - name: is_valid_match_now
        data_type: "boolean"
        description: >
          Whether the case is in agreement a single phase on start and end times and that phase is
          currently matched to the case
      - name: was_valid_match_immediately
        data_type: "boolean"
        description: >
          Whether the case is in agreement a single phase on start and end times and that phase was
          matched immediately to the case
      - name: was_valid_match_after_three_minutes
        data_type: "boolean"
        description: >
          Whether the case is in agreement a single phase on start and end times and that phase was
          matched within 3 minutes to the case
      - name: was_valid_match_after_five_minutes
        data_type: "boolean"
        description: >
          Whether the case is in agreement a single phase on start and end times and that phase was
          matched within 5 minutes to the case
  - name: cm_phases_to_case_analysis
    description: >
      This table contains an analysis of how good our phase to case matching was for each scenario
      immediately after it started, within 3 minutes of the start,
      within 5 minutes of the start, and now
    columns:
      - name: ds
        data_type: "timestamp"
      - name: phase_id
        data_type: "string"
      - name: case_id
        data_type: "string"
      - name: history_id
        data_type: "string"
      - name: immediate_case_id
        data_type: "string"
      - name: three_minute_case_id
        data_type: "string"
      - name: five_minute_case_id
        data_type: "string"
      - name: phase_start_time_utc
        data_type: "timestamp"
      - name: phase_end_time_utc
        data_type: "timestamp"
      - name: room_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: agreed_cases_count
        data_type: "integer"
      - name: is_valid_match_now
        data_type: "boolean"
      - name: was_valid_match_immediately
        data_type: "boolean"
      - name: was_valid_match_after_three_minutes
        data_type: "boolean"
      - name: was_valid_match_after_five_minutes
        data_type: "boolean"
