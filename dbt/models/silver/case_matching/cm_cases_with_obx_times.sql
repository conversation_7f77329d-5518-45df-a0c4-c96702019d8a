{{ daily_config() }}

with cases_to_obx as (
    select
        ehr_cases.case_id,
        observations.observation_time,
        ehr_cases.room_id,
        observations.type_id,
        ehr_cases.site_id,
        ehr_cases.external_case_id
    from
        {{ api_table_snapshot('bronze', 'public_cases', 'ehr_cases') }}
    left outer join
        {{ api_table_snapshot('bronze', 'public_observations', 'observations') }}
        on ehr_cases.case_id = observations.case_id
    where
        (observations.type_id is null or observations.type_id in ('OBSERVED_IN_ROOM', 'OBSERVED_OUT_OF_ROOM'))
        and ehr_cases.status = 'scheduled'
    qualify row_number() over (
        partition by ehr_cases.case_id, observations.type_id order by observations.created_time desc nulls first
    ) = 1
)

select
    {{ ds() }} as ds,
    case_id,
    room_id,
    external_case_id,
    site_id,
    observation_time_observed_in_room_time as observed_in_room_time_utc,
    observation_time_observed_out_of_room_time as observed_out_of_room_time_utc
from (
    select
        case_id,
        room_id,
        external_case_id,
        site_id,
        observation_time,
        type_id
    from
        cases_to_obx
)
pivot (
    any_value(observation_time) as observation_time
    for type_id in ('OBSERVED_IN_ROOM' as observed_in_room_time, 'OBSERVED_OUT_OF_ROOM' as observed_out_of_room_time)
)
