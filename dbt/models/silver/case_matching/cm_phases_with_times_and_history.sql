{{ daily_config() }}

with has_history as (
    select phases.id
    from {{ api_table_snapshot('bronze', 'public_phases_history', 'phases') }}
    inner join {{ api_table_snapshot('bronze', 'public_events', 'events') }}
        on phases.start_event_id = events.id
    where
        phases.updated_time <= (events.start_time + interval 5 minute)
        and {{ cm_phase_case_filter() }}
    group by phases.id
),

phases_to_check as (
    select
        phases.id,
        phases.case_id,
        phases.start_event_id,
        phases.end_event_id,
        phases.room_id,
        phases.site_id
    from {{ api_table_snapshot('bronze', 'public_phases', 'phases') }}
    where {{ cm_phase_case_filter() }}
)

select
    {{ ds() }} as ds,
    phases.id as phase_id,
    phases.case_id as current_case_id,
    coalesce(has_history.id is not null, false) as has_history,
    case_id_immediate.case_id as immediate_case_id,
    case_id_three_minutes.case_id as three_minute_case_id,
    case_id_five_minutes.case_id as five_minute_case_id,
    start_event.start_time as phase_start_time_utc,
    end_event.start_time as phase_end_time_utc,
    phases.room_id,
    phases.site_id
from phases_to_check as phases
inner join {{ api_table_snapshot('bronze', 'public_events', 'start_event') }}
    on
        start_event.id = phases.start_event_id
        and start_event.source_type in ('human_gt', 'prediction')
        and start_event.deleted_at is null
inner join {{ api_table_snapshot('bronze', 'public_events', 'end_event') }}
    on
        end_event.id = phases.end_event_id
        and end_event.source_type in ('human_gt', 'prediction')
        and end_event.deleted_at is null
left outer join {{ cm_get_phase_case_id_history(5, 'asc') }} as case_id_immediate
    on
        case_id_immediate.id = phases.id
left outer join {{ cm_get_phase_case_id_history(3, 'desc') }} as case_id_three_minutes
    on
        case_id_three_minutes.id = phases.id
left outer join {{ cm_get_phase_case_id_history(5, 'desc') }} as case_id_five_minutes
    on
        case_id_five_minutes.id = phases.id
left outer join has_history as has_history
    on has_history.id = phases.id
