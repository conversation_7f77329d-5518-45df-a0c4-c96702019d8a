{{ daily_config() }}

-- The goal of this model is to determine what time periods each day we
-- have ground truth for each event type, and how many events of each type
-- were annotated.

-- This just unnests the annotation_task_types_history table across
-- event_type_id
with annotation_task_types_history_unnested as (
    select
        id as annotation_type_id,
        event_type_id,
        version as annotation_type_version
    from {{ api_table_snapshot("bronze", "public_annotation_task_types_history") }},
        unnest(json_value_array(event_types)) as event_type_id
),

-- This joins the completed tasks model with the unnested task types
-- to "roll them out" by event type.
annotation_tasks_history_unnested as (
    select
        completed_tasks_each_day.org_id,
        completed_tasks_each_day.site_id,
        completed_tasks_each_day.room_id,
        annotation_task_types_history_unnested.event_type_id,
        completed_tasks_each_day.start_time,
        completed_tasks_each_day.end_time,
        completed_tasks_each_day.start_date,
        completed_tasks_each_day.timezone
    from
        {{ ref("event_training_annotation_tasks_completed_each_day") }}
            as completed_tasks_each_day
    inner join annotation_task_types_history_unnested
        on
            completed_tasks_each_day.type_id
            = annotation_task_types_history_unnested.annotation_type_id
            and completed_tasks_each_day.type_version
            = annotation_task_types_history_unnested.annotation_type_version
            and completed_tasks_each_day.ds = {{ ds() }}
),

-- Now, we want to find contiguous periods of time where we have
-- completed task types.  We do this by first finding the gap in time
-- between two tasks.
--
-- Note: this works well if tasks are sequential, like one task ends at
-- 10:00 and the next starts at 10:00.  And it will work if they overlap,
-- like one task ends at 10:00, and the next starts at 9:30.  But, it will
-- incorrectly find lag if there are tasks that are contained by other
-- tasks, such as one task from 6am to 10am, and another from 7am to 9am.
-- In other words, this only works properly if this is true:
--   if a.start_time > b.start_time, then a.end_time > b.end_time.
tasks_with_lag as (
    select
        org_id,
        site_id,
        room_id,
        event_type_id,
        start_time,
        end_time,
        start_date,
        timezone,
        -- this is the time gap between the current task and the previous task
        -- (ordered by start time, partitioned by room/event/date)
        timestamp_diff(
            start_time,
            lag(end_time)
                over (
                    partition by room_id, event_type_id, start_date
                    order by start_time
                ),
            second
        ) as time_gap_sec
    from annotation_tasks_history_unnested
),

-- Then, we add a column that indicates whether there is a gap or not.
new_group_inds as (
    -- adds an indicator for whether there's a gap
    select
        org_id,
        site_id,
        room_id,
        event_type_id,
        start_time,
        end_time,
        start_date,
        timezone,
        -- if there's a gap, then this column is 1, else 0
        case when (
            time_gap_sec is null
            or time_gap_sec > 0
        ) then 1 else 0 end as new_group_ind
    from tasks_with_lag
),

-- Then, we can group them together by having a running sum of the
-- new_group_ind column.  So, the first group will be 0, then 1, then 2,
-- etc.
group_nums as (
    select
        org_id,
        site_id,
        room_id,
        event_type_id,
        start_time,
        end_time,
        start_date,
        timezone,
        -- Create a running sum of the group ind column
        sum(new_group_ind)
            over (
                partition by room_id, event_type_id, start_date
                order by start_time
            )
            as group_num
    from new_group_inds
),

-- Lastly, we just join all of the periods with the same group_num
-- together, and take the min/max of the start/end times.
ground_truth_periods_combined as (
    select
        org_id,
        site_id,
        room_id,
        group_num,
        start_date,
        event_type_id,
        timezone,
        min(start_time) as start_time,
        max(end_time) as end_time
    from group_nums
    group by
        org_id, site_id, room_id, group_num, start_date, event_type_id, timezone
    order by start_date
),

-- Now that we have the tasks combined, we can count the number of events of
-- that type in that time period.
ground_truth_periods_with_event_counts as (
    select
        ground_truth_periods_combined.start_date,
        ground_truth_periods_combined.org_id,
        ground_truth_periods_combined.site_id,
        ground_truth_periods_combined.room_id,
        ground_truth_periods_combined.event_type_id,
        ground_truth_periods_combined.group_num,
        ground_truth_periods_combined.start_time,
        ground_truth_periods_combined.end_time,
        ground_truth_periods_combined.timezone,
        -- I tried generate_uuid() here, but that seemed to be inconsistent and the join in
        -- ground_truth_periods_with_error_counts failed to find any matches
        concat(
            ground_truth_periods_combined.room_id,
            ground_truth_periods_combined.event_type_id,
            ground_truth_periods_combined.start_time
        ) as id,
        count(*) as event_count
    from ground_truth_periods_combined
    inner join {{ api_table_snapshot("bronze", "public_events", alias="event") }}
        on
            ground_truth_periods_combined.room_id = event.room_id
            and ground_truth_periods_combined.start_time <= event.start_time
            and ground_truth_periods_combined.end_time >= event.start_time
            and ground_truth_periods_combined.event_type_id
            = event.event_type_id
            and event.source_type = 'human_gt'
            and event.deleted_at is null
    group by
        ground_truth_periods_combined.start_date,
        ground_truth_periods_combined.org_id,
        ground_truth_periods_combined.site_id,
        ground_truth_periods_combined.room_id,
        ground_truth_periods_combined.event_type_id,
        ground_truth_periods_combined.group_num,
        ground_truth_periods_combined.start_time,
        ground_truth_periods_combined.end_time,
        ground_truth_periods_combined.timezone
),

errors as (
    -- We want to pick OR-day with more errors.
    -- This uses the "all prediction versions matched" which is an approximation
    -- of this.  If the model predicted an error, and then updated it, that error
    -- will be counted twice.  So there could be more errors than events, but that
    -- is okay for this purpose.  It also doesn't handle errors of duplicates, like if
    -- the model predicted a patient draped, then deleted it, then added it again, that would
    -- not be counted as an error.
    select
        -- We select the ids, even though we don't use them, so we can group by them
        ground_truth_id,
        prediction_id,
        room_id,
        event_type_id,
        -- The event time can be different for different errors of the same event,
        -- so we can't just group by it, but we can pick the earliest event_time, since
        -- that will either be the ground truth time, or the first false positive time.
        min(event_time) as event_time
    from {{ ref("event_model_all_prediction_versions_matched") }}
    where
        ds = {{ ds() }}
        and environment = '{{ target.name }}'
        and prediction_class != 'true_positive'
    -- We group by both IDs so that if the same error is predicted multiple times, it is only counted once
    group by ground_truth_id, prediction_id, room_id, event_type_id
),

error_counts_per_gtp as (
    select
        gtps.id,
        countif(errors.room_id is not null) as error_count
    from ground_truth_periods_with_event_counts as gtps
    left outer join errors
        on
            gtps.room_id = errors.room_id
            and gtps.start_time <= errors.event_time
            and gtps.end_time >= errors.event_time
            and gtps.event_type_id = errors.event_type_id
    group by
        gtps.id
),

ground_truth_periods_with_error_counts as (
    select
        gtps.*,
        coalesce(error_counts_per_gtp.error_count, 0) as error_count
    from ground_truth_periods_with_event_counts as gtps
    left outer join error_counts_per_gtp
        on gtps.id = error_counts_per_gtp.id
)

select
    {{ ds() }} as ds,
    org_id,
    site_id,
    room_id,
    event_type_id,
    start_time,
    end_time,
    start_date,
    timezone,
    event_count,
    error_count
from ground_truth_periods_with_error_counts
