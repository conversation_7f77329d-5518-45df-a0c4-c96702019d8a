version: 1

models:
  - name: event_training_annotation_tasks_completed_each_day
    description: >
      This table attaches a 'start_date' and context like timezone to
      every annotation task completed.  And if the annotation task crosses
      the midnight boundary from one date to the next, in the timezone
      of the site, it will be split into two rows.

    columns:
      - name: ds
        data_type: "timestamp"
      - name: org_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: type_id
        data_type: "string"
        description: >
          This is the same field as "annotation_task_types.id", "annotation_task_types_history.id",
          "annotation_tasks.type_id" and "annotation_task_history.type_id" (not to be confused 
          with an event type id). This "type_id" are abstract strings that, in conjunction with 
          their "type_version", define all the event_types that were annotated. ie, an example of
          these "type_id" would be "daba588e-3354-45d5-bb34-c1ac33cce5b6" (for which we have at
          least 2 versions)
      - name: type_version
        data_type: "int"
        description: >
          Annotations completed with the same type_id, might correspond to different versions.
          The tuple (type_id, type_version) uniquely defines what was annotated on a given date.
      - name: start_time
        data_type: "timestamp"
      - name: end_time
        data_type: "timestamp"
      - name: start_date
        data_type: "string"
        description: The date of the start time in the timezone of the site
      - name: timezone
        data_type: "string"

  - name: event_training_ground_truth_periods
    description: >
      This table contains a row for each event type annotated, for each OR-Day,
      for each contiguous period of time that that event type was annotated
      in that room.
    columns:
      - name: ds
        data_type: "timestamp"
      - name: org_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: event_type_id
        data_type: "string"
      - name: start_time
        data_type: "timestamp"
      - name: end_time
        data_type: "timestamp"
      - name: start_date
        data_type: "string"
      - name: timezone
        data_type: "string"
      - name: event_count
        description: The count of this event type during this time period
        data_type: "int"
      - name: error_count
        description: The count of errors for this event type during this time period
        data_type: "int"

  - name: event_training_or_days
    description: >
      This table pulls together everything we know about an OR-Day, such as
      all of the ground truth periods in that OR-Day, as well as the total
      counts of the events and the percentage of image frames found (to be
      used to filter out outages). This table can be used to determine which
      OR-Days to include in event model training.
    columns:
      - name: ds
        data_type: "timestamp"
      - name: org_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: start_time
        data_type: "timestamp"
      - name: end_time
        data_type: "timestamp"
      - name: start_date
        data_type: "string"
      - name: timezone
        data_type: "string"
      - name: event_count
        description: The total count of ground truth events for this OR-Day
      - name: error_count
        description: The total count of errors for this OR-Day
        data_type: "int"
      - name: frame_percent
        description: The percentage of image frames processed by image processor for this OR-Day
        data_type: "float"
      - name: embeddings_frame_percent
        description:
          The percentage of image frames with embeddings data for this OR-Day, for each embedding model
        data_type: "array<struct<str, float>>"
      - name: ground_truth_periods
        data_type:  "array<struct<timestamp, timestamp, string, int, int>>"
      - name: bin_type
        description: "Pre-allocated bin of either 'train' or 'test'"
        data_type:  "string"
