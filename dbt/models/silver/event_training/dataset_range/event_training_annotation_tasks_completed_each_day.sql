{{ daily_config() }}

-- The goal of this model is to split annotation tasks by date (in the
-- timezone of the site), because we treat the OR-Day as a unit, and this
-- will make it easier to understand what tasks were completed on each day.

with completed_tasks_history as (
    -- Get all completed tasks that match our query
    select
        annotation_tasks_history.room_id,
        annotation_tasks_history.org_id,
        annotation_tasks_history.site_id,
        annotation_tasks_history.type_id,
        annotation_tasks_history.start_time,
        annotation_tasks_history.end_time,
        site.timezone as timezone,
        annotation_tasks_history.type_version
    from
        {{ api_table_snapshot("bronze", "public_annotation_tasks_history",
         alias="annotation_tasks_history") }}
    inner join {{ api_table_snapshot("bronze", "public_sites", "site") }}
        on annotation_tasks_history.site_id = site.id
    where
        annotation_tasks_history.status = 'DONE'
)

-- This query works by selecting from the `completed_tasks_history` cte twice,
-- once for the "first day" that it overlaps, and once for the "second day"
-- that it overlaps.
--
-- The first day has a "task_date" of the date of the start_time, and the
-- second day has a "task_date" of the date of the end_time.  We then
-- union both of these together into a single table, and then group them
-- by the task_date.  If the task is contained within a single day, then
-- both tasks will be grouped together.  If the task spans two days, then
-- the tasks will not be grouped together.
--
-- At the moment, the only handles cases where there is a single midnight
-- crossing.  If we have annotation tasks that are > 24 hours, this query
-- will not work correctly, and will ignore the middle 24 hours.
select
    {{ ds() }} as ds,
    org_id,
    site_id,
    room_id,
    type_id,
    type_version,
    start_time,
    end_time,
    task_date as start_date,
    timezone
from (
    -- Generate a row for the "first day" that each task overlaps
    select
        date(start_time, timezone) as task_date,
        org_id,
        site_id,
        room_id,
        type_id,
        type_version,
        start_time,
        -- Clamp the end time to the start of the next day
        least(
            timestamp_trunc(start_time + interval 1 day, day, timezone),
            end_time
        ) as end_time,
        timezone
    from completed_tasks_history
    union all
    -- Generate a row for the "second day" that each task overlaps
    select
        date(end_time, timezone) as task_date,
        org_id,
        site_id,
        room_id,
        type_id,
        type_version,
        -- Clamp the start time to the start of this day
        greatest(
            timestamp_trunc(end_time, day, timezone),
            start_time
        ) as start_time,
        end_time,
        timezone
    from completed_tasks_history
)
-- Group them together.  If the task only overlaps 1 day, both the above will
-- be the same, and will be de-duplicated.
group by
    ds,
    org_id,
    site_id,
    room_id,
    type_id,
    type_version,
    start_time,
    end_time,
    task_date,
    timezone
