{{ daily_config() }}

-- The goal of this model is to collect everything we know about an OR-day
-- together, so that the training code can simply query and filter this model
-- (by date, min events, etc) to get the dataset

-- Assuming 1 frame per 5 seconds from each camera
{% set frames_per_second = 0.2 %}

with all_or_days as (
    -- Combine all of the time periods for a day, across all types
    select
        gtps.start_date,
        gtps.org_id,
        gtps.site_id,
        gtps.room_id,
        gtps.timezone,
        min(gtps.start_time) as start_time,
        max(gtps.end_time) as end_time,
        sum(gtps.event_count) as event_count,
        sum(gtps.error_count) as error_count,
        array_agg(struct(
            gtps.start_time,
            gtps.end_time,
            gtps.event_type_id,
            gtps.event_count,
            gtps.error_count
        )) as ground_truth_periods
    from {{ ref("event_training_ground_truth_periods") }} as gtps
    where gtps.ds = {{ ds() }}
    group by start_date, org_id, site_id, room_id, timezone
),

-- We don't want to train on days with major outages.  So, we can
-- try to detect them by looking at the number of frames we have
-- found in that day, and compare that to the number we expect.
-- To do that, we first need to know how many cameras are in each room
cameras_per_room as (
    select
        room_id,
        count(*) as camera_count
    from {{ api_table_snapshot("bronze", "public_cameras") }}
    group by room_id
),

all_or_days_with_expected_frame_count as (
    select
        or_days.*,
        -- The expected_approximate_frame_count is the number of frames we approximately expect to
        -- have if all cameras were running all day.  We use this to easily determine if there
        -- was an outage, so we can filter out days with large outages.
        timestamp_diff(or_days.end_time, or_days.start_time, second)
        * cameras_per_room.camera_count * {{ frames_per_second }} as expected_approximate_frame_count
    from all_or_days as or_days
    inner join cameras_per_room
        on or_days.room_id = cameras_per_room.room_id
),

or_day_object_frame_percent as (
    select
        or_days.start_date,
        or_days.room_id,
        count(*) / or_days.expected_approximate_frame_count as frame_percent
    from all_or_days_with_expected_frame_count as or_days
    inner join
        {{ api_table_snapshot("ml_project", "image_processing_output",
        alias="image_processing_output") }}
        on
            or_days.room_id = image_processing_output.room_id
            and or_days.start_time <= image_processing_output.frame_time
            and or_days.end_time >= image_processing_output.frame_time
    group by
        or_days.start_date,
        or_days.room_id,
        or_days.expected_approximate_frame_count
),

or_day_embeddings_frame_totals_per_model as (
    select
        or_days.start_date,
        or_days.room_id,
        image_embeddings.model_name,
        count(*) / or_days.expected_approximate_frame_count as frame_percent
    from all_or_days_with_expected_frame_count as or_days
    inner join
        {{ api_table_snapshot("ml_project", "image_embeddings",
        alias="image_embeddings") }}
        on
            or_days.room_id = image_embeddings.room_id
            and or_days.start_time <= image_embeddings.frame_time
            and or_days.end_time >= image_embeddings.frame_time
    group by
        or_days.start_date,
        or_days.room_id,
        image_embeddings.model_name,
        or_days.expected_approximate_frame_count
),

or_day_embeddings_frame_percents as (
    select
        start_date,
        room_id,
        array_agg(struct(
            model_name as model_name,
            frame_percent as frame_percent
        )) as frame_percents
    from or_day_embeddings_frame_totals_per_model
    group by start_date, room_id
),

or_days_with_frame_totals as (
    select
        or_days.start_date,
        or_days.org_id,
        or_days.site_id,
        or_days.room_id,
        or_days.timezone,
        or_days.start_time,
        or_days.end_time,
        or_days.event_count,
        or_days.error_count,
        or_days.ground_truth_periods,
        or_day_object_frame_percent.frame_percent as frame_percent,
        or_day_embeddings_frame_percents.frame_percents as embeddings_frame_percent
    from all_or_days_with_expected_frame_count as or_days
    left outer join or_day_object_frame_percent
        on
            or_days.start_date = or_day_object_frame_percent.start_date
            and or_days.room_id = or_day_object_frame_percent.room_id
    left outer join or_day_embeddings_frame_percents
        on
            or_days.start_date = or_day_embeddings_frame_percents.start_date
            and or_days.room_id = or_day_embeddings_frame_percents.room_id
),

or_days_with_bin_type as (
    select
        or_days_with_frame_totals.*,
        -- We have already allocated train/test/validate days in the table
        -- `prod-ml-2fc132.prod_ml_cv_dataset.model_precalculated_bins`
        -- So we can join with that table
        bins.bin_type as bin_type
    from or_days_with_frame_totals
    left outer join {{ api_table_snapshot("ml_cv_dataset", "model_precalculated_bins",
        alias="bins") }}
    -- Note: the timestamp in image_frame_date is at pacific midnight (outstanding bug)
    -- but that doesn't affect this logic because the date of any midnight timestamp for the US
    -- timezones will be the same.  This does mean some images do leak between train and test,
    -- but those are in the middle of the night, and once the bug is fixed on the pre-calculated
        -- bins, this will be a non-issue.
        on
            or_days_with_frame_totals.start_date = date(bins.image_frame_date)
            and or_days_with_frame_totals.room_id = bins.room_id
)

select
    {{ ds() }} as ds,
    start_date,
    room_id,
    org_id,
    site_id,
    timezone,
    start_time,
    end_time,
    event_count,
    error_count,
    frame_percent,
    embeddings_frame_percent,
    ground_truth_periods,
    bin_type
from or_days_with_bin_type
