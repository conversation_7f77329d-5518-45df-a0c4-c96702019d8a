{{ daily_config() }}

with core_cases_with_date as (
    -- just add surgery_date and minutes_case_started_before_schedule to core cases
    select
        *,
        datetime_diff(scheduled_start_datetime_local, actual_start_datetime_local, minute)
            as minutes_case_started_before_schedule,
        date(scheduled_start_datetime_local) as surgery_date
    from {{ ref("core_cases") }}
    where
        ds = {{ ds() }}
        and status = 'scheduled'
        and is_case_matched
),

cases_with_prev_case as (
    -- just add prev_scheduled_end and prev_actual_end (partitioned by date and room)
    select
        *,
        lag(scheduled_end_datetime_local)
            over (partition by room_id, surgery_date order by actual_start_datetime_local asc)
            as prev_scheduled_end,
        lag(actual_end_datetime_local)
            over (partition by room_id, surgery_date order by actual_start_datetime_local asc)
            as prev_actual_end,
        -- TODO replace hardcoded definition of prime_time_start once prime_time_in_tall_format
        -- 	is merged in the DW
        datetime(date(scheduled_start_datetime_local), time(7, 0, 0)) as prime_time_start
    from core_cases_with_date
),

add_turnover_time as (
    -- if a case is the first in a day, there will be no prev_scheduled/actual_end.
    -- in that case, use prime_time_start. Logic: if prime_time_start is at 7:30AM and the
    -- case is scheduled at 8:00AM, forward_mobility should capture the team trying to start the case
    -- at 7:30AM
    select
        -- scheduled_start                							↓
        -- prev_actual_end        			   ↓
        -- tot duration              			------------
        -- minutes_room_open_before_schedule.  				-------
        c.*,
        datetime_diff(
            c.scheduled_start_datetime_local,
            coalesce(c.prev_actual_end, c.prime_time_start),
            minute
        ) - t.median_minutes_28_days as minutes_room_open_before_schedule
    from cases_with_prev_case as c
    inner join {{ ref("turnover_stats_by_service_line") }} as t
        on
            c.service_line_id = t.service_line_id
            and c.site_id = t.site_id
    where
        c.prev_scheduled_end is not null
        and t.ds = {{ ds() }}
)

select
    {{ ds() }} as ds,
    apella_case_id,
    minutes_room_open_before_schedule,
    minutes_case_started_before_schedule,
    minutes_case_started_before_schedule / minutes_room_open_before_schedule as forward_mobility
from add_turnover_time
where
    true
    and minutes_case_started_before_schedule > 0
    and minutes_room_open_before_schedule > 0
