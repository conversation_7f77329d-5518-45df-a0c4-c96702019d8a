version: 1

models:
  - name: case_forward_mobility
    description: >
      Forward mobility is a measure of how much time the site is "gaining" by moving the case forward
      in time when possible. The gap at the beginning of the case can be created because there
      is a gap in the schedule or the prev case ended before scheduled. Regardless, there is an
      opportunity for the site to use these minutes and create gaps later on and/or avoid extra time
      
      Example:
      - denotes case
      # denotes TOT
                           case1              case2
      scheduled     ---------------------|-------------
      actual_1     ----------------###    -------------   no forward time
      actual_2     ----------------###  -------------     forward time equivalent to 2 time unit
      actual_3     ----------------###-------------       forward time equivalent to 4 time unit
    columns:
      - name: ds
        data_type: "timestamp"
      - name: apella_case_id
        data_type: "string"
      - name: minutes_case_started_before_schedule
        data_type: "int"
        tests:
          - not_null
      - name: minutes_room_open_before_schedule
        data_type: "int"
        tests:
          - not_null
      - name: forward_mobility
        data_type: "float"
        description: The ratio between forward_mobility_minutes / available_mobility_minutes

  - name: case_unexpected_delays
    description: >
      An unexpected delay is the amount of minutes a case starts after it was scheduled that can't
      be attributed to delays in the previous case (when turnover time is taken into account)
      
      Exmaple 1:
      prev_case_ends         ↓
      prev turnover ends         ↓
      scheduled start                ↓
      actual start                          ↓
      unexpected_delay                ------
      
      Exmaple 2:
      prev_case_ends              ↓
      prev turnover ends               ↓
      scheduled start                ↓
      actual start                     ↓
      unexpected_delay                 0 (there is no unexpected delay, case started ASAP)
      
      Exmaple 3:
      prev_case_ends              ↓
      prev turnover ends               ↓
      scheduled start                ↓
      actual start                          ↓
      unexpected_delay                 ------ (It is true that prev case was delayed, but this
                                                case did not start ASAP)
    columns:
      - name: ds
        data_type: "timestamp"
      - name: apella_case_id
      - name: unexpected_delay_minutes
        data_type: "int"
        tests:
         - not_null
         - accepted_range:
             min_value: 0
