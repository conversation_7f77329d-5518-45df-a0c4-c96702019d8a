{{ daily_config() }}

with cases_with_date as (
    select
        *,
        date(scheduled_start_datetime_local) as surgery_date
    from {{ ref("core_cases") }}
    where
        ds = {{ ds() }}
        and is_case_matched
),

case_with_prev_end as (
    select
        *,
        lag(actual_end_datetime_local)
            over (partition by room_id, surgery_date order by actual_start_datetime_local asc)
            as prev_actual_end
    from cases_with_date
),

case_with_prev_end_and_tot as (
    select
        c.*,
        -- expected_start_time: time wheels-in should happen if case was starting ASAP
        -- next line computes the expected_start_time, which is the latest of 2 timestamp
        -- 1. The scheduled time
        -- 2. wheels-out of prev case + turnover time
        greatest(
            datetime_add(c.prev_actual_end, interval t.median_minutes_28_days minute), c.scheduled_start_datetime_local
        ) as expected_start_time
    from case_with_prev_end as c
    inner join {{ ref("turnover_stats_by_service_line") }} as t
        on
            c.site_id = t.site_id
            and c.service_line_id = t.service_line_id
    where t.ds = {{ ds() }}
)

select
    {{ ds() }} as ds,
    apella_case_id,
    case
        when
            expected_start_time < actual_start_datetime_local
            then datetime_diff(actual_start_datetime_local, expected_start_time, minute)
        else 0
    end as unexpected_delay_minutes
from case_with_prev_end_and_tot
