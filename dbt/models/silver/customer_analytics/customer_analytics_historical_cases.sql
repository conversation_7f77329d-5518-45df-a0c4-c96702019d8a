{{ daily_config() }}

-- set up historical cases
with historical_case_error_filtering as ( --filter out known erroneous data from historical cases normalized
    select
        hc.file_case_id,
        case
            when hc.org_id = 'health_first' then false --health first data is backfilled directly into core
            -- TODO: once health_first backfill data is removed from core, adjust this filter
            -- the following filters are copied directly from the forecasting pipeline
            -- TODO: understand the forecasting filters and whether or not they are necessary for the core pipeline
            when
                hc.source_file_gcs_path not in (
                    'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMH_WALTER_20240308.csv', -- noqa: LT05
                    'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMH_OPC_18_20240308.csv' -- noqa: LT05
                )
                then true
            when
                (
                    hc.source_file_gcs_path
                    = 'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMH_OPC_18_20240308.csv' -- noqa: LT05
                    and date(hc.scheduled_start_datetime_local) < '2023-12-22'
                    and hc.customer_room_name not in ('OPC18OR12', 'OPC18OR14')
                )
                then true
            when
                (
                    hc.source_file_gcs_path
                    = 'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMH_OPC_18_20240308.csv' -- noqa: LT05
                    and date(hc.scheduled_start_datetime_local) <= '2024-02-01'
                    and hc.customer_room_name in ('OPC18OR12')
                )
                then true
            when
                (
                    hc.source_file_gcs_path
                    = 'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMH_WALTER_20240308.csv' -- noqa: LT05
                    and date(hc.scheduled_start_datetime_local) < '2022-09-16'
                    and hc.customer_room_name not in ('WT3OR 05', 'WT3OR 08')
                )
                then true
            when
                (
                    hc.source_file_gcs_path
                    = 'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMH_WALTER_20240308.csv' -- noqa: LT05
                    and date(hc.scheduled_start_datetime_local) < '2023-01-09'
                    and hc.customer_room_name in ('WT3OR 05')
                )
                then true
            else false
        end as is_valid_historical_data
        -- TODO: the forecasting pipeline also has additional filtering for valid historical data based on start dates
    -- TODO: (cont.) for each site; see historical_case_features.sql and forecasting_sites_config.sql
    -- TODO: (cont.) ideal solution is to create a site configuration seed table and then join it here for filtering
    from {{ ref("historical_cases_normalized") }} as hc
    where hc.ds = {{ ds() }}
    -- TODO: understand if the below filters are needed, these are copied from forecasting_historical_cases.sql
    and hc.cancellation_reason is null
    and hc.primary_surgeon is not null
    and hc.apella_room_id is not null
    -- TODO: understand if the below filter is needed,
    --  TODO: (cont.) this replicates filtering done in forecasting_historical_file_filter.sql macro
    and source_file_gcs_path in (
        'prod-data-warehouse/historical_cases/health_first/health_first_historical_cases_2023-03-01.csv',
        'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMH_DUNN_20240308.csv',
        'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMH_DUNN_6_20240308.csv',
        'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMH_LD_20240308.csv',
        'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMH_Main_OR_20240308.csv',
        'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMH_OPC_18_20240308.csv',
        'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMH_WALTER_20240308.csv',
        'prod-data-warehouse/historical_cases/houston_methodist/houston_methodist_historical_cases_2022-09-01.csv',
        'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMCL_OR_20240308.csv',
        'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMCL_LD_20240308.csv',
        'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMCL_ASC_OR_20240308.csv',
        'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMSJ_ASU_OR_20240308.csv',
        'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMSJ_OR_20240308.csv',
        'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMSJ_LD_20240308.csv',
        'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMSL_LD_20240308.csv',
        'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMSL_MAIN_20240308.csv',
        'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMW_OR_20240308.csv',
        'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMW_LD_20240308.csv',
        'prod-data-warehouse/historical_cases/nyu/nyu_data_ingest_20111101-20240404.csv',
        'prod-data-warehouse/historical_cases/nyu/nyu_data_ingest_20240405-20241029.csv',
        'prod-data-warehouse/historical_cases/tampa_general/TGH_Historical_Surgical_Data_v3.txt'
    )
),

historical_features as ( --historical cases, cleaning fields for consistency with core, adding relevant features
    select
        hc.org_id,
        cast(null as string) as case_id, --historical cases do not have an Apella case id
        cast(null as string) as case_phase_id, --historical cases do not have an Apella case phase id
        hc.file_case_id as external_case_id,
        hc.scheduled_start_datetime_local,
        hc.scheduled_end_datetime_local,
        hc.actual_start_datetime_local,
        hc.actual_end_datetime_local,
        --TODO: normalize this so historical and Apella case classification is consistent
        hc.case_classification_types_id as case_classification_id,
        --TODO: normalize this and extract the case classification name from the id
        cast(null as string) as case_classification_name,
        --TODO: normalize this so historical and Apella patient classification is consistent
        hc.patient_class as patient_classification_id,
        --TODO: normalize this and extract the patient classification name from the id
        cast(null as string) as patient_classification_name,
        hc.is_add_on,
        hc.primary_procedure_name,
        hc.procedure_count,
        hc.source_file_gcs_path as data_source,
        hc.apella_room_id as room_id,
        hc.customer_room_name as room_name, --TODO: normalize and create apella_room_name in historical_cases_normalized
        hc.customer_site_name as site_name,
        sl.id as service_line_id,
        case
            when hc.customer_site_name = 'HMH OPC 19 OR' then 'HMH-OPC19' --TODO: push this cleaning logic upstream
            else hc.apella_site_id
        end as site_id, --TODO: normalize and create apella_site_name in historical_cases_normalized
        coalesce(sl.name, hc.service_line) as service_line_name,
        upper(primary_surgeon.last_name || ', ' || primary_surgeon.first_name)
            as primary_surgeon,
        regexp_extract(hc.full_procedure_name, r'\b\d{5}\b')
            as primary_procedure_cpt --unnest structure
    from {{ ref("historical_cases_normalized") }} as hc
    left outer join {{ api_table_snapshot("bronze", "public_service_lines", "sl") }} -- add clean service line mapping
        on case
            when hc.service_line in ('ortho') then 'Orthopedics'
            else hc.service_line
        end = sl.name
        and hc.org_id = sl.org_id
    where hc.ds = {{ ds() }}
),



clean_historical_cases as ( --select only valid historical data
    select historical_features.*
    from historical_features
    inner join historical_case_error_filtering
        on historical_features.external_case_id = historical_case_error_filtering.file_case_id
    where historical_case_error_filtering.is_valid_historical_data
)

select
    {{ ds() }} as ds,
    site_id || ': ' || external_case_id as primary_case_id, --primary key for this table
    *
from clean_historical_cases
