{{ daily_config() }}

-- set up historical cases
with historical_cases as (
    select
        org_id,
        case_id,
        case_phase_id,
        external_case_id,
        scheduled_start_datetime_local,
        scheduled_end_datetime_local,
        actual_start_datetime_local,
        actual_end_datetime_local,
        case_classification_id,
        case_classification_name,
        patient_classification_id,
        patient_classification_name,
        is_add_on,
        primary_procedure_name,
        procedure_count,
        data_source,
        room_id,
        room_name,
        site_id,
        site_name,
        service_line_id,
        service_line_name,
        primary_surgeon,
        primary_procedure_cpt
    from {{ ref("customer_analytics_historical_cases") }}
    where ds = {{ ds() }}
),

apella_cases as (
    select
        org_id,
        case_id,
        case_phase_id,
        external_case_id,
        scheduled_start_datetime_local,
        scheduled_end_datetime_local,
        actual_start_datetime_local,
        actual_end_datetime_local,
        case_classification_id,
        case_classification_name,
        patient_classification_id,
        patient_classification_name,
        is_add_on,
        primary_procedure_name,
        procedure_count,
        data_source,
        room_id,
        room_name,
        site_id,
        site_name,
        service_line_id,
        service_line_name,
        primary_surgeon,
        primary_procedure_cpt
    from {{ ref("customer_analytics_cases") }}
    where
        ds = {{ ds() }}
),

combined_cases as ( --combine historical and API data
    select *
    from (
        select *
        from historical_cases

        union all

        select *
        from apella_cases
    )
    -- the below logic is needed because NYU historical cases start with 'Log ' but their Apella cases do not which
    --   causes duplication. Ideally this should be fixed upstream in historical case normalization, but this is a
    --   temporary patch so that we do not break any forecasting pipelines but enable analytics.
    qualify row_number() over (
        partition by
            org_id,
            case
                when
                    org_id = 'nyu'
                    then coalesce(split(external_case_id, ' ')[safe_offset(1)], external_case_id, case_phase_id)
                else coalesce(external_case_id, case_phase_id)
            end
        order by data_source
    ) = 1
)

select distinct
    {{ ds() }} as ds,
    case
        when case_id is not null then 'case_id: ' || case_id
        when external_case_id is not null then site_id || ': ' || external_case_id
        else 'case_phase_id: ' || case_phase_id
    end as primary_case_id, --primary key for this table
    *
from combined_cases
