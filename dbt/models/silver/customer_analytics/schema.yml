version: 1

models:
  - name: customer_analytics_cases
    description: "Table including Apella cases, sourced from `core_cases`. This should have the same row count as
    `core_cases`, but includes added helpful features such as primary_surgeon etc. Plan on adding additional features
    such as is_flip_room, first_case, last_case, etc.
    NOTE: this is limited only to cases that actually happened (i.e. excludes canceled cases)"
    columns:
      - name: ds
        data_type: "timestamp"
      - name: primary_case_id
        data_type: "string"
        description: "synthetic primary key for this table. Logic is as follows:
        case when case_id is not null then 'case_id: ' || case_id
        when external_case_id is not null then site_id || ': ' || external_case_id
        else 'case_phase_id: ' || case_phase_id end as primary_case_id"
        tests:
          - not_null
      - name: org_id
        data_type: "string"
      - name: case_id
        data_type: "string"
      - name: case_phase_id
        data_type: "string"
      - name: external_case_id
        data_type: "string"
      - name: scheduled_start_datetime_local
        data_type: "timestamp"
      - name: scheduled_end_datetime_local
        data_type: "timestamp"
      - name: actual_start_datetime_local
        data_type: "timestamp"
      - name: actual_end_datetime_local
        data_type: "timestamp"
      - name: case_classification_id
        data_type: "string"
      - name: case_classification_name
        data_type: "string"
      - name: patient_classification_id
        data_type: "string"
      - name: patient_classification_name
        data_type: "string"
      - name: is_add_on
        data_type: "boolean"
      - name: primary_procedure_name
        data_type: "string"
      - name: procedure_count
        data_type: "int"
      - name: data_source
        data_type: "string"
        description: "source file name for historical data, 'apella_data' for API data"
      - name: room_id
        data_type: "string"
      - name: room_name
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: site_name
        data_type: "string"
      - name: service_line_id
        data_type: "string"
      - name: service_line_name
        data_type: "string"
      - name: primary_surgeon
        data_type: "string"
      - name: primary_procedure_cpt
        data_type: "string"
  - name: customer_analytics_historical_cases
    description: "Table with clean and normalized historical cases sourced from `historical_cases`. Duplicates
    forecasting error filtering logic."
    columns:
      - name: ds
        data_type: "timestamp"
      - name: primary_case_id
        data_type: "string"
        description: "synthetic primary key for this table. Logic is as follows:
        case when case_id is not null then 'case_id: ' || case_id
        when external_case_id is not null then site_id || ': ' || external_case_id
        else 'case_phase_id: ' || case_phase_id end as primary_case_id"
        tests:
          - not_null
      - name: org_id
        data_type: "string"
      - name: case_id
        data_type: "string"
      - name: case_phase_id
        data_type: "string"
      - name: external_case_id
        data_type: "string"
      - name: scheduled_start_datetime_local
        data_type: "timestamp"
      - name: scheduled_end_datetime_local
        data_type: "timestamp"
      - name: actual_start_datetime_local
        data_type: "timestamp"
      - name: actual_end_datetime_local
        data_type: "timestamp"
      - name: case_classification_id
        data_type: "string"
      - name: case_classification_name
        data_type: "string"
      - name: patient_classification_id
        data_type: "string"
      - name: patient_classification_name
        data_type: "string"
      - name: is_add_on
        data_type: "boolean"
      - name: primary_procedure_name
        data_type: "string"
      - name: procedure_count
        data_type: "int"
      - name: data_source
        data_type: "string"
        description: "source file name for historical data, 'apella_data' for API data"
      - name: room_id
        data_type: "string"
      - name: room_name
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: site_name
        data_type: "string"
      - name: service_line_id
        data_type: "string"
      - name: service_line_name
        data_type: "string"
      - name: primary_surgeon
        data_type: "string"
      - name: primary_procedure_cpt
        data_type: "string"
  - name: customer_analytics_turnovers
    description: "This table looks at all turnover phases (including open and clean) and adds helping features for easy 
      analysis (e.g. is_flip_room, prev_primary_surgeon_name, etc.). This excludes turnovers that are between the last 
      case of one day and the first case of the previous day. To filter turnovers to same surgeon same room, set 
      prev_primary_surgeon_name = next_primary_surgeon_name."
    columns:
      - name: ds
        data_type: "timestamp"
      - name: phase_id
        data_type: "string"
      - name: org_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: phase_type_id
        data_type: "string"
      - name: start_event_id
        data_type: "string"
      - name: end_event_id
        data_type: "string"
      - name: phase_start_datetime_local
        data_type: "timestamp"
      - name: phase_end_datetime_local
        data_type: "timestamp"
      - name: phase_date_local
        data_type: "date"
      - name: phase_duration_minutes
        data_type: "int"
      - name: prev_case_id
        data_type: "string"
      - name: next_case_id
        data_type: "string"
      - name: prev_scheduled_start_datetime_local
        data_type: "timestamp"
      - name: prev_scheduled_end_datetime_local
        data_type: "timestamp"
      - name: prev_actual_start_datetime_local
        data_type: "timestamp"
      - name: prev_actual_end_datetime_local
        data_type: "timestamp"
      - name: prev_is_add_on
        data_type: "boolean"
      - name: prev_case_classification_name
        data_type: "string"
      - name: next_scheduled_start_datetime_local
        data_type: "timestamp"
      - name: next_scheduled_end_datetime_local
        data_type: "timestamp"
      - name: next_actual_start_datetime_local
        data_type: "timestamp"
      - name: next_actual_end_datetime_local
        data_type: "timestamp"
      - name: next_is_add_on
        data_type: "boolean"
      - name: next_case_classification_name
        data_type: "string"
      - name: prev_is_last_case
        data_type: "boolean"
      - name: prev_is_flip_room
        data_type: "boolean"
      - name: next_is_flip_room
        data_type: "boolean"
      - name: prev_service_line_name
        data_type: "string"
      - name: next_service_line_name
        data_type: "string"
      - name: general_time_buckets
        data_type: "string"
      - name: general_time_order
        data_type: "int"
      - name: hospital_shift_buckets
        data_type: "string"
      - name: hospital_shift_order
        data_type: "int"
      - name: scheduled_case_gap_minutes
        data_type: "int"
      - name: prev_end_early_minutes
        data_type: "int"
        description: "the difference between scheduled end and actual end. Positive difference means that the case 
        ended early"
      - name: next_start_early_minutes
        data_type: "int"
        description: "the difference between scheduled start and actual start. Positive difference means that the case
        started early"
      - name: prev_primary_surgeon_name
        data_type: "string"
      - name: next_primary_surgeon_name
        data_type: "string"
  - name: customer_analytics_cases_combined
    description: "Table combined customer_analytics_historical_cases and customer_analytics_cases (historical and Apella
    data). Included deduplication logic to remove overlapping cases between historical and analytics cases, preferring
    Apella data. The historical data usually includes useful metadata (e.g. service_line_name) that is not in the 
    Apella data for these cases. A future to do is to coalesce these metadata.This is still an active WIP, since many 
    of these fields still need further cleaning and normalization. Additionally, we will incrementally add relevant 
    features e.g. is_flip_room, is_first_case to this table until it can replace forecasting_case_features_combined in 
    functionality."
    columns:
    - name: ds
      data_type: "timestamp"
    - name: primary_case_id
      data_type: "string"
      description: "synthetic primary key for this table. Logic is as follows:
      case when case_id is not null then 'case_id: ' || case_id
      when external_case_id is not null then site_id || ': ' || external_case_id
      else 'case_phase_id: ' || case_phase_id end as primary_case_id"
      tests:
        - not_null
    - name: org_id
      data_type: "string"
    - name: case_id
      data_type: "string"
    - name: case_phase_id
      data_type: "string"
    - name: external_case_id
      data_type: "string"
    - name: scheduled_start_datetime_local
      data_type: "timestamp"
    - name: scheduled_end_datetime_local
      data_type: "timestamp"
    - name: actual_start_datetime_local
      data_type: "timestamp"
    - name: actual_end_datetime_local
      data_type: "timestamp"
    - name: case_classification_id
      data_type: "string"
    - name: case_classification_name
      data_type: "string"
    - name: patient_classification_id
      data_type: "string"
    - name: patient_classification_name
      data_type: "string"
    - name: is_add_on
      data_type: "boolean"
    - name: primary_procedure_name
      data_type: "string"
    - name: procedure_count
      data_type: "int"
    - name: data_source
      data_type: "string"
      description: "source file name for historical data, 'apella_data' for API data"
    - name: room_id
      data_type: "string"
    - name: room_name
      data_type: "string"
    - name: site_id
      data_type: "string"
    - name: site_name
      data_type: "string"
    - name: service_line_id
      data_type: "string"
    - name: service_line_name
      data_type: "string"
    - name: primary_surgeon
      data_type: "string"
    - name: primary_procedure_cpt
      data_type: "string"
  - name: customer_job_title_harmonization
    description: "Maps individual/unique job titles across all CMS-managed Google Sheets to a standardized set of job titles. 
    Additionally categorizes the standardized job titles into user group types."
    columns:
    - name: ds
      data_type: "timestamp"
    - name: apella_org_id
      data_type: "string"
      description: "Apella Org ID based on the Google Sheet ID from the original google sheet ingestion table"
    - name: user_email
      data_type: "string"
      description: "The email of the user as-entered in the CSM-managed Google Sheets. There may be typos, and the email may not match those in other tables"
    - name: "original_job_title"
      data_type: "string"
      description: "The job title of the user as originall entered in the CSM-managed Google Sheets."
    - name: "standardized_job_title"
      data_type: "string"
      description: "The mapped job title of the user based on string-based logic rules to categorize the original job titles into a set of standardized job titles"
    - name: "health_score_user_group"
      data_type: "string"
      description: "A high level categorization of the standardized job titles into user group types"


