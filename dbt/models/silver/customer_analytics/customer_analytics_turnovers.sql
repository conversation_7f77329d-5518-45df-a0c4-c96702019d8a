{{ daily_config() }}

with last_case_of_the_day as ( --identify the last case of the day so the overnight is not included as a turnover
    select distinct
        apella_case_id as case_id,
        true as is_last_case
    from {{ ref("core_cases") }}
    where ds = {{ ds() }}
    qualify row_number() over (
        partition by room_id, greatest(date(scheduled_start_datetime_local), date(actual_start_datetime_local))
        order by greatest(scheduled_start_datetime_local, actual_start_datetime_local) desc
    ) = 1
),

case_gaps as ( --get all case gaps with no exclusions
    select distinct
        phase_id,
        org_id,
        room_id,
        site_id,
        phase_type_id,
        start_event_id,
        end_event_id,
        phase_start_datetime_local,
        phase_end_datetime_local,
        phase_date_local,
        phase_duration_minutes,
        prev_apella_case_id as prev_case_id,
        next_apella_case_id as next_case_id
    from {{ ref("core_turnover_phases") }}
    where ds = {{ ds() }}
),

case_features as ( --add helpful case features for analytics
    select distinct
        cg.*,
        pc.scheduled_start_datetime_local as prev_scheduled_start_datetime_local,
        pc.scheduled_end_datetime_local as prev_scheduled_end_datetime_local,
        pc.actual_start_datetime_local as prev_actual_start_datetime_local,
        pc.actual_end_datetime_local as prev_actual_end_datetime_local,
        pc.is_add_on as prev_is_add_on,
        pc.case_classification_name as prev_case_classification_name,
        nc.scheduled_start_datetime_local as next_scheduled_start_datetime_local,
        nc.scheduled_end_datetime_local as next_scheduled_end_datetime_local,
        nc.actual_start_datetime_local as next_actual_start_datetime_local,
        nc.actual_end_datetime_local as next_actual_end_datetime_local,
        nc.is_add_on as next_is_add_on,
        nc.case_classification_name as next_case_classification_name,
        pc.service_line_name as prev_service_line_name,
        nc.service_line_name as next_service_line_name,
        coalesce(lc.is_last_case, false) as prev_is_last_case,
        prev_sgn.first_primary_surgeon.last_name
        || ', '
        || prev_sgn.first_primary_surgeon.first_name as prev_primary_surgeon_name,
        next_sgn.first_primary_surgeon.last_name
        || ', '
        || next_sgn.first_primary_surgeon.first_name as next_primary_surgeon_name,
        coalesce(prev_flp.is_flip_room, false) as prev_is_flip_room,
        coalesce(next_flp.is_flip_room, false) as next_is_flip_room,
        case
            when
                cast('00:00:00' as time) <= cast(cg.phase_start_datetime_local as time)
                and cast('05:59:59' as time) >= cast(cg.phase_start_datetime_local as time)
                then 'early_morning'
            when
                cast('06:00:00' as time) <= cast(cg.phase_start_datetime_local as time)
                and cast('11:59:59' as time) >= cast(cg.phase_start_datetime_local as time)
                then 'morning'
            when
                cast('12:00:00' as time) <= cast(cg.phase_start_datetime_local as time)
                and cast('17:59:59' as time) >= cast(cg.phase_start_datetime_local as time)
                then 'afternoon'
            when
                cast('18:00:00' as time) <= cast(cg.phase_start_datetime_local as time)
                and cast('23:59:59' as time) >= cast(cg.phase_start_datetime_local as time)
                then 'evening'
        end as general_time_buckets,
        case
            when
                cast('00:00:00' as time) <= cast(cg.phase_start_datetime_local as time)
                and cast('05:59:59' as time) >= cast(cg.phase_start_datetime_local as time)
                then 0
            when
                cast('06:00:00' as time) <= cast(cg.phase_start_datetime_local as time)
                and cast('11:59:59' as time) >= cast(cg.phase_start_datetime_local as time)
                then 1
            when
                cast('12:00:00' as time) <= cast(cg.phase_start_datetime_local as time)
                and cast('17:59:59' as time) >= cast(cg.phase_start_datetime_local as time)
                then 2
            when
                cast('18:00:00' as time) <= cast(cg.phase_start_datetime_local as time)
                and cast('23:59:59' as time) >= cast(cg.phase_start_datetime_local as time)
                then 3
        end as general_time_order,
        case
            when
                cast('19:00:00' as time) <= cast(cg.phase_start_datetime_local as time)
                or cast('06:59:59' as time) >= cast(cg.phase_start_datetime_local as time)
                then 'overnight_shift'
            when
                cast('07:00:00' as time) <= cast(cg.phase_start_datetime_local as time)
                and cast('14:59:59' as time) >= cast(cg.phase_start_datetime_local as time)
                then '3pm_shift'
            when
                cast('15:00:00' as time) <= cast(cg.phase_start_datetime_local as time)
                and cast('16:59:59' as time) >= cast(cg.phase_start_datetime_local as time)
                then '5pm_shift'
            when
                cast('17:00:00' as time) <= cast(cg.phase_start_datetime_local as time)
                and cast('18:59:59' as time) >= cast(cg.phase_start_datetime_local as time)
                then '7pm_shift'
        end as hospital_shift_buckets,
        case
            when
                cast('19:00:00' as time) <= cast(cg.phase_start_datetime_local as time)
                or cast('06:59:59' as time) >= cast(cg.phase_start_datetime_local as time)
                then 0
            when
                cast('07:00:00' as time) <= cast(cg.phase_start_datetime_local as time)
                and cast('14:59:59' as time) >= cast(cg.phase_start_datetime_local as time)
                then 1
            when
                cast('15:00:00' as time) <= cast(cg.phase_start_datetime_local as time)
                and cast('16:59:59' as time) >= cast(cg.phase_start_datetime_local as time)
                then 2
            when
                cast('17:00:00' as time) <= cast(cg.phase_start_datetime_local as time)
                and cast('18:59:59' as time) >= cast(cg.phase_start_datetime_local as time)
                then 3
        end as hospital_shift_order,
        date_diff(nc.scheduled_start_datetime_local, pc.scheduled_end_datetime_local, minute)
            as scheduled_case_gap_minutes,
        date_diff(pc.scheduled_end_datetime_local, pc.actual_end_datetime_local, minute) as prev_end_early_minutes,
        date_diff(nc.scheduled_start_datetime_local, nc.actual_start_datetime_local, minute) as next_start_early_minutes
    from case_gaps as cg
    left outer join last_case_of_the_day as lc
        on
            cg.prev_case_id = lc.case_id
    left outer join {{ ref("core_cases") }} as pc
        on
            cg.prev_case_id = pc.apella_case_id
            and pc.ds = {{ ds() }}
    left outer join {{ ref("core_cases") }} as nc
        on
            cg.next_case_id = nc.apella_case_id
            and nc.ds = {{ ds() }}
    left outer join {{ ref("surgeon_features") }} as prev_sgn
        on
            cg.prev_case_id = prev_sgn.case_id
            and prev_sgn.ds = {{ ds() }}
    left outer join {{ ref("surgeon_features") }} as next_sgn
        on
            cg.next_case_id = next_sgn.case_id
            and next_sgn.ds = {{ ds() }}
    left outer join {{ ref("flip_room_case_lookup") }} as prev_flp
        on
            cg.prev_case_id = prev_flp.case_id
            and prev_flp.ds = {{ ds() }}
    left outer join {{ ref("flip_room_case_lookup") }} as next_flp
        on
            cg.next_case_id = next_flp.case_id
            and next_flp.ds = {{ ds() }}
)

select
    {{ ds() }} as ds,
    *
from case_features
