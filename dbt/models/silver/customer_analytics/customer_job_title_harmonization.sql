{{ daily_config() }}

with source_sheet_info as (
    select
        source_google_sheet_id,
        case
            when source_google_sheet_id = '13gQKI2pQ3EVLe_BYBsqt85adsOG2blbjQgauFHSU6ZA' then 'tampa_general'
            when source_google_sheet_id = '1Dcx1c3WaK8h8B7U_U5TUZzl--sqCvXz4H4U0xRUudFs' then 'houston_methodist'
            when source_google_sheet_id = '1K0BGioa_i_pdEjxCO9B2mCAUWZ6n2nQFCzEudwnU2yM' then 'lifebridge'
            when source_google_sheet_id = '1Xg7e3zUGdBpO7f-kQDQMLlo4Sylb5DQpdZuJPO9NeTU' then 'health_first'
            when source_google_sheet_id = '********************************************' then 'nyu'
            when source_google_sheet_id = '1ydn8oB5KsTJYUWF7boQd-bCCKUBY0YPOnuCLE62c0JI' then 'north_bay'
        end as apella_org_id,
        lower(email) as user_email,
        max(job_title) as job_title
    from {{ source("bronze", "cs_google_sheets_users_to_org_titles") }}
    where
        ds = {{ ds() }}
        and email != ''
    group by 1, 2, 3
),

mapped as (
    select
        apella_org_id,
        user_email,
        job_title as original_job_title,
        case
            when
                lower(job_title) like any('%evs%', '%pca%', '%patient care%', '%hospitality%', '%patient access%')
                then 'EVS & PCA'
            when lower(job_title) like any('%infection%') then 'Infection Prevention'
            when lower(job_title) like any('%educat%', '%learn%', '%practice lead%') then 'Educator'
            when lower(job_title) like any('%surgeon%', 'physician') then 'Surgeon'
            when lower(job_title) like any('%anesthes%', '%pain%') then 'Anesthesiologist'
            when job_title like '%IT%' then 'IT / Implementation Staff'
            when
                lower(job_title) like any('%cyber%', '%network%', '%access management%')
                then 'IT / Implementation Staff'
            when
                job_title like any('%Chief%', '%CNO%', '%COO%', '%VP%', '%Chair%', '%CQO%', 'Bellaire Leader')
                then 'Executive'
            when lower(job_title) like any('%quality%', 'svp', 'coo') then 'Executive'
            when job_title = 'Director, Business Operations Surgery Administration' then 'OR Director'
            when lower(
                job_title) like any(
                '%analyst%',
                '%analytics%',
                '%research%',
                '%secretary%',
                '%admin%',
                '%engineer%',
                '%tour%',
                '%informatic%',
                '%command%',
                '%finance%',
                '%hmpo%',
                '%optim%',
                '%project manager%',
                '%pms%project%',
                '%business manager%',
                '%prgrm manager%'
            ) then 'Business Staff - Analyst, Researcher'
            when lower(job_title) like '%icu%' then 'ICU'
            when lower(job_title) like any('%ob%', '%childbirth%', '%cbc%', '%delivery%') then 'OB'
            when lower(job_title) like any('%imag%', '%cath%', '%endo%', '%heart%') then 'Non-Traditional OR'
            when lower(job_title) like any('%schedul%', '%booking%') then 'OR or Clinic Scheduler'
            when lower(job_title) like '%pre/post%' then 'Preop & Postop Nurse'
            when lower(job_title) like any('%preop%', '%aod%') then 'Preop/AOD Nurse'
            when lower(job_title) like any('%postop%', '%pacu%') then 'Postop/PACU Nurse'
            when lower(job_title) like '%charge%' then 'Charge Nurse'
            when
                lower(job_title) like any(
                    '%staff%',
                    '%surgical tech%',
                    '%resource%',
                    '%or rn%',
                    '%rn%',
                    '%clinician%',
                    '%or nurse%',
                    'or assistant'
                )
                then 'OR Staff'
            when lower(job_title) like '%director%' then 'OR Director'
            when lower(job_title) like any('%nurse manager%', '%nm, %') then 'Nurse Manager'
            when
                lower(job_title) like any('% manager%', '%or professional practice%', '%apellamanagement%')
                then 'OR Manager'
            when
                lower(job_title) like any('%physical%', '%special%', '%endo%', '%perfusion%', '%imag%', '%pharma%')
                then 'Specialties - PT, Pharma'
            when
                lower(job_title) like any('%monitor%', '%auto%', '%shared%', '%test%')
                then 'Monitors / Non-user / Shared Logins'
            else 'Need CSM Review'
        end as standardized_job_title
    from source_sheet_info
    order by 1, 2, 3, 4
)

select
    {{ ds() }} as ds,
    *,
    case
        when
            standardized_job_title in ('Charge Nurse', 'OR Manager', 'OR Director', 'Nurse Manager')
            then '1. Core User'
        when
            standardized_job_title in (
                'Anesthesiologist',
                'Surgeon',
                'OR Staff',
                'Preop & Postop Nurse',
                'Postop/PACU Nurse',
                'Preop/AOD Nurse',
                'EVS & PCA',
                'Educator',
                'Infection Prevention'
            )
            then '2. Full OR Users'
        when
            standardized_job_title in (
                'Executive',
                'Business Staff - Analyst, Researcher',
                'ICU',
                'OB',
                'Specialties - PT, Pharma',
                'OR or Clinic Scheduler',
                'Non-Traditional OR'
            )
            then '3. Expanded Hospital'
        else 'Other'
    end as health_score_user_group
from mapped
