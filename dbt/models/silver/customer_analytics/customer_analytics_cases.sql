{{ daily_config() }}

-- set up core cases
with core_cases as (
    select
        cc.org_id,
        cc.apella_case_id as case_id,
        cc.case_phase_id,
        cc.customer_case_id as external_case_id,
        cc.scheduled_start_datetime_local,
        cc.scheduled_end_datetime_local,
        cc.actual_start_datetime_local,
        cc.actual_end_datetime_local,
        cc.case_classification_id, --TODO: normalize this so historical and Apella case classification is consistent
        cc.case_classification_name,
        --TODO: normalize this so historical and Apella patient classification is consistent
        cc.patient_classification_id,
        cc.patient_classification_name,
        cc.is_add_on,
        prx.first_primary_procedure as primary_procedure_name,
        'apella_data' as data_source,
        --TODO: parse out which data was backfilled directly and is actually historical. This should happen upstream
        cc.actual_room_id as room_id,
        cc.actual_room_name as room_name,
        cc.actual_site_name as site_name,
        cc.service_line_id,
        cc.actual_site_id as site_id,
        cc.service_line_name,
        cast(null as string) as primary_procedure_cpt, --TODO: need to get this from bronze data
        array_length(prx.procedures_list) as procedure_count,
        sgn.first_primary_surgeon.last_name
        || ', '
        || sgn.first_primary_surgeon.first_name as primary_surgeon
        -- note that many cases have more than one surgeon and procedure. The forecasting one chooses a surgeon and
        --  procedure based on which one was created first in the EHR, but we don't have an actual surgeon to procedure
        --  mapping. See EHR-651 and EHR-452 for more context.
        --  For this table, it may be more useful to have the full list of surgeons and procedure rather than choosing
        --  one, so these fields may turn into structs rather than strings.
    from {{ ref("core_cases") }} as cc
    left outer join {{ ref("surgeon_features") }} as sgn
        on
            cc.apella_case_id = sgn.case_id
            and sgn.ds = {{ ds() }}
    left outer join {{ ref("procedure_features") }} as prx
        on
            cc.apella_case_id = prx.case_id
            and prx.procedures_list is not null
            and prx.ds = {{ ds() }}
    where
        cc.ds = {{ ds() }}
        and cc.actual_start_datetime_local is not null --limit only to cases that actually happened
)

select
    {{ ds() }} as ds,
    case
        when case_id is not null then 'case_id: ' || case_id
        when external_case_id is not null then site_id || ': ' || external_case_id
        else 'case_phase_id: ' || case_phase_id
    end as primary_case_id, --primary key for this table
    *
from core_cases
