{{ daily_config(produce_latest_snapshot=True) }}

select
    {{ ds() }} as ds,
    a.org_id,
    a.site_id,
    count(distinct a.case_id) as case_count,
    count(distinct a.external_case_id) as external_case_count
from {{ api_table_snapshot("bronze", "public_cases", alias="a") }}
inner join {{ api_table_snapshot("bronze", "public_case_staff", alias="b") }}
    on a.case_id = b.case_id
group by 1, 2, 3
