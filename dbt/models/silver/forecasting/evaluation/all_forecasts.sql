{{ daily_config() }}

with raw_case_forecasting_forecasts as (
    select
        forecasts.case_id,
        forecasts.room_id,
        forecasts.site_id,
        forecasts.end_time_model,
        forecasts.start_time_model,
        -- in this table, we did not have forecast variants yet so everything will
        -- be set as the "stable" variant (defined as the default shown to customers)
        'stable' as forecast_variant_backfilled,
        forecasts.forecast_start_time,
        forecasts.forecast_end_time,
        forecasts.forecast_duration_in_seconds,
        forecasts.forecast_run_time,
        forecasts.start_time_model_version as case_start_offset_model_version,
        forecasts.end_time_model_version as case_duration_model_version,
        'case_forecasting.forecasts' as raw_forecast_source,
        public_sites.timezone
    from {{ api_table_snapshot("case_forecasting", "forecasts", "forecasts") }}
    inner join {{ api_table_snapshot("bronze", "public_sites", "public_sites") }}
        on forecasts.site_id = public_sites.id
    -- starting in March 2024, the case_forecasting.forecasts table no longer recorded a new
    -- row with each forecast time update
    -- we do not have reliable data coming in from case_forecasting.case_forecast_info before July 2024
    -- so the period of March - July 2024 will contain faulty data
    where date(forecasts.forecast_run_time) < '2024-07-11'
),

public_rooms as (
    select
        public_sites.org_id,
        public_sites.id as site_id,
        public_sites.timezone,
        public_rooms.id as room_id
    from {{ api_table_snapshot("bronze", "public_sites", "public_sites") }}
    inner join {{ api_table_snapshot("bronze", "public_rooms", "public_rooms") }}
        on public_sites.id = public_rooms.site_id
),

raw_case_forecast_info as (
    select
        case_id,
        room_id,
        end_time_model,
        start_time_model,
        forecast_variant_backfilled,
        forecast_start_time,
        forecast_end_time,
        forecast_run_time,
        case_start_offset_model_version,
        case_duration_model_version,
        datetime_diff(
            forecast_end_time,
            forecast_start_time,
            second
        ) as forecast_duration_in_seconds,
        case when
            lag(forecast_start_time) over (partition by case_id, forecast_variant_backfilled order by forecast_run_time)
            = forecast_start_time
            then 0
        when
            lag(forecast_start_time) over (partition by case_id, forecast_variant_backfilled order by forecast_run_time)
            != forecast_start_time
            then 1
        when
            lag(forecast_start_time)
                over (partition by case_id, forecast_variant_backfilled order by forecast_run_time)
            is null
            then 0
        else 0 end as forecast_start_time_changed,
        case when
            lag(forecast_end_time) over (partition by case_id, forecast_variant_backfilled order by forecast_run_time)
            = forecast_end_time
            then 0
        when
            lag(forecast_end_time) over (partition by case_id, forecast_variant_backfilled order by forecast_run_time)
            != forecast_end_time
            then 1
        when
            lag(forecast_end_time)
                over (partition by case_id, forecast_variant_backfilled order by forecast_run_time)
            is null
            then 0
        else 0 end as forecast_end_time_changed,
        lag(forecast_start_time)
            over (partition by case_id, forecast_variant_backfilled order by forecast_run_time)
            as prev_forecast_start_time,
        row_number()
            over (partition by case_id, forecast_variant_backfilled order by forecast_run_time)
            as forecast_order
    from (
        select
            *,
            -- before we had forecast variants, all forecasts correspond to the 'stable' variant
            -- we do not expect the value of forecast_variant to be null after variants were turned on, so the
            -- only reason this would be null is because it was before variants were on
            coalesce(forecast_variant, 'stable') as forecast_variant_backfilled
        from {{ api_table_snapshot("case_forecasting", "case_forecast_info") }}
    )
    -- starting in July 2024, case_forecasting.case_forecast_info began to record forecasts
    -- coming from the forecasting service.
    where
        source = 'service'
        and date(forecast_run_time) >= '2024-07-11'
),

raw_case_forecast_info_changes_only as (
    select
        raw_case_forecast_info.case_id,
        raw_case_forecast_info.room_id,
        public_rooms.site_id,
        raw_case_forecast_info.end_time_model,
        raw_case_forecast_info.start_time_model,
        raw_case_forecast_info.forecast_variant_backfilled,
        raw_case_forecast_info.forecast_start_time,
        raw_case_forecast_info.forecast_end_time,
        raw_case_forecast_info.forecast_duration_in_seconds,
        raw_case_forecast_info.forecast_run_time,
        raw_case_forecast_info.case_start_offset_model_version,
        raw_case_forecast_info.case_duration_model_version,
        'case_forecasting.case_forecast_info' as raw_forecast_source,
        public_rooms.timezone
    from raw_case_forecast_info
    inner join public_rooms
        on raw_case_forecast_info.room_id = public_rooms.room_id
    -- the first forecast will not appear to have a change in forecasted start/end time but we still want to keep it
    where
        raw_case_forecast_info.forecast_order = 1
        or raw_case_forecast_info.forecast_start_time_changed = 1
        or raw_case_forecast_info.forecast_end_time_changed = 1
),

raw_forecasts as (
    select *
    from raw_case_forecasting_forecasts

    union all

    select *
    from raw_case_forecast_info_changes_only
),

forecasts as (
    select
        case_id,
        room_id,
        site_id,
        raw_forecast_source,
        forecast_variant_backfilled,
        datetime(forecast_start_time, timezone) as forecasted_start_local,
        datetime(forecast_end_time, timezone) as forecasted_end_local,
        date(datetime(forecast_start_time, timezone)) as forecast_start_date,
        date(datetime(forecast_run_time, timezone)) as forecast_run_date,
        extract(hour from datetime(forecast_run_time, timezone))
            as forecast_run_hour,
        datetime(forecast_run_time, timezone) as forecast_run_at_local,
        date_diff(
            datetime(forecast_start_time, timezone),
            datetime(forecast_run_time, timezone),
            day
        ) as days_forecast_run_to_forecast_start,
        date_diff(
            datetime(forecast_start_time, timezone),
            datetime(forecast_run_time, timezone),
            minute
        ) as minutes_forecast_run_to_forecast_start,
        date_diff(
            datetime(forecast_end_time, timezone),
            datetime(forecast_run_time, timezone),
            minute
        ) as minutes_forecast_run_to_forecast_end,
        round(forecast_duration_in_seconds / 60) as forecasted_duration
    from raw_forecasts
),

case_actuals as (
    select
        case_id,
        site_id,
        room_id,
        org_id,
        scheduled_start_datetime_local,
        scheduled_end_datetime_local,
        actual_start_datetime_local,
        actual_end_datetime_local,
        first_primary_procedure,
        first_primary_surgeon,
        scheduled_duration,
        actual_duration,
        add_on,
        cast(is_num_phases_match_num_cases as int) as num_phases_match_num_cases,
        cast(case_has_experienced_reordering as int) as case_has_experienced_reordering,
        first_case as first_case_by_scheduled_start,
        last_case as last_case_by_scheduled_start,
        case_type_short,
        date(scheduled_start_datetime_local) as case_date,
        format_date('%A', extract(date from scheduled_start_datetime_local)) as case_day_of_week,
        extract(dayofweek from scheduled_start_datetime_local) as case_day_of_week_num,
        format_date('%B', extract(date from scheduled_start_datetime_local)) as case_month
    from {{ ref('forecasting_case_features_combined') }}
    where
        ds = {{ ds() }}
        and apella_data = 1
),

raw_case_history as (
    select
        ch.version,
        ch.case_id,
        ch.room_id,
        ch.scheduled_start_time,
        ch.scheduled_end_time,
        ch.site_id,
        datetime(ch.updated_time, sites.timezone) as updated_time_local,
        datetime(ch.scheduled_start_time, sites.timezone) as scheduled_start_time_local,
        datetime(ch.scheduled_end_time, sites.timezone) as scheduled_end_time_local,
        date(datetime(ch.scheduled_start_time, sites.timezone)) as case_date
    from {{ api_table_snapshot("bronze", "public_cases_history", "ch") }}
    inner join {{ api_table_snapshot("bronze", "public_sites", "sites") }}
        on ch.site_id = sites.id
),

history as (
    select
        raw_case_history.updated_time_local,
        raw_case_history.version,
        raw_case_history.case_id,
        raw_case_history.scheduled_start_time_local as historical_scheduled_start_time_local,
        raw_case_history.scheduled_end_time_local as historical_scheduled_end_time_local,
        lead(raw_case_history.updated_time_local)
            over (partition by raw_case_history.case_id order by raw_case_history.version)
            as next_updated_time_local,
        lag(raw_case_history.scheduled_start_time_local)
            over (partition by raw_case_history.case_id order by raw_case_history.version)
            as previous_scheduled_start_time_local,
        datetime_diff(
            raw_case_history.scheduled_end_time_local,
            raw_case_history.scheduled_start_time_local, minute)
            as historical_scheduled_duration,
        lag(datetime_diff(
            raw_case_history.scheduled_end_time_local,
            raw_case_history.scheduled_start_time_local, minute
        ))
            over (partition by raw_case_history.case_id order by raw_case_history.version)
            as previous_scheduled_duration
    from raw_case_history


),

history_with_change_flags as (

    select
        case_id,
        updated_time_local,
        case
            when previous_scheduled_start_time_local != historical_scheduled_start_time_local then 1
            when
                previous_scheduled_start_time_local is null
                or previous_scheduled_start_time_local = historical_scheduled_start_time_local
                then 0
        end as is_scheduled_start_time_changed,
        case
            when previous_scheduled_duration != historical_scheduled_duration then 1
            when
                previous_scheduled_duration is null
                or previous_scheduled_duration = historical_scheduled_duration
                then 0
        end as is_scheduled_duration_changed
    from history
),

latest_changes_to_scheduled_start_time as (
    select
        case_id,
        max(updated_time_local) as latest_time_scheduled_start_time_changed
    from history_with_change_flags
    where is_scheduled_start_time_changed = 1
    group by case_id
),

latest_changes_to_scheduled_duration as (
    select
        case_id,
        max(updated_time_local) as latest_time_scheduled_duration_changed
    from history_with_change_flags
    where is_scheduled_duration_changed = 1
    group by case_id
),

metrics as (


    select
        case_actuals.case_id,
        case_actuals.site_id,
        case_actuals.room_id,
        case_actuals.org_id,
        case_actuals.scheduled_start_datetime_local,
        case_actuals.scheduled_end_datetime_local,
        case_actuals.actual_start_datetime_local,
        case_actuals.actual_end_datetime_local,
        case_actuals.case_date,
        case_actuals.case_day_of_week,
        case_actuals.case_day_of_week_num,
        case_actuals.case_month,
        case_actuals.first_primary_procedure,
        case_actuals.first_primary_surgeon,
        case_actuals.scheduled_duration,
        case_actuals.actual_duration,
        case_actuals.add_on,
        case_actuals.num_phases_match_num_cases,
        case_actuals.case_has_experienced_reordering,
        case_actuals.first_case_by_scheduled_start,
        case_actuals.last_case_by_scheduled_start,
        case_actuals.case_type_short,
        forecasts.forecasted_start_local,
        forecasts.forecasted_end_local,
        forecasts.forecasted_duration,
        forecasts.forecast_start_date,
        forecasts.forecast_run_hour,
        forecasts.forecast_run_date,
        forecasts.forecast_run_at_local,
        forecasts.raw_forecast_source,
        forecasts.forecast_variant_backfilled,
        history.historical_scheduled_start_time_local as scheduled_start_time_at_forecast_run,
        history.historical_scheduled_end_time_local as scheduled_end_time_at_forecast_run,
        history.historical_scheduled_duration as scheduled_duration_at_forecast_run,
        forecasts.days_forecast_run_to_forecast_start,
        forecasts.minutes_forecast_run_to_forecast_start,
        latest_changes_to_scheduled_start_time.latest_time_scheduled_start_time_changed,
        latest_changes_to_scheduled_duration.latest_time_scheduled_duration_changed,
        case
            when case_actuals.case_day_of_week in ('Saturday', 'Sunday') then 1
            else 0
        end as case_during_weekend,
        timestamp_diff(case_actuals.actual_start_datetime_local, forecasts.forecast_run_at_local, minute)
            as minutes_forecast_run_to_actual_start,
        timestamp_diff(case_actuals.actual_end_datetime_local, forecasts.forecast_run_at_local, minute)
            as minutes_forecast_run_to_actual_end,
        timestamp_diff(case_actuals.scheduled_start_datetime_local, forecasts.forecast_run_at_local, minute)
            as minutes_forecast_run_to_scheduled_start,
        timestamp_diff(case_actuals.scheduled_end_datetime_local, forecasts.forecast_run_at_local, minute)
            as minutes_forecast_run_to_scheduled_end,
        case
            when forecasts.forecast_run_at_local < case_actuals.actual_start_datetime_local then 1
            else 0
        end as forecast_run_before_actual_start,
        case
            when forecasts.forecast_run_at_local < case_actuals.actual_end_datetime_local then 1
            else 0
        end as forecast_run_before_actual_end,
        case
            when forecasts.forecast_run_at_local < case_actuals.scheduled_start_datetime_local then 1
            else 0
        end as forecast_run_before_scheduled_start,
        -- find timestamps within 30 seconds
        case
            when forecasts.forecast_run_at_local < case_actuals.scheduled_end_datetime_local then 1
            else 0
        end as forecast_run_before_scheduled_end,
        timestamp_diff(
            forecasts.forecast_run_at_local,
            latest_changes_to_scheduled_start_time.latest_time_scheduled_start_time_changed,
            minute
        ) as minutes_forecast_run_after_latest_time_scheduled_start_changed,
        timestamp_diff(
            forecasts.forecast_run_at_local,
            latest_changes_to_scheduled_duration.latest_time_scheduled_duration_changed,
            minute
        ) as minutes_forecast_run_after_latest_time_scheduled_duration_changed,
        case
            -- if forecasted durations are rounded to nearest minute,
            -- there will be about 1% more forecasts labeled as naive forecast
            when forecasts.forecasted_duration = history.historical_scheduled_duration then 1
            -- sometimes we don't know historical schedule b/c no updated time recorded
            when history.historical_scheduled_duration is null then null
            else 0
        end as forecasted_duration_equals_schedule_at_forecast_run,
        case
            when
                abs(
                    datetime_diff(
                        forecasts.forecasted_start_local, history.historical_scheduled_start_time_local, second
                    )
                )
                <= 1
                then 1
            -- sometimes we don't know historical schedule b/c no updated time recorded
            when history.historical_scheduled_start_time_local is null then null
            else 0
        end as forecasted_start_equals_schedule_at_forecast_run,
        case_actuals.scheduled_duration - case_actuals.actual_duration as duration_schedule_error,
        abs(case_actuals.scheduled_duration - case_actuals.actual_duration) as duration_abs_schedule_error,
        forecasts.forecasted_duration - case_actuals.actual_duration as duration_model_error,
        abs(forecasts.forecasted_duration - case_actuals.actual_duration) as duration_abs_model_error,
        abs(safe_divide((case_actuals.actual_duration - case_actuals.scheduled_duration), case_actuals.actual_duration))
            as duration_abs_percent_schedule_error,
        abs(safe_divide((case_actuals.actual_duration - forecasts.forecasted_duration), case_actuals.actual_duration))
            as duration_abs_percent_model_error,
        case
            when
                abs(forecasts.forecasted_duration - case_actuals.actual_duration) <= 15
                and case_actuals.scheduled_duration <= 90
                then 1
            when
                abs(forecasts.forecasted_duration - case_actuals.actual_duration) <= 30
                and case_actuals.scheduled_duration > 90
                then 1
            else 0
        end as duration_forecast_is_accurate,
        case
            when
                abs(case_actuals.scheduled_duration - case_actuals.actual_duration) <= 15
                and case_actuals.scheduled_duration <= 90
                then 1
            when
                abs(case_actuals.scheduled_duration - case_actuals.actual_duration) <= 30
                and case_actuals.scheduled_duration > 90
                then 1
            else 0
        end as duration_schedule_is_accurate,
        datetime_diff(
            forecasts.forecasted_start_local,
            case_actuals.actual_start_datetime_local,
            minute
        ) as start_forecast_error,
        abs(datetime_diff(
            forecasts.forecasted_start_local,
            case_actuals.actual_start_datetime_local,
            minute
        )) as start_forecast_abs_error,
        datetime_diff(
            case_actuals.scheduled_start_datetime_local,
            case_actuals.actual_start_datetime_local,
            minute
        ) as start_schedule_error,
        abs(datetime_diff(
            case_actuals.scheduled_start_datetime_local,
            case_actuals.actual_start_datetime_local,
            minute
        )) as start_schedule_abs_error,
        case
            when
                abs(
                    datetime_diff(
                        case_actuals.actual_start_datetime_local,
                        forecasts.forecasted_start_local,
                        minute
                    )
                ) <= 30
                then 1
            else 0
        end as start_forecasted_is_within_30_min,
        case
            when
                abs(datetime_diff(
                    case_actuals.scheduled_start_datetime_local,
                    case_actuals.actual_start_datetime_local,
                    minute
                )) <= 30
                then 1
            else 0
        end as start_schedule_is_within_30_min,
        case
            when
                forecasts.forecast_run_at_local
                > latest_changes_to_scheduled_start_time.latest_time_scheduled_start_time_changed
                or latest_changes_to_scheduled_start_time.latest_time_scheduled_start_time_changed is null then 1
            else 0
        end as forecast_run_after_latest_scheduled_start_change,
        case
            when
                forecasts.forecast_run_at_local
                > latest_changes_to_scheduled_duration.latest_time_scheduled_duration_changed
                or latest_changes_to_scheduled_duration.latest_time_scheduled_duration_changed is null then 1
            else 0
        end as forecast_run_after_latest_scheduled_duration_change
    from forecasts
    inner join case_actuals
        on forecasts.case_id = case_actuals.case_id
    --left outer join b/c some cases in cases_history table don't have any updated times
    left outer join history
        on
            forecasts.case_id = history.case_id
            and (
                -- once historical schedule was updated, that updated value is valid
                -- from the updated time to the next updated time
                (
                    forecasts.forecast_run_at_local >= history.updated_time_local
                    and forecasts.forecast_run_at_local < history.next_updated_time_local
                )
                -- for the final update
                or (
                    forecasts.forecast_run_at_local >= history.updated_time_local
                    and history.next_updated_time_local is null
                )
                -- sometimes the forecast run time is seconds before the earliest updated_time_local, so:
                --      if it's the first version, then just filter based on next_updated_time_local
                or (history.version = 1 and forecasts.forecast_run_at_local < history.next_updated_time_local)
                --      if there's only one version and therefore no next_updated_time_local, just use the first version
                or (history.version = 1 and history.next_updated_time_local is null)
                -- as of July 2024, there are about 400 forecasts out of 600k that don't have a first version, and
                -- these forecasts will have no known historical schedule
            )
    left outer join latest_changes_to_scheduled_start_time
        on forecasts.case_id = latest_changes_to_scheduled_start_time.case_id
    left outer join latest_changes_to_scheduled_duration
        on forecasts.case_id = latest_changes_to_scheduled_duration.case_id
)

select
    {{ ds() }} as ds,
    *,
    case
        when
            (num_phases_match_num_cases = 1 or num_phases_match_num_cases is null)
            and (case_has_experienced_reordering = 0 or case_has_experienced_reordering is null)
            and forecast_run_after_latest_scheduled_start_change = 1
            then 1
        else 0
    end as is_recommended_filter_for_start_forecast,
    case
        when forecast_run_after_latest_scheduled_duration_change = 1
            then 1
        else 0
    end as is_recommended_filter_for_duration_forecast
from metrics
