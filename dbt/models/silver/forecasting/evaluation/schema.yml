version: 3

models:
  - name: all_forecasts
    columns:
      - name: ds
        data_type: "timestamp"
      - name: case_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: org_id
        data_type: "string"
      - name: scheduled_start_datetime_local
        data_type: "datetime"
        description: "Final scheduled start time of the case"
      - name: scheduled_end_datetime_local
        data_type: "datetime"
        description: "Final scheduled end time of the case"
      - name: actual_start_datetime_local
        data_type: "datetime"
        description: "Actual start time of the case"
      - name: actual_end_datetime_local
        data_type: "datetime"
        description: "Actual end time of the case"
      - name: case_date
        data_type: "date"
        description: "Scheduled start date of the case"
      - name: case_day_of_week
        data_type: "string"
        description: "Scheduled start day of week for the case"
      - name: case_day_of_week_num
        data_type: "int"
        description: "Schedule start day of week for the case, in integer. 
        This is intended as an ordering column for downstream aggregations"
      - name: case_month
        data_type: "string"
        description: "Month associated with scheduled start time"
      - name: first_primary_procedure
        data_type: "string"
        description: "The first primary procedure as determined in the forecasting features pipeline"
      - name: first_primary_surgeon
        data_type: "string"
        description: "The first primary surgeon as determined in the forecasting features pipeline"
      - name: scheduled_duration
        data_type: "float"
        description: "The final scheduled duration of the case, in minutes"
      - name: actual_duration
        data_type: "float"
        description: "The actual duration of the case, in minutes"
      - name: add_on
        data_type: "int"
        description: "Whether the case was an add on, as determined in forecasting features pipeline"
      - name: num_phases_match_num_cases
        data_type: "int"
        description: "Whether the OR-day had the same number of case phases as scheduled cases"
      - name: case_has_experienced_reordering
        data_type: "int"
        description: "Whether this or any previous cases in the OR-day have been reordered"
      - name: case_type_short
        data_type: "string"
        description: "Case type, as taken from forecasting features pipeline"
      - name: forecasted_start_local
        data_type: "datetime"
        description: "Forecasted start time of the case"
      - name: forecasted_end_local
        data_type: "datetime"
        description: "Forecasted end time of the case"
      - name: forecasted_duration
        data_type: "datetime"
        description: "Forecasted duration of the case, in minutes"
      - name: forecast_start_date
        data_type: "date"
        description: "The date associated with forecasted_start_local"
      - name: forecast_run_hour
        data_type: "int"
        description: "The hour associated with forecast_run_at_local"
      - name: forecast_run_date
        data_type: "date"
        description: "The date associated with forecast_run_at_local"
      - name: forecast_run_at_local
        data_type: "datetime"
        description: "The time at which the forecast inference was run. This could occur weeks before the 
        case up to minutes after the case has had a wheels-in event."
      - name: days_forecast_run_to_forecast_start
        data_type: "int"
        description: "The number of days between when the forecast inference was run and the forecasted
        start time of the case. Can be used in downstream aggregations of forecast metrics."
      - name: minutes_forecast_run_to_forecast_start
        data_type: "int"
        description: "The number of minutes between when the forecast inference was run and the forecasted
        start time of the case. Can be used in downstream aggregations for day-of forecast metrics."
      - name: duration_schedule_error
        data_type: "float"
        description: "Final scheduled duration minus actual duration in minutes"
      - name: duration_abs_schedule_error
        data_type: "float"
        description: "Absolute value of final scheduled duration minutes actual duration in minutes"
      - name: duration_model_error
        data_type: "float"
        description: "Forecasted duration minus actual duration in minutes"
      - name: duration_abs_model_error
        data_type: "float"
        description: "Absolute value of forecasted duration minus actual duration in minutes"
      - name: duration_abs_percent_schedule_error
        data_type: "float"
        description: "Absolute value of actual duration minus final scheduled duration, 
        divided by actual duration"
      - name: duration_abs_percent_model_error
        data_type: "float"
        description: "Absolute value of actual duration minus forecasted duration, 
        divided by actual duration"
      - name: duration_forecast_is_accurate
        data_type: "int"
        description: "Value of 1 when the final scheduled duration is less than 90 minutes and absolute forecasted error 
        is less than 15 minutes, or when the final scheduled duration is over 90 minutes and the absolute 
        forecasted error is less than 30 minutes. Value of 0 otherwise"
      - name: duration_schedule_is_accurate
        data_type: "int"
        description: "Value of 1 when the final scheduled duration is less than 90 minutes and 
        absolute final schedule error is less than 15 minutes, or when the final scheduled duration 
        is over 90 minutes and the absolute final schedule error is less than 30 minutes. Value of 0 otherwise"
      - name: start_forecast_error
        data_type: "float"
        description: "Forecasted start time minus actual start time, in minutes"
      - name: start_forecast_abs_error
        data_type: "float"
        description: "Absolute value of forecasted start time minus actual start time, in minutes"
      - name: start_schedule_error
        data_type: "float"
        description: "Final scheduled start time minus actual start time, in minutes"
      - name: start_schedule_abs_error
        data_type: "float"
        description: "Absolute value of final scheduled start time minus actual start time, in minutes"
      - name: start_forecasted_is_within_30_min
        data_type: "int"
        description: "Value of 1 when the forecasted start time is within 30 minutes of actual start time"
      - name: start_schedule_is_within_30_min
        data_type: "int"
        description: "Value of 1 when the final scheduled start time is within 30 minutes of actual start time"
      - name: latest_time_scheduled_start_time_changed
        data_type: "datetime"
        description: "The latest time at which the scheduled start time of this case was changed.
        Can be used in downstream aggregations to eliminate forecasts made before the final scheduled start time
        was in place."
      - name: latest_time_scheduled_duration_changed
        data_type: "datetime"
        description: "The latest time at which the scheduled duration of this case was changed.
        Can be used in downstream aggregations to eliminate forecasts made before the final scheduled duration
        was in place."
      - name: case_during_weekend
        data_type: "int"
        description: "1 if the case is during the weekend; 0 if during a weekday"
      - name: first_case_by_scheduled_start
        data_type: "int"
        description: "Whether this is the first case of the OR-day, by scheduled start time"
      - name: last_case_by_scheduled_start
        data_type: "int"
        description: "Whether this is the last case of the OR-day, by scheduled start time"
      - name: minutes_forecast_run_to_actual_start
        data_type: "int"
        description: "Minutes between the forecast run time to actual start time; negative values if 
        forecast run happened after actual start time"
      - name: minutes_forecast_run_to_actual_end
        data_type: "int"
        description: "Minutes between the forecast run time to actual end time; negative values if 
        forecast run happened after actual end time"
      - name: minutes_forecast_run_to_scheduled_start
        data_type: "int"
        description: "Minutes between the forecast run time to scheduled start time; negative values if 
        forecast run happened after scheduled start time"
      - name: minutes_forecast_run_to_scheduled_end
        data_type: "int"
        description: "Minutes between the forecast run time to scheduled end time; negative values if 
        forecast run happened after scheduled end time"
      - name: forecast_run_before_actual_start
        data_type: "int"
        description: "Value of 1 if the forecast was run before case started; value of 0 otherwise"
      - name: forecast_run_before_actual_end
        data_type: "int"
        description: "Value of 1 if the forecast was run before case ended; value of 0 otherwise"
      - name: forecast_run_before_scheduled_start
        data_type: "int"
        description: "Value of 1 if the forecast was run before case scheduled start time; value of 0 otherwise"
      - name: forecast_run_before_scheduled_end
        data_type: "int"
        description: "Value of 1 if the forecast was run before case scheduled end time; value of 0 otherwise"
      - name: minutes_forecast_run_after_latest_time_scheduled_start_changed
        data_type: "int"
        description: "The minutes between the latest update time for scheduled start time and forecast run time; 
        positive if the forecast was run after the latest update time, and null if the scheduled start time was never
        updated"
      - name: minutes_forecast_run_after_latest_time_scheduled_duration_changed
        data_type: "int"
        description: "The minutes between the latest update time for scheduled duration and forecast run time; 
        positive if the forecast was run after the latest update time, and null if the scheduled duration was never
        updated"
      - name: scheduled_start_time_at_forecast_run
        data_type: "datetime"
        description: "The scheduled start time of the case as of the forecast run time"
      - name: scheduled_end_time_at_forecast_run
        data_type: "datetime"
        description: "The scheduled end time of the case as of the forecast run time"
      - name: scheduled_duration_at_forecast_run
        data_type: "datetime"
        description: "The scheduled duration of the case as of the forecast run time"
      - name: forecasted_duration_equals_schedule_at_forecast_run
        data_type: "int"
        description: "Value of 1 if the forecasted duration is equal to scheduled_duration_at_forecast_run, 
        which indicates a naive prediction"
      - name: forecasted_start_equals_schedule_at_forecast_run
        data_type: "int"
        description: "Value of 1 if the forecasted start time is equal to scheduled_start_time_at_forecast_run within a 
        30 second buffer, which indicates a naive prediction"
      - name: forecast_run_after_latest_scheduled_start_change
        data_type: "int"
        description: "Value of 1 if the forecast run time was later than the latest scheduled start time change;
        indicates that the forecast used the more up-to-date schedule"
      - name: forecast_run_after_latest_scheduled_duration_change
        data_type: "int"
        description: "Value of 1 if the forecast run time was later than the latest scheduled duration change;
        indicates that the forecast used the more up-to-date schedule"
      - name: is_recommended_filter_for_start_forecast
        data_type: "int"
        description: "Value of 1 if there are no case matching issues, no reordering of cases, and the forecast
        reflects the most up-to-date scheduled star time"
      - name: is_recommended_filter_for_duration_forecast
        data_type: "int"
        description: "Value of 1 if the forecast reflects the most up-to-date scheduled star time"
      - name: raw_forecast_source
        data_type: "string"
        description: "The case_forecasting table that logged this forecast"
      - name: forecast_variant_backfilled
        data_type: "string"
        description: "The forecast variant used to generate this forecast. When the forecast_variant is null,
        assume that it was a forecast made before variants were introduced and that the variant is stable."

