{{ daily_config() }}

with api_scheduled as (
    select
        case_id,
        site_id,
        room,
        scheduled_start_datetime_local,
        date(scheduled_start_datetime_local) as case_date
    from {{ ref('cases') }}
    where
        ds = {{ ds() }}
        and is_apella_data
),

wheels_in_observations as (
    select
        phases.case_id as phases_case_id,
        events.start_time as wheels_in_time,
        sites.org_id,
        datetime(events.start_time, sites.timezone) as wheels_in_datetime_local
    from {{ api_table_snapshot("bronze", "public_phases", "phases") }}
    inner join {{ api_table_snapshot("bronze", "public_events", "events") }}
        on phases.start_event_id = events.id
    inner join {{ api_table_snapshot("bronze", "public_sites", "sites") }}
        on events.site_id = sites.id
    where
        phases.source_type = 'unified'
        and phases.type_id = 'CASE'
        and events.event_type_id = 'patient_wheels_in'
),

case_orders as (

    select
        c.case_id,
        c.site_id,
        c.room,
        c.case_date,
        c.scheduled_start_datetime_local,
        c.wheels_in_datetime_local,
        c.order_by_schedule,
        c.order_by_observations,
        coalesce(
            c.order_by_schedule != c.order_by_observations,
            false
        ) as case_was_reordered
    from (
        select
            scheduled.case_id,
            scheduled.site_id,
            scheduled.room,
            scheduled.case_date,
            scheduled.scheduled_start_datetime_local,
            wheels_in_observations.wheels_in_datetime_local,
            row_number() over (
                partition by scheduled.site_id, scheduled.room, scheduled.case_date
                order by scheduled.scheduled_start_datetime_local, scheduled.case_id
            ) as order_by_schedule,
            row_number() over (
                partition by scheduled.site_id, scheduled.room, scheduled.case_date
                order by wheels_in_observations.wheels_in_datetime_local, scheduled.case_id
            ) as order_by_observations
        from api_scheduled as scheduled
        inner join wheels_in_observations
            on scheduled.case_id = wheels_in_observations.phases_case_id
    ) as c
)


select
    {{ ds() }} as ds,
    case_orders.case_id,
    case_orders.site_id,
    case_orders.room,
    case_orders.case_date,
    case_orders.scheduled_start_datetime_local,
    case_orders.wheels_in_datetime_local,
    case_orders.order_by_schedule,
    case_orders.order_by_observations,
    case_orders.case_was_reordered,
    (max(case_orders.case_was_reordered) over (
        partition by case_orders.site_id, case_orders.room, case_orders.case_date
        order by case_orders.scheduled_start_datetime_local
    )) is true as this_or_prev_case_was_reordered
from case_orders
