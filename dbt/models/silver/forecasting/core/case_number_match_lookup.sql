{{ daily_config() }}

-- TODO: in scheduled_cases CTE, do we need to take out historical date ranges?
-- there ARE days in historical date ranges where phases don't match scheduled

with scheduled_cases as (
    select
        cases.case_id,
        cases.org_id,
        cases.site_id,
        cases.room_id,
        date(datetime(cases.scheduled_start_time, sites.timezone)) as case_date,
        datetime(cases.scheduled_start_time, sites.timezone)
            as scheduled_start_datetime_local,
        datetime(cases.scheduled_end_time, sites.timezone)
            as scheduled_end_datetime_local
    from {{ api_table_snapshot('bronze', 'public_cases', 'cases') }}
    inner join {{ api_table_snapshot('bronze', 'public_sites', 'sites') }}
        on sites.id = cases.site_id
    where
        cases.status = 'scheduled'
        and cases.scheduled_start_time is not null
        and cases.scheduled_end_time is not null
        and sites.name is not null
),

phases as (

    select
        phases.site_id,
        phases.room_id,
        phases.case_id,
        datetime(
            start_events.start_time, sites.timezone
        ) as actual_start_datetime_local,
        date(datetime(start_events.start_time, sites.timezone)) as case_date
    from {{ api_table_snapshot('bronze', 'public_phases', 'phases') }}
    inner join {{ api_table_snapshot('bronze', 'public_events', 'start_events') }}
        on phases.start_event_id = start_events.id
    inner join {{ api_table_snapshot('bronze', 'public_sites', 'sites') }}
        on sites.id = phases.site_id
    where
        phases.source_type = 'unified'
        and phases.status = 'VALID'
        and phases.type_id = 'CASE'
        and phases.room_id is not null
        and start_events.deleted_at is null
),

phase_case_counts as (

    select
        site_id,
        room_id,
        case_date,
        count(actual_start_datetime_local) as num_cases
    from phases
    group by site_id, room_id, case_date
),

scheduled_case_counts as (
    select
        site_id,
        room_id,
        case_date,
        count(scheduled_start_datetime_local) as num_cases
    from scheduled_cases
    group by site_id, room_id, case_date
)


select
    {{ ds() }} as ds,
    phase_case_counts.case_date,
    phase_case_counts.site_id,
    phase_case_counts.room_id,
    phase_case_counts.num_cases as num_phases,
    scheduled_case_counts.num_cases as num_scheduled_cases,
    phase_case_counts.num_cases
    = scheduled_case_counts.num_cases as is_num_cases_match
from phase_case_counts
inner join scheduled_case_counts
    on
        phase_case_counts.case_date = scheduled_case_counts.case_date
        and phase_case_counts.site_id = scheduled_case_counts.site_id
        and phase_case_counts.room_id = scheduled_case_counts.room_id
