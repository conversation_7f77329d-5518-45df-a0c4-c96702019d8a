version: 3

models:
  - name: case_events
    columns:
      - name: ds
        data_type: "timestamp"
      - name: event_id
        data_type: "string"
      - name: start_time
        data_type: "timestamp"
      - name: process_timestamp
        data_type: "timestamp"
      - name: org_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: created_time
        data_type: "timestamp"
      - name: updated_time
        data_type: "timestamp"
      - name: event_type_id
        data_type: "string"
      - name: case_id
        data_type: "string"

  - name: case_procedures
    columns:
    - name: ds
      data_type: "timestamp"
    - name: case_id
      data_type: "string"
    - name: org_id
      data_type: "string"
    - name: created_at
      data_type: "timestamp"
    - name: procedure_created_at
      data_type: "timestamp"
    - name: procedure_name
      data_type: "string"
    - name: hierarchy
      data_type: "int"
    - name: is_primary_procedure
      data_type: "boolean"

  - name: case_surgeons
    description: maps surgeons to cases and designates a primary surgeon.  Only includes HL7 data for OPC19 & WT03 >= '2022-09-15', for OPC18 >= '2023-04-28', for HF > '2023-02-28', and for NB >= '2022-02-01'
    columns:
      - name: ds
        data_type: "timestamp"
      - name: case_id
        data_type: "string"
      - name: staff_id
        data_type: "string"
      - name: start_event_id
        data_type: "string"
      - name: org_id
        data_type: "string"
      - name: staff_created_at
        data_type: "timestamp"
      - name: room
        data_type: "string"
      - name: scheduled_start_datetime_local
        data_type: "timestamp"
      - name: created_at
        data_type: "timestamp"
      - name: surgeon_first_name
        data_type: "string"
      - name: surgeon_last_name
        data_type: "string"
      - name: surgeon_role
        data_type: "string"
      - name: is_primary_surgeon
        data_type: "boolean"

  - name: cases
    columns:
      - name: ds
        data_type: "timestamp"
      - name: case_id
        data_type: "string"
      - name: start_event_id
        data_type: "string"
      - name: end_event_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: org_id
        data_type: "string"
      - name: room
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: external_service_line_name
        data_type: "string"
      - name: service_line_name
        data_type: "string"
      - name: is_add_on
        data_type: "boolean"
      - name: prev_case_id
        data_type: "string"
      - name: preceding_scheduled_turnover_minutes
        data_type: "int"
      - name: prev_flip_case_id
        data_type: "string"
      - name: created_at
        data_type: "timestamp"
      - name: case_classification_types_id
        data_type: "string"
      - name: is_first_case
        data_type: "boolean"
      - name: is_last_case
        data_type: "boolean"
      - name: scheduled_start_datetime_local
        data_type: "datetime"
      - name: scheduled_end_datetime_local
        data_type: "datetime"
      - name: actual_start_datetime_local
        data_type: "datetime"
      - name: actual_end_datetime_local
        data_type: "datetime"
      - name: timezone
        data_type: 'string'
      - name: scheduled_start_timestamp_utc
        data_type: "timestamp"
      - name: scheduled_end_timestamp_utc
        data_type: "timestamp"
      - name: actual_start_timestamp_utc
        data_type: "timestamp"
      - name: actual_end_timestamp_utc
        data_type: "timestamp"
      - name: "patient_class"
        data_type: "string"
      - name: is_apella_data
        data_type: "boolean"
      - name: is_valid_data
        data_type: "boolean"

  - name: reordered_cases
    description: Compares the order of EHR OBSERVED_IN_ROOM observations to the order of scheduled cases. If the order does not match, it indicates the case was reordered.
    columns:
      - name: ds
        data_type: "timestamp"
      - name: case_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: room
        data_type: "string"
      - name: case_date
        data_type: "date"
      - name: scheduled_start_datetime_local
        data_type: "datetime"
      - name: ehr_wheels_in_datetime_local
        data_type: "datetime"
      - name: order_by_schedule
        data_type: "int"
      - name: order_by_ehr
        data_type: "int"
      - name: case_was_reordered
        data_type: "boolean"
      - name: this_or_prev_case_was_reordered
        data_type: "boolean"

  - name: historical_case_id_lookup
    description: Matches apella-generated case_ids to case_ids from external historical files
    columns:
      - name: ds
        data_type: "timestamp"
      - name: apella_case_id
        data_type: "string"
      - name: external_case_id
        data_type: "string"
      - name: historical_file_case_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: scheduled_start_datetime_local
        data_type: "datetime"

  - name: flip_room_case_lookup
    description: "Cases in this table are considered 'flip room cases' if the first primary surgeon has at least 
    one case in another room on the same day. A case_id is matched to the most recent previous flip room case_id. 
    Rows are only available for flip room cases. This table is intended for use in schedule forecasting and identifies
    cases with the constraint that a surgeon must be finished with a case in room A before the 
    next case in room B can begin. Note that this is a looser definition than the one used in the 
    flip_room_utilization table."
    columns:
      - name: ds
        data_type: "timestamp"
      - name: org_id
        data_type: "string"
      - name: case_date
        data_type: "date"
      - name: scheduled_start_datetime_local
        data_type: "datetime"
      - name: prev_flip_case_scheduled_start_datetime_local
        data_type: "datetime"
        description: "The scheduled start time of the most recent case that the first 
        primary surgeon has in another room. "
      - name: is_flip_room
        data_type: "boolean"
        description: "True when the first primary surgeon has at least one case in another room on the same day."
      - name: case_id
        data_type: "string"
      - name: prev_flip_case_id
        data_type: "string"
        description: "The case_id of the most recent case that the first primary surgeon has in another room. "

  - name: historical_reordered_cases
    description: Uses mismatch in start_offset between API and historical file to 
      identify historical OR-days that were likely reordered
    columns:
      - name: ds
        data_type: "timestamp"
      - name: site_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: case_date
        data_type: "date"
      - name: start_offsets_mismatched_in_or_day
        data_type: "boolean"

  - name: case_number_match_lookup
    description: Identifies OR-days where the number of phases and number of scheduled cases do not match.
    columns:
      - name: ds
        data_type: "timestamp"
      - name: case_date
        data_type: "date"
      - name: site_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: num_phases
        data_type: "int"
      - name: num_scheduled_cases
        data_type: "int"

  - name: turnovers_wide
    description: "Turnovers calculated based on wheels-in, wheels-out and back table open timestamps."
    columns:
      - name: ds
        data_type: "timestamp"
      - name: org_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: case_date
        data_type: "date"
      - name: next_apella_case_id
        data_type: "string"
        description: "The case_id of the case that follows the turnover. This will be relevant case for turnover and
        turnover_open (which reports setup for the next case), but the irrelevant case for turnover_clean (which
        reports cleanup for the previous case)."
      - name: next_case_actual_start_datetime_local
        data_type: "datetime"
        description: "The patient wheels-in time of the case corresponding to next_apella_case_id"
      - name: next_case_actual_end_datetime_local
        data_type: "datetime"
        description: "The patient wheels-out time of the case corresponding to next_apella_case_id"
      - name: next_case_scheduled_start_datetime_local
        data_type: "datetime"
        description: "The scheduled start time of the case corresponding to next_apella_case_id"
      - name: next_case_scheduled_end_datetime_local
        data_type: "datetime"
        description: "The scheduled end time of the case corresponding to next_apella_case_id"
      - name: first_case_by_actual_start
        data_type: "tinyint"
        description: "Value of 1 if it's the first case in the OR-day when ordered by actual start time"
      - name: prev_apella_case_id
        data_type: "string"
        description: "The case_id of the case that precedes the turnover. This will be relevant case for turnover and
        turnover_clean (which reports cleanup for the previous case), but the irrelevant case for turnover_open
        (which reports setup for the next case). This is calculated by ordering on actual start times, not 
        scheduled start times."
      - name: prev_case_scheduled_end_datetime_local
        data_type: "datetime"
        description: "The scheduled end time of the case corresponding to prev_apella_case_id"
      - name: prev_case_actual_end_datetime_local
        data_type: "datetime"
        description: "The patient wheels-out time of the case corresponding to prev_apella_case_id"
      - name: back_table_open_datetime_local
        data_type: "datetime"
        description: "The back table open timestamp that occurred between prev_apella_case_id and next_apella_case_id.
        This represents beginning of setup for the next_apella_case_id."
      - name: turnover_clean_minutes
        data_type: "float"
        description: "The duration between wheels-out of the previous case to back table open of the next case.
        Null if there is no back table for the next case."
      - name: turnover_open_minutes
        data_type: "float"
        description: "The duration between back table open of the next case to wheels-in of the next case.
        Null if there is no back table for the next case."
      - name: turnover_minutes
        data_type: "float"
        description: "The duration between wheels-out of the previous case to wheels-in of the next case.
        Null if the next case is the first case by actual start time."
      - name: scheduled_turnover_minutes
        data_type: "float"
        description: "The duration between scheduled end of the previous case to scheduled start of the next case."

  - name: turnovers_active_and_idle
    description: "Turnovers are split into active and idle time, with idle time defined as any time segment of at 
    least two minutes with zero occupancy. "
    columns:
      - name: ds
        data_type: "timestamp"
      - name: org_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: case_date
        data_type: "date"
      - name: apella_case_id
        data_type: "string"
        description: "The case_id of the relevant case for each turnover phase. For example, it's the case that 
        follows the turnover_open_before_case and scheduled_turnover_minutes, and precedes the 
        turnover_clean_after_case. It is also the case referred to by the first_case_by_actual_start.
        See description for other columns for details."
      - name: first_case_by_actual_start
        data_type: "int"
        description: "Value of 1 if the apella_case_id is the first case of the day by actual start time.
        Value of 0 otherwise."
      - name: actual_start_datetime_local
        data_type: "datetime"
        description: "Patient wheels in time of the apella_case_id"
      - name: prev_case_actual_end_datetime_local
        data_type: "datetime"
        description: "Patient wheels out time for the case prior to apella_case_id. This is calculated in
        turnovers_wide by ordering on actual start times, not scheduled start times."
      - name: back_table_open_datetime_local
        data_type: "datetime"
        description: "Back table open time for apella_case_id"
      - name: scheduled_turnover_minutes
        data_type: "float"
        description: "Minutes between scheduled start time of apella_case_id and scheduled end time of 
        the previous case (as defined by actual start times)"
      - name: turnover_before_case
        data_type: "float"
        description: "Minutes between previous case actual end time and apella_case_id actual start time"
      - name: turnover_after_case
        data_type: "float"
        description: "Minutes between apella_case_id actual end time and the next actual start time"
      - name: turnover_open_before_case
        data_type: "float"
        description: "Minutes between the back table open before apella_case_id and its actual start time"
      - name: turnover_clean_after_case
        data_type: "float"
        description: "Minutes between apella_case_id actual end time and next back table open time"
      - name: active_turnover_before_case
        data_type: "float"
        description: "Minutes during the turnover_before_case that are not idle"
      - name: active_turnover_after_case
        data_type: "float"
        description: "Minutes during turnover_after_case that are not idle"
      - name: active_turnover_open_before_case
        data_type: "float"
        description: "Minutes during turnover_open_before_case that are not idle"
      - name: active_turnover_clean_after_case
        data_type: "float"
        description: "Minutes during turnover_clean_after_case that are not idle"
      - name: idle_duration_in_turnover_before_case
        data_type: "float"
        description: "Minutes during turnover_before_case that are idle"
      - name: idle_duration_in_turnover_after_case
        data_type: "float"
        description: "Minutes during turnover_after_case that are idle"
      - name: idle_duration_in_turnover_open_before_case
        data_type: "float"
        description: "Minutes during turnover_open_before_case that are idle"
      - name: idle_duration_in_turnover_clean_after_case
        data_type: "float"
        description: "Minutes during turnover_clean_after_case that are idle"

