{{ daily_config() }}

with all_case_surgeons as (
    {% if not for_feature_fetch() %}
        select
            file_case_id as case_id,
            org_id,
            upper(primary_surgeon.first_name) as surgeon_first_name,
            upper(primary_surgeon.last_name) as surgeon_last_name,
            room_id as room,
            scheduled_start_datetime_local
        -- there is no historical version of case_surgeons because
        -- we only get one primary surgeon per historical case
        from {{ ref("forecasting_historical_cases") }}
        where ds = {{ ds() }}

        union all
    {% endif %}

    select
        case_id,
        org_id,
        surgeon_first_name,
        surgeon_last_name,
        room,
        scheduled_start_datetime_local
    from {{ ref('case_surgeons') }}
    where ds = {{ ds() }}
),

flip_room_indicators as (
    select
        org_id,
        surgeon_first_name,
        surgeon_last_name,
        date(scheduled_start_datetime_local) as case_date,
        count(distinct room) >= 2 as is_flip_room
    from all_case_surgeons
    group by org_id, surgeon_first_name, surgeon_last_name, case_date
),

flip_rooms as (

    select
        c.org_id,
        c.room,
        c.scheduled_start_datetime_local,
        c.case_id,
        c.surgeon_first_name,
        c.surgeon_last_name,
        i.is_flip_room,
        date(c.scheduled_start_datetime_local) as case_date,
        lag(c.scheduled_start_datetime_local) over (
            partition by
                c.org_id,
                date(c.scheduled_start_datetime_local),
                c.surgeon_first_name,
                c.surgeon_last_name
            order by c.scheduled_start_datetime_local
        ) as prev_flip_case_scheduled_start_datetime_local,
        lag(c.case_id) over (
            partition by
                c.org_id,
                date(c.scheduled_start_datetime_local),
                c.surgeon_first_name,
                c.surgeon_last_name
            order by c.scheduled_start_datetime_local
        ) as prev_flip_case_id,
        lag(c.room) over (
            partition by
                c.org_id,
                date(c.scheduled_start_datetime_local),
                c.surgeon_first_name,
                c.surgeon_last_name
            order by c.scheduled_start_datetime_local
        ) as prev_flip_room
    from all_case_surgeons as c
    inner join flip_room_indicators as i
        on
            c.surgeon_first_name = i.surgeon_first_name
            and c.surgeon_last_name = i.surgeon_last_name
            and date(c.scheduled_start_datetime_local) = i.case_date
            and c.org_id = i.org_id
    where
        i.is_flip_room is true

)

-- if a case has two surgeons, and both surgeons have a flip room,
-- there are two rows per case.
-- if this happens, choose the previous flip case that's later
select
    {{ ds() }} as ds,
    org_id,
    case_date,
    scheduled_start_datetime_local,
    prev_flip_case_scheduled_start_datetime_local,
    is_flip_room,
    case_id,
    prev_flip_case_id
from (
    select
        flip_rooms.org_id,
        flip_rooms.case_date,
        flip_rooms.scheduled_start_datetime_local,
        flip_rooms.prev_flip_case_scheduled_start_datetime_local,
        flip_rooms.is_flip_room,
        flip_rooms.case_id,
        flip_rooms.prev_flip_case_id,
        row_number()
            over (
                partition by flip_rooms.case_id
                order by
                    flip_rooms.prev_flip_case_scheduled_start_datetime_local
                    desc
            )
            as row_num
    from flip_rooms
    -- disregard flip rooms where surgeon doesn't
    -- change rooms for consecutive cases
    where
        flip_rooms.room != flip_rooms.prev_flip_room
        or flip_rooms.prev_flip_room is null
)
where row_num = 1
