{{ daily_config() }}

with unique_primary_procedures as (
    select
        case_id,
        procedure_id,
        hierarchy,
        case_row
    from (
        select
            case_procedures.case_id,
            case_procedures.procedure_id,
            case_procedures.hierarchy,
            procedures.created_time as procedure_created_at,
            -- we order by procedure created_time based on the ideal scenario
            -- that procedures are ordered by the time they are
            -- added to the case. In lieu of this, we are ordering by
            -- when the procedure was first created
            row_number()
                over (
                    partition by
                        case_procedures.case_id, case_procedures.hierarchy
                    order by procedures.created_time
                )
                as case_row
        from {{ api_table_snapshot("bronze", "public_case_procedures", "case_procedures") }}
        left outer join
            {{ api_table_snapshot("bronze", "public_procedures", "procedures") }}
            on case_procedures.procedure_id = procedures.id
        where
            case_procedures.archived_time is null
            and case_procedures.hierarchy = 1
    )
    where case_row = 1
)

select
    {{ ds() }} as ds,
    case_procedures.case_id as case_id,
    procedures.org_id,
    case_procedures.created_time as created_at,
    procedures.created_time as procedure_created_at,
    if( -- TODO(DATA-1954): Investigate '\\t\\' / '&' normalization in procedures in all orgs.
        procedures.org_id in ("tampa_general", "houston_methodist"),
        replace(upper(procedures.name), "\\T\\", "&"),
        regexp_replace(upper(procedures.name), "\\s{2,}", " ")
    ) as procedure_name,
    case_procedures.hierarchy,
    case_procedures.procedure_id,
    case
        when unique_primary_procedures.hierarchy = 1 then true
        -- sometimes public_case_procedures has more than one
        -- hierarchy = 1 procedure per case,
        -- but we want to force this table to have
        -- only one primary procedure per case
        when
            (
                case_procedures.hierarchy = 1
                and unique_primary_procedures.hierarchy is null
            )
            or (case_procedures.hierarchy != 1) then false
    end as is_primary_procedure
from {{ api_table_snapshot("bronze", "public_case_procedures", "case_procedures") }}
left outer join {{ api_table_snapshot("bronze", "public_procedures", "procedures") }}
    on case_procedures.procedure_id = procedures.id
left outer join unique_primary_procedures
    on
        case_procedures.case_id = unique_primary_procedures.case_id
        and case_procedures.procedure_id
        = unique_primary_procedures.procedure_id
where
    case_procedures.archived_time is null
    and upper(procedures.name) not like "%CD:%"
    and upper(procedures.name) not like "%�%"
