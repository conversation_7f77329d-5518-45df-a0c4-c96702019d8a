{{ daily_config() }}

with case_time_windows as (
    select
        phases.case_id,
        phases.id as phase_id,
        phases.org_id,
        phases.site_id,
        phases.room_id,
        min(start_events.start_time) as case_start_time,
        max(end_events.start_time) as case_end_time
    from
        {{ api_table_snapshot("bronze", "public_phases", "phases") }}
    inner join
        {{ api_table_snapshot("bronze", "public_events", "start_events") }}
        on
            phases.start_event_id = start_events.id
    inner join
        {{ api_table_snapshot("bronze", "public_events", "end_events") }}
        on
            phases.end_event_id = end_events.id
    where
        phases.type_id = 'CASE'
        and phases.status = 'VALID'
        and phases.source_type = 'unified'
    group by
        phases.case_id, phases.id, phases.org_id, phases.site_id, phases.room_id
),

event_case_mapping as (
    select
        events.id,
        events.start_time,
        events.process_timestamp,
        events.org_id,
        events.site_id,
        events.room_id,
        events.created_time,
        events.updated_time,
        events.event_type_id,
        ctw.case_id as case_id
    from
        {{ api_table_snapshot("bronze", "public_events", "events") }}
    left outer join
        case_time_windows as ctw
        on
            events.org_id = ctw.org_id
            and events.site_id = ctw.site_id
            and events.room_id = ctw.room_id
            and events.start_time
            between ctw.case_start_time and ctw.case_end_time
    where
        events.source_type in ('human_gt', 'prediction')
        and events.deleted_at is null
)

select
    {{ ds () }} as ds,
    id as event_id,
    start_time,
    process_timestamp,
    org_id,
    site_id,
    room_id,
    created_time,
    updated_time,
    event_type_id,
    case_id
from event_case_mapping
where
    case_id is not null
