{{ daily_config(produce_latest_snapshot=True) }}

with turnovers as (
    select
        *,
        -- turnovers will be relative to the "next case" from the turnovers_wide table
        next_apella_case_id as apella_case_id,
        next_case_actual_start_datetime_local as actual_start_datetime_local
    from {{ ref('turnovers_wide') }}
    where ds = {{ ds() }}
),

object_data as (
    select
        org_id,
        room_id,
        cast(frame_time_local_minute_trunc as date) as frame_date_local,
        frame_time_local_minute_trunc,
        extract(hour from frame_time_local_minute_trunc) as frame_hour_local,
        round(avg(total_occupancy) over (
            partition by org_id, room_id
            order by frame_time_local_minute_trunc asc
            rows between 3 preceding and current row
        )) as rolling_avg_total_occupancy_rounded
    from {{ ref('object_model_results_aggregated') }}
    where ds = {{ ds() }}
),

turnover_date_range_per_room as (
    select
        room_id,
        min(case_date) as min_turnover_date,
        max(case_date) as max_turnover_date
    from turnovers
    group by room_id
),

object_date_range_per_room as (
    select
        room_id,
        min(frame_date_local) as min_object_date,
        max(frame_date_local) as max_object_date
    from object_data
    group by room_id
),

-- use this to ensure that the date range of cameras and turnover data overlaps
-- non-overlaps can happen when there is historical/EHR data before cameras are on
-- and can make it appear that this pre-camera data had no idle time
-- note that this does not fix issues with camera downtime after they are turned on
allowable_dates_by_room as (

    select
        object_date_range_per_room.room_id,
        greatest(object_date_range_per_room.min_object_date, turnover_date_range_per_room.min_turnover_date)
            as min_date,
        least(object_date_range_per_room.max_object_date, turnover_date_range_per_room.max_turnover_date) as max_date
    from object_date_range_per_room
    inner join turnover_date_range_per_room
        on object_date_range_per_room.room_id = turnover_date_range_per_room.room_id
),

-- filter turnovers and object data to only include allowable dates
turnovers_filtered as (
    select turnovers.*
    from turnovers
    inner join allowable_dates_by_room
        on
            turnovers.room_id = allowable_dates_by_room.room_id
            and turnovers.case_date >= allowable_dates_by_room.min_date
            and turnovers.case_date <= allowable_dates_by_room.max_date
),

object_data_filtered as (
    select object_data.*
    from object_data
    inner join allowable_dates_by_room
        on
            object_data.room_id = allowable_dates_by_room.room_id
            and object_data.frame_date_local >= allowable_dates_by_room.min_date
            and object_data.frame_date_local <= allowable_dates_by_room.max_date
),

-- one row for each idle minute
-- "idle time" is defined as whenever there are two consecutive minutes with zero occupancy
idle_minutes as (

    select
        *,
        1 as is_idle_based_on_occupancy_stretch
    from (
        select
            *,
            lead(frame_time_local_minute_trunc) over (
                partition by room_id, frame_date_local, org_id
                order by frame_time_local_minute_trunc
            ) as next_minute,
            lag(frame_time_local_minute_trunc) over (
                partition by room_id, frame_date_local, org_id
                order by frame_time_local_minute_trunc
            ) as prev_minute
        from object_data_filtered
        where rolling_avg_total_occupancy_rounded = 0
    )
    where
        next_minute = timestamp_add(frame_time_local_minute_trunc, interval 1 minute)
        or prev_minute = timestamp_add(frame_time_local_minute_trunc, interval -1 minute)
),

-- for each idle minute, classify it as idle during open or clean
-- there is one row for each idle minute in each turnover
-- if a turnover doesn't have idle time, there will only be one row
idle_minutes_to_turnovers as (
    select
        turnovers_filtered.org_id,
        turnovers_filtered.site_id,
        turnovers_filtered.room_id,
        turnovers_filtered.case_date,
        idle_minutes.frame_time_local_minute_trunc as idle_time_local,
        turnovers_filtered.apella_case_id,
        -- if there is no back table, then the "active + idle" turnover_open and turnover_clean are null
        -- so don't calculate active/idle time for turnover_open and turnover_clean
        turnovers_filtered.prev_apella_case_id,
        turnovers_filtered.first_case_by_actual_start,
        turnovers_filtered.back_table_open_datetime_local,
        turnovers_filtered.actual_start_datetime_local,
        turnovers_filtered.prev_case_actual_end_datetime_local,
        turnovers_filtered.turnover_clean_minutes,
        turnovers_filtered.turnover_open_minutes,
        turnovers_filtered.turnover_minutes,
        turnovers_filtered.scheduled_turnover_minutes,
        case
            when idle_minutes.is_idle_based_on_occupancy_stretch is not null then 1
            else 0
        end as is_idle,
        case
            when
                idle_minutes.frame_time_local_minute_trunc <= turnovers_filtered.actual_start_datetime_local
                and idle_minutes.frame_time_local_minute_trunc >= turnovers_filtered.back_table_open_datetime_local
                then idle_minutes.is_idle_based_on_occupancy_stretch
            else 0
        end as is_idle_during_turnover_open,
        case
            when
                idle_minutes.frame_time_local_minute_trunc >= turnovers_filtered.prev_case_actual_end_datetime_local
                and idle_minutes.frame_time_local_minute_trunc <= turnovers_filtered.back_table_open_datetime_local
                then idle_minutes.is_idle_based_on_occupancy_stretch
            else 0
        end as is_idle_during_turnover_clean
    from turnovers_filtered
    -- left outer join b/c idle_minutes only contains rows with zero occupancy
    -- and we want to include turnovers that didn't have zero-occupancy time
    left outer join idle_minutes
        on
            turnovers_filtered.org_id = idle_minutes.org_id
            and turnovers_filtered.room_id = idle_minutes.room_id
            -- for idle minutes between two cases
            and (
                (
                    (
                        turnovers_filtered.actual_start_datetime_local
                        >= idle_minutes.frame_time_local_minute_trunc
                    )
                    and (
                        turnovers_filtered.prev_case_actual_end_datetime_local
                        <= idle_minutes.frame_time_local_minute_trunc
                    )
                )
                -- for idle minutes during turnover_open of first case
                or (
                    (
                        turnovers_filtered.actual_start_datetime_local
                        >= idle_minutes.frame_time_local_minute_trunc
                    )
                    and (
                        turnovers_filtered.back_table_open_datetime_local <= idle_minutes.frame_time_local_minute_trunc
                    )
                    and (turnovers_filtered.first_case_by_actual_start = 1)
                )
            )
),

-- from one-row-per-minute to one-row-per-turnover
active_turnover as (
    select
        org_id,
        site_id,
        room_id,
        case_date,
        apella_case_id,
        first_case_by_actual_start,
        actual_start_datetime_local,
        prev_case_actual_end_datetime_local,
        back_table_open_datetime_local,
        turnover_open_minutes,
        turnover_clean_minutes,
        turnover_minutes,
        scheduled_turnover_minutes,
        sum(is_idle) as idle_duration_in_total_turnover,
        sum(is_idle_during_turnover_clean) as idle_duration_in_turnover_clean,
        sum(is_idle_during_turnover_open) as idle_duration_in_turnover_open,
        turnover_minutes - sum(is_idle) as active_turnover_minutes,
        turnover_clean_minutes - sum(is_idle_during_turnover_clean) as active_turnover_clean_minutes,
        turnover_open_minutes - sum(is_idle_during_turnover_open) as active_turnover_open_minutes
    from idle_minutes_to_turnovers
    group by
        org_id,
        site_id,
        room_id,
        case_date,
        apella_case_id,
        first_case_by_actual_start,
        actual_start_datetime_local,
        prev_case_actual_end_datetime_local,
        back_table_open_datetime_local,
        turnover_open_minutes,
        turnover_clean_minutes,
        turnover_minutes,
        scheduled_turnover_minutes
)

select
    {{ ds() }} as ds,
    org_id,
    site_id,
    room_id,
    case_date,
    apella_case_id,
    first_case_by_actual_start,
    actual_start_datetime_local,
    prev_case_actual_end_datetime_local,
    back_table_open_datetime_local,
    scheduled_turnover_minutes,
    -- in "before case" and "after case" columns, the "case" refers to the apella_case_id
    -- shift the durations of turnovers to get the relevant phase relative to the apella_case_id
    turnover_minutes as turnover_before_case,
    lead(turnover_minutes) over (
        partition by room_id, case_date
        order by actual_start_datetime_local
    ) as turnover_after_case,
    turnover_open_minutes as turnover_open_before_case,
    lead(turnover_clean_minutes) over (
        partition by room_id, case_date
        order by actual_start_datetime_local
    ) as turnover_clean_after_case,
    -- for active turnover
    active_turnover_minutes as active_turnover_before_case,
    lead(active_turnover_minutes) over (
        partition by room_id, case_date
        order by actual_start_datetime_local
    ) as active_turnover_after_case,
    active_turnover_open_minutes as active_turnover_open_before_case,
    lead(active_turnover_clean_minutes) over (
        partition by room_id, case_date
        order by actual_start_datetime_local
    ) as active_turnover_clean_after_case,
    idle_duration_in_total_turnover as idle_duration_in_turnover_before_case,
    lead(idle_duration_in_total_turnover) over (
        partition by room_id, case_date
        order by actual_start_datetime_local
    ) as idle_duration_in_turnover_after_case,
    idle_duration_in_turnover_open as idle_duration_in_turnover_open_before_case,
    lead(idle_duration_in_turnover_clean) over (
        partition by room_id, case_date
        order by actual_start_datetime_local
    ) as idle_duration_in_turnover_clean_after_case
from active_turnover
