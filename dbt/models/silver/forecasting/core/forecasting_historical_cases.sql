{{ daily_config() }}

with historical_cases_normalized as (
    select
        historical_cases_normalized.ds,
        historical_cases_normalized.org_id,
        historical_cases_normalized.file_case_id,
        historical_cases_normalized.apella_site_id as site_id,
        historical_cases_normalized.apella_site_name as site,
        historical_cases_normalized.scheduled_start_datetime_local,
        historical_cases_normalized.scheduled_end_datetime_local,
        historical_cases_normalized.actual_start_datetime_local,
        historical_cases_normalized.actual_end_datetime_local,
        historical_cases_normalized.patient_class,
        historical_cases_normalized.is_add_on,
        historical_cases_normalized.primary_surgeon,
        historical_cases_normalized.primary_procedure_name,
        historical_cases_normalized.full_procedure_name,
        historical_cases_normalized.procedure_count,
        historical_cases_normalized.source_file_date_utc,
        historical_cases_normalized.source_file_gcs_path,
        historical_cases_normalized.apella_room_id as room_id,
        historical_cases_normalized.case_classification_types_id,
        historical_cases_normalized.apella_staff_id,
        historical_cases_normalized.all_procedure_names,
        case
            when
                source_file_gcs_path not in (
                    'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMH_WALTER_20240308.csv', -- noqa: LT05
                    'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMH_OPC_18_20240308.csv' -- noqa: LT05
                )
                then true
            when
                (
                    source_file_gcs_path
                    = 'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMH_OPC_18_20240308.csv' -- noqa: LT05
                    and date(historical_cases_normalized.scheduled_start_datetime_local) < '2023-12-22'
                    and historical_cases_normalized.customer_room_name not in ('OPC18OR12', 'OPC18OR14')
                )
                then true
            when
                (
                    source_file_gcs_path
                    = 'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMH_OPC_18_20240308.csv' -- noqa: LT05
                    and date(historical_cases_normalized.scheduled_start_datetime_local) <= '2024-02-01'
                    and historical_cases_normalized.customer_room_name in ('OPC18OR12')
                )
                then true
            when
                (
                    source_file_gcs_path
                    = 'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMH_WALTER_20240308.csv' -- noqa: LT05
                    and date(historical_cases_normalized.scheduled_start_datetime_local) < '2022-09-16'
                    and historical_cases_normalized.customer_room_name not in ('WT3OR 05', 'WT3OR 08')
                )
                then true
            when
                (
                    source_file_gcs_path
                    = 'prod-data-warehouse/historical_cases/houston_methodist/Apella_Historical_Cases_HMH_WALTER_20240308.csv' -- noqa: LT05
                    and date(historical_cases_normalized.scheduled_start_datetime_local) < '2023-01-09'
                    and historical_cases_normalized.customer_room_name in ('WT3OR 05')
                )
                then true
            else false
        end as is_valid_historical_data
    from {{ ref("historical_cases_normalized") }} as historical_cases_normalized
    where
        historical_cases_normalized.ds = {{ ds() }}
        and {{ forecasting_historical_file_filter() }}
        and historical_cases_normalized.cancellation_reason is null
        and historical_cases_normalized.primary_surgeon is not null
        and historical_cases_normalized.apella_room_id is not null
)

select
    {{ ds() }} as ds,
    historical_cases_normalized.org_id,
    historical_cases_normalized.file_case_id,
    historical_cases_normalized.room_id,
    historical_cases_normalized.site_id,
    historical_cases_normalized.site,
    historical_cases_normalized.scheduled_start_datetime_local,
    historical_cases_normalized.scheduled_end_datetime_local,
    historical_cases_normalized.actual_start_datetime_local,
    historical_cases_normalized.actual_end_datetime_local,
    historical_cases_normalized.case_classification_types_id,
    historical_cases_normalized.patient_class,
    historical_cases_normalized.is_add_on,
    historical_cases_normalized.primary_surgeon,
    historical_cases_normalized.apella_staff_id,
    regexp_replace(
        regexp_replace(
            regexp_replace(
                trim(
                    regexp_replace(
                        upper(
                            split(historical_cases_normalized.primary_procedure_name, '[')[offset(0)]
                        ),
                        r'(RIGHT|RIGHT |, RIGHT| RIGHT|, RIGHT|LEFT|LEFT |, LEFT| LEFT|LEFT|,LEFT|-|INDEX)', -- noqa: LT05
                        ' '
                    )
                ),
                r'(\\s+$)|,$|\\.$',
                ''
            ),
            r'^, ',
            ''
        ), '\\s{2,}', ' '
    ) as primary_procedure_name,
    historical_cases_normalized.full_procedure_name,
    historical_cases_normalized.procedure_count,
    historical_cases_normalized.source_file_date_utc,
    historical_cases_normalized.source_file_gcs_path,
    historical_cases_normalized.all_procedure_names
from historical_cases_normalized
where historical_cases_normalized.is_valid_historical_data
