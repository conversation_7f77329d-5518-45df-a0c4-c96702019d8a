{{ daily_config() }}

select
    {{ ds() }} as ds,
    case_staff.apella_case_id as case_id,
    case_staff.apella_staff_id as staff_id,
    case_staff.org_id,
    staff.created_time as staff_created_at,
    cases.room,
    cases.scheduled_start_datetime_local,
    case_staff.api_created_timestamp_utc as created_at,
    upper(case_staff.first_name) as surgeon_first_name,
    upper(case_staff.last_name) as surgeon_last_name,
    case_staff.role as surgeon_role,
    case_staff.is_candidate_primary_surgeon as is_primary_surgeon
from {{ ref("core_case_staff") }} as case_staff
left outer join {{ ref("cases") }} as cases
    on
        cases.case_id = case_staff.apella_case_id
        and cases.is_apella_data
left outer join {{ api_table_snapshot("bronze", "public_staff", "staff") }}
    on case_staff.apella_staff_id = staff.id
where
    staff.archived_time is null
    and cases.ds = {{ ds() }}
    and case_staff.ds = {{ ds() }}
    and (
        case_staff.is_candidate_primary_surgeon or case_staff.role = 'Co-Surgeon'
    )
