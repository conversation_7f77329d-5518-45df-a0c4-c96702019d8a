{{ daily_config() }}

{% set sites_config = forecasting_sites_config() %}
{% set config_sites = sites_config.site_ids.keys() %}

with dupe_cases as (

    -- duplication of case_ids stems from public_phases, not public_cases
    select case_id
    from {{ api_table_snapshot("bronze", "public_phases", alias="phases") }}
    where
        phases.source_type = 'unified'
        and phases.status = 'VALID'
        and phases.type_id = 'CASE'
        and phases.room_id is not null
    group by case_id
    having count(case_id) > 1
),

filtered_cases as (

    select cases.*
    from {{ api_table_snapshot("bronze", "public_cases", alias="cases") }}
    left outer join
        {{
            api_table_snapshot(
                "bronze",
                "public_case_matching_status_reason",
                alias="case_matching_status_reason")
        }}
        on cases.case_id = case_matching_status_reason.case_id
    where
        cases.status = 'scheduled'
        and cases.scheduled_start_time is not null
        and cases.scheduled_end_time is not null
        and timestamp_diff(
            datetime(cases.scheduled_end_time),
            datetime(cases.scheduled_start_time),
            minute
        ) > 0
        and cases.case_id not in (select case_id from dupe_cases)
        -- Exclude cases which have been manually marked by users as not real cases. Sometimes this
        -- is done when the EHR cannot be changed but the user wants the Apella schedule to be
        -- correct.  These cases should be treated in the same manner as cases which are marked
        -- "canceled" in the bronze public_cases table.
        and (
            case_matching_status_reason.case_id is null
            or case_matching_status_reason.case_matching_status not in ('NOT_A_CASE', 'CANCELED')
        )
)

select
    {{ ds() }} as ds,
    filtered_cases.case_id as case_id,
    phases.start_event_id as start_event_id,
    phases.end_event_id as end_event_id,
    filtered_cases.created_time as created_at,
    filtered_cases.org_id as org_id,
    sites.id as site_id,
    filtered_cases.room_id as room,
    filtered_cases.room_id as room_id,
    service_line.external_service_line_id as external_service_line_name,
    service_line.name as service_line_name,
    filtered_cases.is_add_on as is_add_on,
    filtered_cases.case_classification_types_id as case_classification_types_id,
    coalesce(filtered_cases.patient_class, 'UNSPECIFIED') as patient_class,
    row_number() over (
        partition by
            filtered_cases.org_id,
            sites.id,
            filtered_cases.room_id,
            extract(
                date from filtered_cases.scheduled_start_time
                at time zone sites.timezone
            )
        order by filtered_cases.scheduled_start_time
    ) = 1 as is_first_case,
    row_number() over (
        partition by
            filtered_cases.org_id,
            sites.id,
            filtered_cases.room_id,
            extract(
                date from filtered_cases.scheduled_start_time
                at time zone sites.timezone
            )
        order by filtered_cases.scheduled_start_time desc
    ) = 1 as is_last_case,
    datetime(filtered_cases.scheduled_start_time, sites.timezone)
        as scheduled_start_datetime_local,
    datetime(filtered_cases.scheduled_end_time, sites.timezone)
        as scheduled_end_datetime_local,
    datetime(start_events.start_time, sites.timezone)
        as actual_start_datetime_local,
    datetime(end_events.start_time, sites.timezone)
        as actual_end_datetime_local,
    sites.timezone as timezone,
    filtered_cases.scheduled_start_time as scheduled_start_timestamp_utc,
    filtered_cases.scheduled_end_time as scheduled_end_timestamp_utc,
    start_events.start_time as actual_start_timestamp_utc,
    end_events.start_time as actual_end_timestamp_utc,
    {{
        forecasting_is_apella_data(
            "date(datetime(filtered_cases.scheduled_start_time, sites.timezone))",
            "sites.id",
            "filtered_cases.room_id"
        )
    }} as is_apella_data,
    case
        {% for site_id in config_sites %}
            {% if site_id in ['HF-VH02'] %}
                when
                    sites.id = '{{ site_id }}'
                    and date(
                        datetime(
                            filtered_cases.scheduled_start_time, sites.timezone
                        )
                    )
                    < extract(
                        date from parse_timestamp(
                            '%Y-%m-%d',
                            '{{ sites_config.site_ids[site_id].valid_start_date }}'
                        )
                    )
                    then false
            {% endif %}
        {% endfor %}
        else true
    end as is_valid_data
from filtered_cases
left outer join {{ api_table_snapshot("bronze", "public_phases", alias="phases") }}
    on
        filtered_cases.case_id = phases.case_id
        and phases.source_type = 'unified'
        and phases.status = 'VALID'
        and phases.type_id = 'CASE'
        and phases.room_id is not null
left outer join {{ api_table_snapshot("bronze", "public_events", alias="start_events") }}
    on
        phases.start_event_id = start_events.id
        and start_events.deleted_at is null
left outer join {{ api_table_snapshot("bronze", "public_events", alias="end_events") }}
    on
        phases.end_event_id = end_events.id
        and end_events.deleted_at is null
left outer join {{ api_table_snapshot("bronze", "public_sites", alias="sites") }}
    on
        filtered_cases.site_id = sites.id
        and sites.name is not null
left outer join {{ api_table_snapshot("bronze", "public_service_lines", alias="service_line") }}
    on
        filtered_cases.service_line_id = service_line.id
        and filtered_cases.org_id = service_line.org_id
where
    {% if for_feature_fetch() %}
    extract(
        date from datetime(filtered_cases.scheduled_start_time, sites.timezone)
    ) >= '{case_scheduled_date}'
    {% else %}
        -- only take cases that actually happened
        phases.case_id is not null
    {% endif %}
