{{ daily_config() }}


with historical_file_start_offsets as (
    select
        historical_cases.case_id as historical_file_case_id,
        historical_case_lookup.apella_case_id,
        historical_cases.site_id,
        historical_cases.scheduled_start_datetime_local,
        historical_cases.start_offset as file_start_offset,
        date(historical_cases.scheduled_start_datetime_local) as case_date
    from {{ ref('historical_case_features') }} as historical_cases
    inner join {{ ref('historical_case_id_lookup') }} as historical_case_lookup
        on
            historical_cases.case_id
            = historical_case_lookup.historical_file_case_id
    where
        historical_cases.ds = {{ ds() }}
        and historical_case_lookup.ds = {{ ds() }}
        and historical_cases.site_id = 'HF-VH02'
),

api_start_offsets as (
    select
        api_data.case_id as apella_case_id,
        api_data.site_id,
        api_data.room_id,
        api_data.scheduled_start_datetime_local,
        api_data.actual_start_datetime_local,
        date(api_data.scheduled_start_datetime_local) as case_date,
        timestamp_diff(
            api_data.actual_start_datetime_local,
            api_data.scheduled_start_datetime_local,
            minute
        ) as api_start_offset
    from {{ ref('cases') }} as api_data
    where ds = {{ ds() }}
),

start_offsets_joined as (
    select
        api_start_offsets.apella_case_id,
        api_start_offsets.site_id,
        api_start_offsets.room_id,
        api_start_offsets.case_date,
        api_start_offsets.scheduled_start_datetime_local,
        api_start_offsets.actual_start_datetime_local
            as api_actual_start_datetime_local,
        api_start_offsets.api_start_offset,
        historical_file_start_offsets.file_start_offset
    from api_start_offsets
    inner join historical_file_start_offsets
        on
            api_start_offsets.apella_case_id
            = historical_file_start_offsets.apella_case_id
)

select
    {{ ds() }} as ds,
    site_id,
    room_id,
    case_date,
    max(api_start_offset != file_start_offset)
        as start_offsets_mismatched_in_or_day
from start_offsets_joined
group by
    site_id,
    room_id,
    case_date
