{{ daily_config() }}

with manual_opc19_cases as (
    select
        cases.site_id,
        cases.room_id,
        cases.external_case_id,
        cases.case_id,
        cases.scheduled_start_time,
        datetime(cases.scheduled_start_time, sites.timezone)
            as scheduled_start_datetime_local
    from {{ api_table_snapshot("bronze", "public_cases", "cases") }}
    inner join {{ api_table_snapshot("bronze", "public_sites", "sites") }}
        on sites.id = cases.site_id
    where
        cases.org_id = 'houston_methodist'
        and cases.site_id = 'HMH-OPC19'
        and cases.external_case_id like '%MANUAL%'
),

opc19_manual_case_lookup as (
    select
        manual_opc19_cases.external_case_id,
        manual_opc19_cases.case_id as apella_case_id,
        silver_historical_cases.case_id as historical_file_case_id,
        manual_opc19_cases.site_id,
        manual_opc19_cases.room_id,
        manual_opc19_cases.scheduled_start_datetime_local
    from manual_opc19_cases
    inner join {{ ref('historical_case_features') }} as silver_historical_cases
        on
            manual_opc19_cases.room_id = silver_historical_cases.room_id
            and manual_opc19_cases.scheduled_start_datetime_local
            = silver_historical_cases.scheduled_start_datetime_local
    where silver_historical_cases.ds = {{ ds() }}
),

historical_case_features_unified as (
    select
        historical_cases.case_id as historical_file_case_id,
        historical_cases.site_id,
        historical_cases.scheduled_start_datetime_local,
        trim(historical_cases.case_id, 'hfvi_')
            as unified_historical_external_case_id,
        date(historical_cases.scheduled_start_datetime_local) as case_date
    from {{ ref('historical_case_features') }} as historical_cases
    where ds = {{ ds() }}
),

bronze_cases_unified as (
    select
        bronze_cases.case_id,
        bronze_cases.room_id,
        bronze_cases.site_id,
        bronze_cases.external_case_id,
        trim(bronze_cases.external_case_id, 'health_first::HFVI_')
            as unified_bronze_external_case_id
    from {{ api_table_snapshot("bronze", "public_cases", "bronze_cases") }}
)

select
    {{ ds() }} as ds,
    bronze_cases_unified.case_id as apella_case_id,
    bronze_cases_unified.external_case_id,
    historical_case_features_unified.historical_file_case_id,
    bronze_cases_unified.site_id,
    bronze_cases_unified.room_id,
    historical_case_features_unified.scheduled_start_datetime_local
from historical_case_features_unified
-- this join turns external file ids into apella-generated case_ids
inner join bronze_cases_unified
    on
        historical_case_features_unified.unified_historical_external_case_id
        = bronze_cases_unified.unified_bronze_external_case_id
where
    bronze_cases_unified.case_id
    != historical_case_features_unified.historical_file_case_id

union all

select
    {{ ds() }} as ds,
    apella_case_id,
    external_case_id,
    historical_file_case_id,
    site_id,
    room_id,
    scheduled_start_datetime_local
from opc19_manual_case_lookup
