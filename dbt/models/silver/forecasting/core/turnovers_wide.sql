{{ daily_config() }}

with cases as (
    select
        org_id,
        site_id,
        room_id,
        apella_case_id as next_apella_case_id,
        actual_start_datetime_local as next_case_actual_start_datetime_local,
        actual_end_datetime_local as next_case_actual_end_datetime_local,
        scheduled_start_datetime_local as next_case_scheduled_start_datetime_local,
        scheduled_end_datetime_local as next_case_scheduled_end_datetime_local,
        cast(row_number() over (
            partition by site_id, room_id, date(scheduled_start_datetime_local)
            order by actual_start_datetime_local
        ) = 1 as tinyint) as first_case_by_actual_start,
        date(actual_start_datetime_local) as case_date,
        -- here, previous case is calculated by actual start; in other places it's calculated by scheduled start
        -- we calculate by actual start here because we focus on actual turnovers;
        -- ordering by scheduled start will lead to inaccurate calc of actual turnovers
        lag(apella_case_id) over (
            partition by site_id, room_id, date(scheduled_start_datetime_local)
            order by actual_start_datetime_local
        ) as prev_apella_case_id,
        lag(scheduled_end_datetime_local) over (
            partition by site_id, room_id, date(scheduled_start_datetime_local)
            order by actual_start_datetime_local
        ) as prev_case_scheduled_end_datetime_local,
        lag(actual_end_datetime_local) over (
            partition by site_id, room_id, date(scheduled_start_datetime_local)
            order by actual_start_datetime_local
        ) as prev_case_actual_end_datetime_local
    from {{ ref('core_cases') }}
    where
        ds = {{ ds() }}
        and is_case_matched
),

duplicate_case_ids as (

    select next_apella_case_id
    from {{ ref('core_turnover_phases') }}
    where
        ds = {{ ds() }}
        and phase_type_id = 'TURNOVER_OPEN'
    group by next_apella_case_id
    having count(*) > 1
),

turnover_opens as (
    select
        org_id,
        room_id,
        site_id,
        phase_date_local,
        prev_apella_case_id,
        next_apella_case_id,
        phase_type_id,
        phase_start_datetime_local,
        phase_end_datetime_local
    from {{ ref('core_turnover_phases') }}
    where
        ds = {{ ds() }}
        and phase_type_id = 'TURNOVER_OPEN'
        -- if there are duplicates (6 total as of April 22, 2024), eliminate both
        -- because these duplicates can happen on different days and unsure which one to pick
        -- the total turnover count will still be intact
        and next_apella_case_id not in (select next_apella_case_id from duplicate_case_ids)
),

-- the time range for HF in core cases doesn't match with time range in core_turnover_phases
-- HF core cases goes back to 2020; we only begin to have back table events in 2023
-- so filter turnovers to only dates available in core_turnover_phases
-- to avoid mistakenly labeling cases as having no back table
turnover_available_date_range as (
    select
        site_id,
        min(phase_date_local) as min_turnover_date,
        max(phase_date_local) as max_turnover_date
    from turnover_opens
    group by site_id
)

select
    {{ ds() }} as ds,
    cases.org_id,
    cases.site_id,
    cases.room_id,
    cases.case_date,
    cases.next_apella_case_id,
    cases.next_case_actual_start_datetime_local,
    cases.next_case_actual_end_datetime_local,
    cases.next_case_scheduled_start_datetime_local,
    cases.next_case_scheduled_end_datetime_local,
    cases.first_case_by_actual_start,
    cases.prev_apella_case_id,
    cases.prev_case_scheduled_end_datetime_local,
    cases.prev_case_actual_end_datetime_local,
    turnover_opens.phase_start_datetime_local as back_table_open_datetime_local,
    timestamp_diff(
        turnover_opens.phase_start_datetime_local,
        cases.prev_case_actual_end_datetime_local,
        minute
    ) as turnover_clean_minutes,
    timestamp_diff(
        cases.next_case_actual_start_datetime_local,
        turnover_opens.phase_start_datetime_local,
        minute
    ) as turnover_open_minutes,
    timestamp_diff(
        cases.next_case_actual_start_datetime_local,
        cases.prev_case_actual_end_datetime_local,
        minute
    ) as turnover_minutes,
    timestamp_diff(
        cases.next_case_scheduled_start_datetime_local,
        cases.prev_case_scheduled_end_datetime_local,
        minute
    ) as scheduled_turnover_minutes
from cases
inner join turnover_available_date_range as date_range
    on
        cases.site_id = date_range.site_id
        and cases.case_date >= date_range.min_turnover_date
        and cases.case_date <= date_range.max_turnover_date
left outer join turnover_opens
    on cases.next_apella_case_id = turnover_opens.next_apella_case_id
