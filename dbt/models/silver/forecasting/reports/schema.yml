version: 1

models:
  - name: forecasting_report_data_used_in_training
    description: As part of DATA-2161, exposing customer provided data corresponding for cases used in training case duration models.
    columns:
      - name: ds
        data_type: timestamp
      - name: org_id
        data_type: string
      - name: customer_case_id
        data_type: string
        description: The case ID as provided by the customer. Guaranteed to not be null.
      - name: apella_case_id
        data_type: string
        description: Apella's unique case ID for this case. Not available for historical cases.
      - name: actual_start_datetime_local
        data_type: timestamp
      - name: actual_duration
        data_type: bigint
      - name: scheduled_duration
        data_type: bigint
      - name: first_primary_procedure
        data_type: string
        description: A normalized string as used in the forecasting pipeline.
      - name: first_primary_surgeon
        data_type: string
        description: A normalized string representing the first primary surgeon as used in the forecasting pipeline.
      - name: first_primary_surgeon_id
        data_type: string
        description: Apella's unique surgeon ID for the first primary surgeon. Not guaranteed to be available if a historical case comes from a room Apella is not live in.
      - name: room_id
        data_type: string
        description: <PERSON>pella's unique room ID. Not guaranteed to be available if a historical case comes from a room A<PERSON> is not live in.
      - name: site_id
        data_type: string
        description: <PERSON>pella's unique site ID. Not guaranteed to be available if a historical case comes from a room <PERSON><PERSON> is not live in.
      - name: site
        data_type: string
        description: The site name as provided by the customer. Guaranteed to not be null.
      - name: room
        data_type: string
        description: The room name as provided by the customer. Guaranteed to not be null.
      - name: procedure_list
        data_type: array<string>
        description: A list of procedure strings. Note that historical data representation may not look like API data.
      - name: surgeon_list
        data_type: array<struct<string, string>>
        description: A list of surgeons <first_name, last_name> based on is_candidate_primary_surgeon = True in core_case_staff.
      - name: data_source
        data_type: string
        description: One of "HISTORICAL_DATA_DUMP" or "APELLA_LIVE"