{{ daily_config(produce_latest_snapshot=True) }}

with all_forecasting_training_data as (
    select
        org_id,
        apella_data,
        case_id,
        actual_start_datetime_local,
        actual_duration,
        scheduled_duration,
        first_primary_procedure,
        first_primary_surgeon,
        first_primary_surgeon_id,
        case_procedure_list as procedure_list,
        room_id,
        site_id
    from {{ ref("forecasting_case_features_combined") }}
    where ds = {{ ds() }}
),

latest_historical_cases_used_in_forecasting as (
    select *
    from {{ ref("forecasting_historical_cases") }}
    where ds = {{ ds() }}
),

latest_historical_cases_normalized as (
    select historical_cases_normalized.*
    from {{ ref("historical_cases_normalized") }} as historical_cases_normalized
    inner join latest_historical_cases_used_in_forecasting
        on
            historical_cases_normalized.source_file_gcs_path
            = latest_historical_cases_used_in_forecasting.source_file_gcs_path
            and historical_cases_normalized.file_case_id = latest_historical_cases_used_in_forecasting.file_case_id
    where historical_cases_normalized.ds = {{ ds() }}

),

historical_training_data as (
    select
        all_forecasting_training_data.org_id,
        all_forecasting_training_data.case_id as customer_case_id,
        all_forecasting_training_data.actual_start_datetime_local,
        all_forecasting_training_data.actual_duration,
        all_forecasting_training_data.scheduled_duration,
        all_forecasting_training_data.first_primary_procedure,
        all_forecasting_training_data.first_primary_surgeon,
        all_forecasting_training_data.first_primary_surgeon_id,
        all_forecasting_training_data.room_id,
        all_forecasting_training_data.site_id,
        rooms.name as room,
        if(
            array_length(latest_historical_cases_normalized.all_procedure_names) = 0,
            [latest_historical_cases_normalized.primary_procedure_name],
            latest_historical_cases_normalized.all_procedure_names
        ) as procedure_list,
        -- TODO: Pipe through other surgeon info when available.
        [
            struct(
                latest_historical_cases_normalized.primary_surgeon.first_name,
                latest_historical_cases_normalized.primary_surgeon.last_name
            )
        ] as surgeon_list
    from all_forecasting_training_data
    inner join latest_historical_cases_normalized
        on
            all_forecasting_training_data.org_id = latest_historical_cases_normalized.org_id
            and all_forecasting_training_data.case_id = latest_historical_cases_normalized.file_case_id
    inner join {{ api_table_snapshot("bronze", "public_rooms", alias="rooms") }}
        on latest_historical_cases_normalized.apella_room_id = rooms.id
    where all_forecasting_training_data.apella_data = 0

),

core_cases_to_staff as (
    select
        core_cases.apella_case_id,
        any_value(core_cases.customer_case_id) as customer_case_id,
        any_value(core_cases.room_name) as room,
        array_agg(struct(core_case_staff.first_name, core_case_staff.last_name)) as surgeon_list
    from {{ ref("core_cases") }} as core_cases
    inner join {{ ref("core_case_staff") }} as core_case_staff
        on
            core_case_staff.ds = {{ ds() }}
            and core_cases.apella_case_id = core_case_staff.apella_case_id
    where
        core_cases.ds = {{ ds() }}
        and (core_case_staff.is_candidate_primary_surgeon or core_case_staff.role = 'Co-Surgeon')
        and core_cases.is_case_matched
    group by core_cases.apella_case_id
),

apella_live_training_data as (
    select
        all_forecasting_training_data.org_id,
        core_cases_to_staff.customer_case_id,
        all_forecasting_training_data.case_id as apella_case_id,
        all_forecasting_training_data.actual_start_datetime_local,
        all_forecasting_training_data.actual_duration,
        all_forecasting_training_data.scheduled_duration,
        all_forecasting_training_data.first_primary_procedure,
        all_forecasting_training_data.first_primary_surgeon,
        all_forecasting_training_data.first_primary_surgeon_id,
        all_forecasting_training_data.room_id,
        all_forecasting_training_data.site_id,
        core_cases_to_staff.room,
        core_cases_to_staff.surgeon_list,
        all_forecasting_training_data.procedure_list
    from all_forecasting_training_data
    inner join core_cases_to_staff
        on all_forecasting_training_data.case_id = core_cases_to_staff.apella_case_id
    where all_forecasting_training_data.apella_data = 1
)

select
    {{ ds() }} as ds,
    report.*,
    sites.name as site
from (
    select
        org_id,
        customer_case_id,
        null as apella_case_id,
        actual_start_datetime_local,
        actual_duration,
        scheduled_duration,
        first_primary_procedure,
        first_primary_surgeon,
        first_primary_surgeon_id,
        room_id,
        site_id,
        room,
        procedure_list,
        surgeon_list,
        'HISTORICAL_DATA_DUMP' as data_source
    from historical_training_data

    union all

    select
        org_id,
        customer_case_id,
        apella_case_id,
        actual_start_datetime_local,
        actual_duration,
        scheduled_duration,
        first_primary_procedure,
        first_primary_surgeon,
        first_primary_surgeon_id,
        room_id,
        site_id,
        room,
        procedure_list,
        surgeon_list,
        'APELLA_LIVE' as data_source
    from apella_live_training_data
) as report
left outer join {{ api_table_snapshot("bronze", "public_sites", "sites") }}
    on report.site_id = sites.id
