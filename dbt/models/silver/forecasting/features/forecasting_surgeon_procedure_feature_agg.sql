{{ daily_config(produce_latest_snapshot=True) }}

with filtered_forecasting_data as (
    select
        case_features.first_primary_surgeon,
        case_features.first_primary_procedure,
        case_features.procedure_count,
        -- TODO: Remove surgeon in favor of (first_primary_surgeon, first_primary_procedure, procedure_count)
        case_features.surgeon_proc_combo_num,
        case_features.actual_duration,
        case_features.end_offset,
        case_features.schedule_actual_diff_normalized
    from {{ ref('forecasting_case_features_combined') }} as case_features
    where
        ds = {{ ds() }}
        and (case_features.is_num_phases_match_num_cases is not false)
        and (case_features.case_has_experienced_reordering is not true)
        and case_features.is_emergent_case_class_type = 0
        and case_features.patient_class != 'EMERGENCY'
        and case_features.day_of_week not in ('Saturday', 'Sunday')
        and case_features.room_id not in (
            'HMH-OPC18-OR15', 'HMH-OPC18-OR17', 'NBH-NBMC02-ENDO02'
        )
),

duration_outliers as (
    select
        filtered_forecasting_data.first_primary_surgeon,
        filtered_forecasting_data.first_primary_procedure,
        filtered_forecasting_data.procedure_count,
        approx_quantiles(filtered_forecasting_data.actual_duration, 100)[
            offset(98)
        ] as actual_duration_outliers
    from
        filtered_forecasting_data
    group by
        filtered_forecasting_data.first_primary_surgeon,
        filtered_forecasting_data.first_primary_procedure,
        filtered_forecasting_data.procedure_count
)

select
    {{ ds() }} as ds,
    filtered_forecasting_data.first_primary_surgeon,
    filtered_forecasting_data.first_primary_procedure,
    filtered_forecasting_data.procedure_count,
    filtered_forecasting_data.surgeon_proc_combo_num,
    count(filtered_forecasting_data.surgeon_proc_combo_num) as combo_count,
    avg(filtered_forecasting_data.actual_duration) as actual_duration_mean,
    avg(filtered_forecasting_data.end_offset) as end_offset_mean,
    avg(filtered_forecasting_data.schedule_actual_diff_normalized)
        as schedule_actual_diff_normalized_mean,
    variance(filtered_forecasting_data.actual_duration) as actual_duration_var,
    variance(filtered_forecasting_data.end_offset) as end_offset_var,
    variance(filtered_forecasting_data.schedule_actual_diff_normalized)
        as schedule_actual_diff_normalized_var
from filtered_forecasting_data
left outer join duration_outliers
    on
        filtered_forecasting_data.first_primary_surgeon
        = duration_outliers.first_primary_surgeon
        and filtered_forecasting_data.first_primary_procedure
        = duration_outliers.first_primary_procedure
        and filtered_forecasting_data.procedure_count
        = duration_outliers.procedure_count
where
    filtered_forecasting_data.actual_duration
    < duration_outliers.actual_duration_outliers
group by
    filtered_forecasting_data.surgeon_proc_combo_num,
    filtered_forecasting_data.first_primary_surgeon,
    filtered_forecasting_data.first_primary_procedure,
    filtered_forecasting_data.procedure_count
having combo_count >= 2
