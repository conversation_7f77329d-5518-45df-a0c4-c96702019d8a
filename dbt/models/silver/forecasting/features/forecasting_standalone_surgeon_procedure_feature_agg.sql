{{ daily_config(produce_latest_snapshot=True) }}

with filtered_forecasting_data as (
    select
        case_features.site_id,
        case_features.org_id,
        case_features.first_primary_surgeon,
        case_features.first_primary_procedure,
        case_features.procedure_count,
        case_features.actual_duration,
        case_features.end_offset,
        case_features.schedule_actual_diff_normalized
    from {{ ref('forecasting_case_features_combined') }} as case_features
    where
        ds = {{ ds() }}
        and (case_features.is_num_phases_match_num_cases is not false)
        and (case_features.case_has_experienced_reordering is not true)
        and case_features.is_emergent_case_class_type = 0
        and case_features.patient_class != "EMERGENCY"
        and case_features.day_of_week not in ("Saturday", "Sunday")
),

reporting_dimensions as (
    select distinct
        org_id,
        site_id,
        first_primary_surgeon,
        first_primary_procedure
    from filtered_forecasting_data
),

-- Calculation and filtering of "outliers" matches the ml-services training 
-- pipeline for the standalone case duration. In ml-services, any case lasting 
-- longer than the 85th percentile within the combo (first_primary_surgeon, 
-- first_primary_procedure, procedure_count) is considered "complex". Then, 
-- complex cases are filtered out before calculating aggregate features on the 
-- training data. Note that to calculate "complex cases", we are aggregating by 
-- the combo (first_primary_surgeon, first_primary_procedure, procedure_count), 
-- but changing the aggregation to surgeon and procedure to calculate aggregate 
-- features in order to match what's available at inference.
duration_outliers as (
    select
        filtered_forecasting_data.org_id,
        filtered_forecasting_data.first_primary_surgeon,
        filtered_forecasting_data.first_primary_procedure,
        filtered_forecasting_data.procedure_count,
        approx_quantiles(filtered_forecasting_data.actual_duration, 100)[
            offset(85)
        ] as actual_duration_outliers
    from
        filtered_forecasting_data
    group by
        filtered_forecasting_data.org_id,
        filtered_forecasting_data.first_primary_surgeon,
        filtered_forecasting_data.first_primary_procedure,
        filtered_forecasting_data.procedure_count
),

agg_cleaned as (
    select
        filtered_forecasting_data.org_id,
        filtered_forecasting_data.first_primary_procedure,
        filtered_forecasting_data.first_primary_surgeon,
        count(filtered_forecasting_data.first_primary_procedure) as case_count,
        avg(filtered_forecasting_data.actual_duration) as actual_duration_mean,
        avg(filtered_forecasting_data.end_offset) as end_offset_mean,
        avg(filtered_forecasting_data.schedule_actual_diff_normalized)
            as schedule_actual_diff_normalized_mean,
        variance(filtered_forecasting_data.actual_duration) as actual_duration_var,
        variance(filtered_forecasting_data.end_offset) as end_offset_var,
        variance(filtered_forecasting_data.schedule_actual_diff_normalized)
            as schedule_actual_diff_normalized_var
    from filtered_forecasting_data
    left outer join duration_outliers
        on
            filtered_forecasting_data.org_id = duration_outliers.org_id
            and
            filtered_forecasting_data.first_primary_surgeon
            = duration_outliers.first_primary_surgeon
            and
            filtered_forecasting_data.first_primary_procedure
            = duration_outliers.first_primary_procedure
            and
            filtered_forecasting_data.procedure_count
            = duration_outliers.procedure_count
    where
        filtered_forecasting_data.actual_duration
        < duration_outliers.actual_duration_outliers
    group by
        filtered_forecasting_data.org_id,
        filtered_forecasting_data.first_primary_procedure,
        filtered_forecasting_data.first_primary_surgeon
    having case_count >= 2
)

select
    {{ ds() }} as ds,
    reporting_dimensions.org_id,
    reporting_dimensions.site_id,
    agg_cleaned.first_primary_surgeon,
    agg_cleaned.first_primary_procedure,
    agg_cleaned.case_count,
    agg_cleaned.actual_duration_mean,
    agg_cleaned.end_offset_mean,
    agg_cleaned.schedule_actual_diff_normalized_mean,
    agg_cleaned.actual_duration_var,
    agg_cleaned.end_offset_var,
    agg_cleaned.schedule_actual_diff_normalized_var
from
    reporting_dimensions
inner join agg_cleaned
    on
        reporting_dimensions.org_id = agg_cleaned.org_id
        and reporting_dimensions.first_primary_surgeon = agg_cleaned.first_primary_surgeon
        and reporting_dimensions.first_primary_procedure = agg_cleaned.first_primary_procedure
