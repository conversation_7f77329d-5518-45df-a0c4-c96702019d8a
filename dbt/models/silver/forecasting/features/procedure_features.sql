{{ daily_config() }}

{%
    set procedure_name_sql = "
        regexp_replace(
            trim(
                regexp_replace(
                    upper(
                        split(procedure_name, '[')[offset(0)]
                    ),
                    r'(RIGHT|RIGHT |, RIGHT| RIGHT|, RIGHT|LEFT|LEFT |, LEFT| LEFT|LEFT|,LEFT|-|INDEX)', -- noqa: LT05
                    ' '
                )
            ),
            r'(\\s+$)|,$|\\.$',
            ''
        )
    "
%}

with normalized_deduped_procedures as (
    select
        case_procedures.case_id as case_id,
        {{ procedure_name_sql }} as procedure_name,
        min(case_procedures.hierarchy) as hierarchy,
        min(case_procedures.procedure_created_at) as procedure_created_at
    from {{ ref("case_procedures") }} as case_procedures
    where ds = {{ ds() }}
    group by 1, 2
),


ordered_procedures as (
    select
        case_id,
        procedure_name,
        hierarchy,
        procedure_created_at
    from normalized_deduped_procedures
    -- We order by procedure created_time because
    -- not all cases have procedures with a hierarchy and ideally,
    -- in lieu of hierarchy, procedures are ordered by the time they
    -- are added to the case.
    --In lieu procedure ordering missing upstream,
    --we are ordering by when the procedure was first created
    order by
        1 asc,
        3 asc,
        4 asc
),

all_procedures as (
    select
        case_id,
        -- already ordered by procedure_created_at in previous CTE
        array_agg(procedure_name) as procedures_list
    from ordered_procedures
    group by 1
),

primary_procedures as (
    select
        case_id,
        hierarchy,
        {{ procedure_name_sql }} as primary_procedure
    from {{ ref("case_procedures") }}
    where
        ds = {{ ds() }}
        and is_primary_procedure
)

select
    {{ ds() }} as ds,
    all_procedures.case_id as case_id,
    all_procedures.procedures_list,
    if(
        primary_procedures.hierarchy is null
        and array_length(all_procedures.procedures_list) > 0,
        all_procedures.procedures_list[safe_offset(0)],
        primary_procedures.primary_procedure
    ) as first_primary_procedure
from all_procedures
left outer join primary_procedures
    on all_procedures.case_id = primary_procedures.case_id
