-- TODO(DATA-2015): Expiration removed to support ROI studies while RO<PERSON> doesn't own pipeline.
{{ daily_config(expiration_days=None, produce_latest_snapshot=True) }}


with unified as (
    {% if not for_feature_fetch() %}
        select
            historical_case_features.case_id,
            historical_case_features.org_id,
            historical_case_features.date_of_surgery,
            historical_case_features.scheduled_start_datetime_local,
            timestamp_add(
                historical_case_features.scheduled_start_datetime_local,
                interval historical_case_features.scheduled_duration minute
            ) as scheduled_end_datetime_local,
            historical_case_features.actual_start_datetime_local,
            historical_case_features.actual_end_datetime_local,
            null as start_event_id,
            null as end_event_id,
            historical_case_features.site_id,
            historical_case_features.room_id,
            historical_case_features.month_of_surgery,
            historical_case_features.scheduled_starting_hour,
            historical_case_features.day_of_week,
            historical_case_features.case_classification_types_id,
            case
                when
                    historical_case_features.patient_class = 'outpatient'
                    then 'HOSPITAL_OUTPATIENT_SURGERY'
                when historical_case_features.patient_class is null
                    then 'UNSPECIFIED'
                else upper(historical_case_features.patient_class)
            end as patient_class,
            historical_case_features.is_to_follow_case,
            historical_case_features.is_first_case,
            historical_case_features.is_last_case,
            historical_case_features.is_running_during_lunch,
            historical_case_features.scheduled_duration,
            historical_case_features.actual_duration,
            historical_case_features.start_offset,
            historical_case_features.end_offset,
            historical_case_features.surgeon_count as surgeon_count,
            historical_case_features.first_primary_surgeon,
            historical_case_features.first_primary_surgeon_id,
            historical_case_features.primary_procedure_name as first_primary_procedure,
            historical_case_features.primary_procedure_cpt
                as first_primary_cpt,
            historical_case_features.procedure_count,
            -- invalid data will be filtered out at the end of this SQL
            -- by eliminating apella_data is null
            case
                when historical_case_features.is_valid_data
                    then
                        cast(historical_case_features.is_apella_data as tinyint)
                when not historical_case_features.is_valid_data
                    then null
            end as apella_data,
            historical_case_features.is_add_on,
            historical_case_features.prev_flip_case_id,
            historical_case_features.is_flip_room,
            if(
                historical_case_features.all_procedure_names is not null
                and array_length(historical_case_features.all_procedure_names) > 0,
                historical_case_features.all_procedure_names,
                [historical_case_features.primary_procedure_name]
            )
                as case_procedure_list
        from {{ ref("historical_case_features") }} as historical_case_features
        where historical_case_features.ds = {{ ds() }}

        union all
    {% endif %}

    select
        case_features.case_id,
        case_features.org_id,
        date(case_features.scheduled_start_datetime_local) as date_of_surgery,
        case_features.scheduled_start_datetime_local,
        case_features.scheduled_end_datetime_local,
        case_features.actual_start_datetime_local,
        case_features.actual_end_datetime_local,
        case_features.start_event_id,
        case_features.end_event_id,
        case_features.site_id,
        case_features.room_id,
        case_features.month_of_surgery,
        case_features.scheduled_starting_hour,
        case_features.day_of_week,
        case_features.case_classification_types_id,
        case_features.patient_class,
        case_features.is_to_follow_case,
        case_features.is_first_case,
        case_features.is_last_case,
        case_features.is_running_during_lunch,
        case_features.scheduled_duration,
        case_features.actual_duration,
        case_features.start_offset,
        case_features.end_offset,
        array_length(case_features.surgeon_list) as surgeon_count,
        first_primary_surgeon.last_name
        || ', '
        || first_primary_surgeon.first_name as first_primary_surgeon,
        case_features.first_primary_surgeon_id,
        case_features.first_primary_procedure,
        null as first_primary_cpt,
        array_length(case_features.procedures_list) as procedure_count,
        cast(case_features.is_apella_data as tinyint) as apella_data,
        case_features.is_add_on,
        case_features.prev_flip_case_id,
        case_features.is_flip_room,
        case_features.procedures_list as case_procedure_list
    from {{ ref("case_features") }} as case_features
    where
        case_features.ds = {{ ds() }}
        and case_features.is_apella_data
        -- as of Feb 2024, almost all of northbay's cases are initially scheduled with patient_class = 'PRE_ADMIT'
        -- eliminate this filter so inference in realtime-dags will pick them up
        and (
            (
                case_features.org_id != 'north_bay'
                and case_features.patient_class != 'PRE_ADMIT'
            )
            or (
                case_features.org_id != 'lifebridge'
                and case_features.patient_class != 'PRE_ADMIT'
            )
            or case_features.org_id in ('north_bay', 'lifebridge')
        )
),

surgeon_count_agg as (
    select
        first_primary_surgeon
        || '_'
        || first_primary_procedure
        || '_'
        || procedure_count as combo,
        round(avg(surgeon_count)) as surgeon_count_combo_avg
    from unified
    where
        apella_data = 1
        and surgeon_count is not null
    group by 1
),

first_case_counts as (
    select
        scheduled_start_datetime_local,
        site_id,
        date_of_surgery,
        count(distinct case_id) as number_first_cases_started_at_same_time
    from unified
    where
        cast(unified.is_first_case as tinyint) = 1
    group by 1, 2, 3
)

select
    {{ ds() }} as ds,
    unified.case_id,
    unified.org_id,
    unified.date_of_surgery,
    date_diff(unified.date_of_surgery, '2022-01-01', day)
        as days_since_jan1_2022,
    unified.scheduled_start_datetime_local,
    unified.scheduled_end_datetime_local,
    unified.actual_start_datetime_local,
    unified.actual_end_datetime_local,
    unified.start_event_id,
    unified.end_event_id,
    unified.site_id,
    unified.room_id,
    -- TODO: remove `room`. Keeping for now for downstream services. Here, room
    -- is actually room_id. Not to confuse with some historical tables,
    -- where room isn't the room id
    unified.room_id as room,
    unified.first_primary_procedure,
    unified.first_primary_cpt as first_primary_cpt,
    unified.month_of_surgery,
    unified.scheduled_starting_hour,
    unified.day_of_week,
    case
        when
            unified.case_classification_types_id is not null
            and unified.case_classification_types_id != 'CASE_CLASSIFICATION_UNKNOWN'
            then
                case
                    when unified.case_classification_types_id != 'CASE_CLASSIFICATION_EXPEDITED'
                        then
                            split(unified.case_classification_types_id, '_')[safe_offset(2)]
                    else
                        'URGENT'
                end
        else
            'UNSPECIFIED'
    end as case_type_short,
    -- at tampa_general, LEVEL1 corresponds to Emergent
    case
        when
            unified.case_classification_types_id in (
                'CASE_CLASSIFICATION_EMERGENT',
                'CASE_CLASSIFICATION_LEVEL1'
            )
            then 1
        else 0
    end as is_emergent_case_class_type,
    unified.patient_class as patient_class,
    if(
        (unified.patient_class = 'HOSPITAL_OUTPATIENT_SURGERY')
        or
        (unified.patient_class = 'UNSPECIFIED'),
        1,
        0
    ) as outpatient,
    cast(coalesce(unified.is_to_follow_case, false) as tinyint)
        as to_follow_case,
    cast(unified.is_first_case as tinyint) as first_case,
    cast(unified.is_last_case as tinyint) as last_case,
    cast(unified.is_running_during_lunch as tinyint) as running_during_lunch,
    unified.scheduled_duration,
    unified.actual_duration,
    unified.start_offset,
    unified.end_offset,
    (unified.actual_duration - unified.scheduled_duration)
    / unified.scheduled_duration as schedule_actual_diff_normalized,
    case
        when
            unified.surgeon_count is not null
            then unified.surgeon_count
        when
            unified.surgeon_count is null
            and unified.first_primary_surgeon
            || '_'
            || unified.first_primary_procedure
            || '_'
            || unified.procedure_count in (
                select surgeon_count_agg.combo
                from surgeon_count_agg
            )
            then surgeon_count_agg.surgeon_count_combo_avg
        else
            (
                select
                    cast(
                        round(
                            avg(
                                surgeon_count
                            )
                        )
                        as int
                    )
                from unified
                where
                    apella_data = 1
                    and surgeon_count is not null
            )
    end as surgeon_count,
    unified.first_primary_surgeon,
    unified.first_primary_surgeon_id,
    unified.first_primary_surgeon
    || '_'
    || unified.first_primary_procedure
    || '_'
    || unified.procedure_count as surgeon_proc_combo_num,
    cast(unified.procedure_count as int64) as procedure_count,
    count(distinct unified.scheduled_start_datetime_local) over (
        partition by
            unified.site_id, unified.room_id, unified.date_of_surgery
    ) as num_scheduled_cases,
    count(distinct unified.first_primary_procedure) over (
        partition by
            unified.site_id, unified.room_id, unified.date_of_surgery
    ) as number_of_or_day_different_procedures,
    count(distinct unified.first_primary_surgeon) over (
        partition by
            unified.site_id, unified.room_id, unified.date_of_surgery
    ) as number_of_or_day_different_surgeons,
    sum(unified.scheduled_duration) over (
        partition by
            unified.site_id, unified.room_id, unified.date_of_surgery
    ) as sum_or_day_scheduled_minutes,
    unified.apella_data,
    if(
        unified.is_add_on = true,
        1,
        0
    ) as add_on,
    unified.prev_flip_case_id,
    unified.is_flip_room,
    if(
        unified.site_id in ('HMH-OPC19')
        or
        unified.room_id in (
            'HMH-OPC18-OR17',
            'HMH-OPC18-OR16',
            'HMH-OPC18-OR14',
            'HMH-OPC18-OR15'
        ),
        'HMH-OPC19',
        unified.site_id)
        as business_unit,
    if(
        cast(unified.is_first_case as tinyint) = 0,
        timestamp_diff(
            unified.scheduled_start_datetime_local,
            lag(unified.scheduled_end_datetime_local, 1) over (
                partition by
                    unified.site_id,
                    unified.room_id,
                    date(unified.scheduled_start_datetime_local)
                order by unified.scheduled_start_datetime_local
            ), minute
        ),
        null
    ) as minutes_after_previous_case_scheduled_end,
    sum(
        unified.scheduled_duration)
        over
        (
            partition by
                unified.site_id,
                unified.room_id,
                date(unified.scheduled_start_datetime_local)
            order by unified.scheduled_start_datetime_local
        )
    - unified.scheduled_duration
        as cumsum_scheduled_case_duration_so_far,
    first_case_counts.number_first_cases_started_at_same_time,
    unified.case_procedure_list,
    {% if not for_feature_fetch() %}
        case_number_match.is_num_cases_match
            as is_num_phases_match_num_cases,
        api_reordered_cases.this_or_prev_case_was_reordered
            as api_date_range_this_or_prev_case_was_reordered,
        historical_reordered.start_offsets_mismatched_in_or_day
            as historical_date_range_or_day_had_reordering,
        coalesce(
            historical_reordered.start_offsets_mismatched_in_or_day,
            api_reordered_cases.this_or_prev_case_was_reordered
        ) as case_has_experienced_reordering
    {% endif %}
from unified as unified
left outer join surgeon_count_agg
    on
        unified.first_primary_surgeon
        || '_'
        || unified.first_primary_procedure
        || '_'
        || unified.procedure_count = surgeon_count_agg.combo
left outer join {{ api_table_snapshot("bronze", "public_sites", "sites") }}
    on unified.site_id = sites.id
left outer join first_case_counts
    on
        unified.site_id = first_case_counts.site_id
        and unified.date_of_surgery
        = first_case_counts.date_of_surgery
        and unified.scheduled_start_datetime_local
        = first_case_counts.scheduled_start_datetime_local
{% if not for_feature_fetch() %}
    left outer join {{ ref('reordered_cases') }} as api_reordered_cases
        on
            unified.case_id = api_reordered_cases.case_id
            and api_reordered_cases.ds = {{ ds() }}
    left outer join {{ ref('case_number_match_lookup') }} as case_number_match
        on
            sites.id = case_number_match.site_id
            and unified.room_id = case_number_match.room_id
            and unified.date_of_surgery = case_number_match.case_date
            and case_number_match.ds = {{ ds() }}
    left outer join
        {{ ref('historical_reordered_cases') }} as historical_reordered
        on
            unified.site_id = historical_reordered.site_id
            and unified.room_id = historical_reordered.room_id
            and unified.date_of_surgery = historical_reordered.case_date
            and historical_reordered.ds = {{ ds() }}
{% endif %}
where
    unified.apella_data is not null
    {% if not for_feature_fetch() %}
    -- Filter out cases that hasn't completed. This is known to happen for some of the last cases
    -- of the day, where the actual end time is not available yet at the time the table is created
        and unified.actual_duration is not null
    {% endif %}
