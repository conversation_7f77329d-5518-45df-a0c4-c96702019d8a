{{ daily_config() }}

with next_cases as (
    select
        ds as next_ds,
        created_at as next_case_created_at,
        case_id as next_case_id,
        room_id as next_room_id,
        site_id as next_site_id,
        scheduled_start_datetime_local as next_start,
        scheduled_end_datetime_local as next_end
    from {{ ref("cases") }}
    where ds = {{ ds() }}
),

cases_to_follow_cases as (
    select
        cases.ds,
        cases.case_id,
        cases.start_event_id,
        cases.end_event_id,
        cases.created_at,
        cases.org_id,
        cases.site_id,
        cases.room_id,
        cases.is_add_on,
        cases.case_classification_types_id,
        cases.patient_class,
        cases.is_first_case,
        cases.is_last_case,
        cases.scheduled_start_datetime_local,
        cases.scheduled_end_datetime_local,
        cases.actual_start_datetime_local,
        cases.actual_end_datetime_local,
        next_cases.next_case_created_at,
        next_cases.next_case_id,
        next_cases.next_room_id,
        next_cases.next_site_id,
        next_cases.next_start,
        next_cases.next_end,
        cases.is_apella_data,
        cases.is_valid_data,
        lag(cases.case_id) over (
            partition by
                cases.site_id,
                cases.room_id,
                date(cases.scheduled_start_datetime_local)
            order by cases.scheduled_start_datetime_local
        ) as prev_case_id,
        if(
            not cases.is_first_case,
            timestamp_diff(
                cases.scheduled_start_datetime_local,
                lag(cases.scheduled_end_datetime_local, 1) over (
                    partition by
                        cases.site_id,
                        cases.room_id,
                        date(cases.scheduled_start_datetime_local)
                    order by cases.scheduled_start_datetime_local
                ), minute
            ),
            null
        ) as preceding_scheduled_turnover_minutes,
        row_number() over (
            partition by cases.case_id, cases.room_id, cases.site_id, cases.org_id
            order by next_cases.next_start
        ) as next_case_rank
    from {{ ref("cases") }} as cases
    left outer join next_cases
        on
            next_cases.next_start >= timestamp_sub(
                cases.scheduled_end_datetime_local,
                interval 5 minute
            )
            and next_cases.next_start <= timestamp_add(
                cases.scheduled_end_datetime_local,
                interval 5 minute
            )
            and cases.room_id = next_cases.next_room_id
            and cases.site_id = next_cases.next_site_id
    where (
        next_cases.next_case_id is null
        or cases.case_id != next_cases.next_case_id
    )
    and cases.ds = {{ ds() }}
)

select
    {{ ds() }} as ds,
    cases.case_id,
    cases.start_event_id,
    cases.end_event_id,
    cases.org_id,
    cases.site_id,
    cases.room_id,
    cases.scheduled_start_datetime_local,
    cases.scheduled_end_datetime_local,
    cases.actual_start_datetime_local,
    cases.actual_end_datetime_local,
    extract(
        month from cases.scheduled_start_datetime_local
    ) as month_of_surgery,
    extract(
        hour from cases.scheduled_start_datetime_local
    ) as scheduled_starting_hour,
    format_date('%A', cases.scheduled_start_datetime_local) as day_of_week,
    cases.next_case_id is not null or cases.case_id in (
        select next_case_id from cases_to_follow_cases
    ) as is_to_follow_case,
    cases.next_case_rank,
    cases.case_classification_types_id,
    cases.patient_class,
    cases.is_first_case,
    cases.is_last_case,
    cast(round(
        timestamp_diff(
            cases.scheduled_end_datetime_local,
            cases.scheduled_start_datetime_local,
            second
        ) / 60.0
    ) as int64) as scheduled_duration,
    cast(round(
        timestamp_diff(
            cases.actual_end_datetime_local,
            cases.actual_start_datetime_local,
            second
        ) / 60.0
    ) as int64) as actual_duration,
    cast(round(
        timestamp_diff(
            cases.actual_start_datetime_local,
            cases.scheduled_start_datetime_local,
            second
        ) / 60.0
    ) as int64) as start_offset,
    cast(round(
        timestamp_diff(
            cases.actual_end_datetime_local,
            cases.scheduled_end_datetime_local,
            second
        ) / 60.0
    ) as int64) as end_offset,
    cast(cases.scheduled_start_datetime_local as time) < '13:00:00'
    and cast(cases.scheduled_end_datetime_local as time) > '12:00:00'

        as is_running_during_lunch,
    case_surgeons.first_primary_surgeon,
    case_surgeons.first_primary_surgeon_id,
    case_surgeons.surgeon_list,
    case_procedures.first_primary_procedure,
    case_procedures.procedures_list,
    cases.is_add_on,
    cases.prev_case_id,
    cases.preceding_scheduled_turnover_minutes,
    flip_room_lookup.prev_flip_case_id,
    coalesce(flip_room_lookup.is_flip_room, false) as is_flip_room,
    cases.is_apella_data,
    cases.is_valid_data
from cases_to_follow_cases as cases
inner join {{ ref("procedure_features") }} as case_procedures
    on
        cases.case_id = case_procedures.case_id
        and case_procedures.procedures_list is not null
        and case_procedures.ds = {{ ds() }}
inner join {{ ref("surgeon_features") }} as case_surgeons
    on
        case_procedures.case_id = case_surgeons.case_id
        and case_surgeons.surgeon_list is not null
        and case_surgeons.ds = {{ ds() }}
left outer join {{ ref('flip_room_case_lookup') }} as flip_room_lookup
    on
        cases.case_id = flip_room_lookup.case_id
        and flip_room_lookup.ds = {{ ds() }}
where
    cases.case_id in (

        select case_id
        from cases_to_follow_cases
        group by case_id
        having count(case_id) = 1

    ) and cases.next_case_rank = 1
