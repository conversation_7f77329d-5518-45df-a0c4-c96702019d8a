{{ daily_config() }}

{%
    set surgeon_struct_sql = "struct(surgeon_last_name as last_name, surgeon_first_name as first_name, surgeon_role as surgeon_role)"
%}

{%
    set surgeon_list_sql = "array_agg(surgeon_struct order by surgeon_struct.last_name, surgeon_struct.first_name)"
%}

with distinct_surgeons as (
    select
        case_id as case_id,
        staff_id,
        surgeon_last_name,
        surgeon_first_name,
        surgeon_role,
        is_primary_surgeon,
        any_value({{ surgeon_struct_sql }}) as surgeon_struct,
        min(staff_created_at) as staff_created_at
    from {{ ref("case_surgeons") }}
    where ds = {{ ds() }}
    group by 1, 2, 3, 4, 5, 6
),

surgeon_pre_features as (
    select
        case_id,
        {{ surgeon_list_sql }} as surgeon_list,
        coalesce(
            array_agg(
                if(is_primary_surgeon, surgeon_struct, null)
                ignore nulls
                order by staff_created_at asc
            )[safe_offset(0)],
            array_agg(surgeon_struct order by staff_created_at)[safe_offset(0)]
        ) as first_primary_surgeon
    from distinct_surgeons
    group by 1
)

select
    {{ ds () }} as ds,
    surgeon_pre_features.case_id,
    surgeon_pre_features.surgeon_list,
    surgeon_pre_features.first_primary_surgeon,
    distinct_surgeons.staff_id as first_primary_surgeon_id
from surgeon_pre_features
inner join distinct_surgeons
    on
        surgeon_pre_features.case_id = distinct_surgeons.case_id
        and surgeon_pre_features.first_primary_surgeon.last_name = distinct_surgeons.surgeon_last_name
        and surgeon_pre_features.first_primary_surgeon.first_name = distinct_surgeons.surgeon_first_name
        and surgeon_pre_features.first_primary_surgeon.surgeon_role = distinct_surgeons.surgeon_role
