{{ daily_config() }}

with historical_cases_standardized_ids as (
    select
        cases.ds,
        cases.org_id,
        cases.file_case_id,
        cases.scheduled_start_datetime_local,
        cases.scheduled_end_datetime_local,
        cases.actual_start_datetime_local,
        cases.actual_end_datetime_local,
        cases.case_classification_types_id,
        cases.patient_class,
        cases.is_add_on,
        cases.primary_surgeon,
        cases.apella_staff_id as primary_surgeon_id,
        cases.primary_procedure_name,
        cases.procedure_count,
        cases.source_file_date_utc,
        cases.source_file_gcs_path,
        cases.full_procedure_name,
        cases.room_id,
        cases.site_id,
        cases.all_procedure_names
    from {{ ref("forecasting_historical_cases") }} as cases
    where ds = {{ ds() }}
),

next_cases as (
    select
        ds as next_ds,
        file_case_id as next_case_id,
        room_id as next_room_id,
        scheduled_start_datetime_local as next_start,
        scheduled_end_datetime_local as next_end,
        site_id,
        org_id
    from historical_cases_standardized_ids
),

cases_to_follow_cases as (
    select
        cases.ds,
        cases.org_id,
        cases.file_case_id,
        next_cases.next_case_id,
        cases.site_id,
        cases.room_id,
        next_cases.next_room_id,
        cases.scheduled_start_datetime_local,
        next_cases.next_start,
        cases.scheduled_end_datetime_local,
        next_cases.next_end,
        cases.actual_start_datetime_local,
        cases.actual_end_datetime_local,
        cases.case_classification_types_id,
        cases.patient_class,
        cases.is_add_on,
        cases.primary_surgeon,
        cases.primary_surgeon_id,
        cases.primary_procedure_name,
        cases.procedure_count,
        flip_room_lookup.prev_flip_case_id,
        cases.source_file_date_utc,
        cases.source_file_gcs_path,
        cases.all_procedure_names,
        regexp_extract(cases.full_procedure_name, r'\b\d{5}\b')
            as primary_procedure_cpt,
        coalesce(flip_room_lookup.is_flip_room, false) as is_flip_room,
        row_number() over (
            partition by cases.file_case_id, cases.room_id, cases.site_id, cases.org_id
            order by next_cases.next_start
        ) as next_case_rank
    from historical_cases_standardized_ids as cases
    left outer join next_cases
        on
            next_cases.next_start >= timestamp_sub(
                cases.scheduled_end_datetime_local,
                interval 5 minute
            )
            and next_cases.next_start <= timestamp_add(
                cases.scheduled_end_datetime_local,
                interval 5 minute
            )
            and cases.room_id = next_cases.next_room_id
            and cases.site_id = next_cases.site_id
            and cases.org_id = next_cases.org_id
    left outer join {{ ref('flip_room_case_lookup') }} as flip_room_lookup
        on
            cases.file_case_id = flip_room_lookup.case_id
            and flip_room_lookup.ds = {{ ds() }}
    where cases.ds = {{ ds () }}
)

{% set sites_config = forecasting_sites_config() %}
{% set config_sites = sites_config.site_ids.keys() %}

select
    {{ ds() }} as ds,
    historical.file_case_id as case_id,
    date(historical.scheduled_start_datetime_local) as date_of_surgery,
    historical.scheduled_start_datetime_local,
    historical.actual_start_datetime_local,
    historical.actual_end_datetime_local,
    historical.org_id,
    historical.site_id,
    historical.room_id,
    extract(
        month from historical.scheduled_start_datetime_local
    ) as month_of_surgery,
    extract(
        hour from historical.scheduled_start_datetime_local
    ) as scheduled_starting_hour,
    format_date('%A', historical.scheduled_start_datetime_local) as day_of_week,
    historical.next_case_id is not null or historical.file_case_id in (
        select next_case_id from cases_to_follow_cases
    ) as is_to_follow_case,
    historical.next_case_rank,
    row_number() over (
        partition by
            historical.org_id,
            historical.room_id,
            extract(
                date from historical.scheduled_start_datetime_local
            )
        order by historical.scheduled_start_datetime_local
    ) = 1 as is_first_case,
    row_number() over (
        partition by
            historical.org_id,
            historical.room_id,
            extract(
                date from historical.scheduled_start_datetime_local
            )
        order by historical.scheduled_start_datetime_local desc
    ) = 1 as is_last_case,
    historical.case_classification_types_id,
    historical.patient_class,
    cast(round(
        timestamp_diff(
            historical.scheduled_end_datetime_local,
            historical.scheduled_start_datetime_local,
            second
        ) / 60.0
    ) as int64) as scheduled_duration,
    cast(round(
        timestamp_diff(
            historical.actual_end_datetime_local,
            historical.actual_start_datetime_local,
            second
        ) / 60.0
    ) as int64) as actual_duration,
    cast(round(
        timestamp_diff(
            historical.actual_start_datetime_local,
            historical.scheduled_start_datetime_local,
            second
        ) / 60.0
    ) as int64) as start_offset,
    cast(round(
        timestamp_diff(
            historical.actual_end_datetime_local,
            historical.scheduled_end_datetime_local,
            second
        ) / 60.0
    ) as int64) as end_offset,
    cast(historical.scheduled_start_datetime_local as time) < '13:00:00'
    and cast(historical.scheduled_end_datetime_local as time) > '12:00:00'
        as is_running_during_lunch,
    upper(primary_surgeon.last_name || ', ' || primary_surgeon.first_name)
        as first_primary_surgeon,
    historical.primary_surgeon_id as first_primary_surgeon_id,
    case when
        primary_surgeon.first_name is not null
        then null
    else 0 end as surgeon_count,
    upper(historical.primary_procedure_name) as primary_procedure_name,
    historical.primary_procedure_cpt,
    coalesce(procedure_count, 1) as procedure_count,
    historical.is_add_on,
    historical.prev_flip_case_id,
    historical.is_flip_room,
    historical.all_procedure_names,
    false as is_apella_data,
    case
        {% for site_id in config_sites %}
            {% if site_id in ['HF-VH02'] %}
                when
                    historical.site_id = '{{ site_id }}'
                    and date(
                        historical.scheduled_start_datetime_local
                    )
                    < extract(
                        date from parse_timestamp(
                            '%Y-%m-%d',
                            '{{ sites_config.site_ids[site_id].valid_start_date }}'
                        )
                    )
                    then false
            {% endif %}
        {% endfor %}
        else true
    end as is_valid_data,
    source_file_gcs_path
from cases_to_follow_cases as historical where historical.next_case_rank = 1
