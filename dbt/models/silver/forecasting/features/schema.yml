version: 3

models:
  - name: case_features
    columns:
      - name: ds
        data_type: "timestamp"
      - name: case_id
        data_type: "string"
      - name: org_id
        data_type: "string"
      - name: start_event_id
        data_type: "string"
      - name: end_event_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: service_line
        data_type: "string"
      - name: scheduled_start_datetime_local
        data_type: "datetime"
      - name: scheduled_end_datetime_local
        data_type: "datetime"
      - name: actual_start_datetime_local
        data_type: "datetime"
      - name: actual_end_datetime_local
        data_type: "datetime"
      - name: month_of_surgery
        data_type: "int"
      - name: scheduled_starting_hour
        data_type: "int"
      - name: day_of_week
        data_type: "int"
      - name: is_emergent_case_class_type
        data_type: "tinyint"
        description: "Emergent case types include EMERGENT, EMERGENCY, and LEVEL1 (as of Aug 2024, LEVELs are used only at tampa_general)"
      - name: is_to_follow_case
        data_type: "boolean"
      - name: case_classification_types_id
        data_type: "string"
      - name: patient_class
        data_type: "string"
      - name: is_first_case
        data_type: "boolean"
      - name: is_last_case
        data_type: "boolean"
      - name: scheduled_duration
        data_type: "bigint"
      - name: actual_duration
        data_type: "bigint"
      - name: start_offset
        data_type: "bigint"
      - name: end_offset
        data_type: "bigint"
      - name: is_running_during_lunch
        data_type: "boolean"
      - name: first_primary_surgeon
        data_type: "string"
      - name: surgeon_list
        data_type: "array<struct<string, string>>"
      - name: surgeon_count
        data_type: "int"
      - name: first_primary_procedure
        data_type: "string"
      - name: procedures_list
        data_type: "array<struct<string, string>>"
      - name: is_apella_data
        data_type: "boolean"
      - name: prev_flip_case_id
        data_type: "string"
        description: "The case_id of the most recent case that the first primary surgeon has in another room. Definition is taken from flip_room_case_lookup_table."
      - name: is_flip_room
        data_type: "boolean"
        description: "True when the first primary surgeon has at least one case in another room on the same day. Definition is taken from flip_room_case_lookup_table."
      - name: is_apella_data
        data_type: "boolean"
      - name: is_valid_data
        data_type: "boolean"

  - name: procedure_features
    columns:
      - name: ds
        data_type: "timestamp"
      - name: case_id
        data_type: "string"
      - name: procedures_list
        data_type: "array<string>"
      - name: first_primary_procedure
        data_type: "string"

  - name: surgeon_features
    columns:
      - name: ds
        data_type: "timestamp"
      - name: case_id
        data_type: "string"
      - name: surgeon_list
        data_type: "array<struct<string, string, string>>"
      - name: first_primary_surgeon
        data_type: "struct<string, string, string>"
      - name: first_primary_surgeon_id
        data_type: "string"

  - name: historical_case_features
    columns:
      - name: ds
        data_type: "timestamp"
      - name: case_id
        data_type: "string"
      - name: org_id
        data_type: "string"
      - name: date_of_surgery
        data_type: "date"
      - name: scheduled_start_datetime_local
        data_type: 'datetime'
      - name: actual_start_datetime_local
        data_type: 'datetime'
      - name: actual_end_datetime_local
        data_type: 'datetime'
      - name: site_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: month_of_surgery
        data_type: "int"
      - name: scheduled_starting_hour
        data_type: "int"
      - name: day_of_week
        data_type: "int"
      - name: is_to_follow_case
        data_type: "boolean"
      - name: case_class
        data_type: "string"
      - name: patient_class
        data_type: "string"
      - name: is_first_case
        data_type: "boolean"
      - name: is_last_case
        data_type: "boolean"
      - name: scheduled_duration
        data_type: "bigint"
      - name: actual_duration
        data_type: "bigint"
      - name: start_offset
        data_type: "bigint"
      - name: end_offset
        data_type: "bigint"
      - name: is_running_during_lunch
        data_type: "boolean"
      - name: first_primary_surgeon
        data_type: "string"
      - name: surgeon_count
        data_type: "int"
      - name: primary_procedure_name
        data_type: "string"
      - name: primary_procedure_cpt
        data_type: "string"
      - name: procedure_count
        data_type: "int"
      - name: is_add_on
        data_type: "boolean"
      - name: prev_flip_case_id
        data_type: "string"
        description: "The case_id of the most recent case that the first primary surgeon has in another room. Definition is taken from flip_room_case_lookup_table."
      - name: is_flip_room
        data_type: "boolean"
        description: "True when the first primary surgeon has at least one case in another room on the same day. Definition is taken from flip_room_case_lookup_table."
      - name: is_apella_data
        data_type: "boolean"
      - name: is_valid_data
        data_type: "boolean"

  - name: forecasting_case_features_combined
    columns:
      - name: ds
        data_type: "timestamp"
      - name: case_id
        data_type: "string"
      - name: org_id
        data_type: "string"
      - name: date_of_surgery
        data_type: "date"
      - name: days_since_jan1_2022
        data_type: "int"
      - name: scheduled_start_datetime_local
        data_type: "datetime"
      - name: scheduled_end_datetime_local
        data_type: "datetime"
      - name: actual_start_datetime_local
        data_type: "datetime"
      - name: actual_end_datetime_local
        data_type: "datetime"
      - name: start_event_id
        data_type: "string"
      - name: end_event_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: room
        data_type: "string"
      - name: first_primary_procedure
        data_type: "string"
      - name: first_primary_cpt
        data_type: "string"
      - name: month_of_surgery
        data_type: "int"
      - name: scheduled_starting_hour
        data_type: "int"
      - name: day_of_week
        data_type: "int"
      - name: case_type_short
        data_type: "string"
        description: "Shortened case type used for forecasting. Note that some case types are combined or renamed."
      - name: patient_class
        data_type: "string"
      - name: outpatient
        data_type: "tinyint"
      - name: to_follow_case
        data_type: "tinyint"
      - name: first_case
        data_type: "tinyint"
      - name: last_case
        data_type: "tinyint"
      - name: running_during_lunch
        data_type: "tinyint"
      - name: scheduled_duration
        data_type: "bigint"
      - name: actual_duration
        data_type: "bigint"
      - name: start_offset
        data_type: "bigint"
      - name: end_offset
        data_type: "bigint"
      - name: schedule_actual_diff_normalized
        data_type: "bigint"
      - name: surgeon_count
        data_type: "int"
      - name: first_primary_surgeon
        data_type: "string"
      - name: first_primary_surgeon_id
        data_type: "string"
      - name: surgeon_proc_combo_num
        data_type: "string"
        description: "Concatenation of first_primary_surgeon name, first_primary_procedure name, and procedure_count."
      - name: procedure_count
        data_type: "int"
      - name: num_scheduled_cases
        data_type: "int"
      - name: number_of_or_day_different_procedures
        data_type: "int"
      - name: number_of_or_day_different_surgeons
        data_type: "int"
      - name: sum_or_day_scheduled_minutes
        data_type: "int"
      - name: apella_data
        data_type: "tinyint"
      - name: add_on
        data_type: "tinyint"
      - name: prev_flip_case_id
        data_type: "string"
        description: "The case_id of the most recent case that the first primary surgeon has in another room. Definition is taken from flip_room_case_lookup_table."
      - name: is_flip_room
        data_type: "boolean"
        description: "True when the first primary surgeon has at least one case in another room on the same day. Definition is taken from flip_room_case_lookup_table."
      - name: business_unit
        data_type: "string"
      - name: minutes_after_previous_case_scheduled_end
        type: "float"
      - name: cumsum_scheduled_case_duration_so_far
        type: "float"
      - name: number_first_cases_started_at_same_time
        type: "int"      
      - name: is_num_phases_match_num_cases
        data_type: "boolean"
      - name: api_date_range_this_or_prev_case_was_reordered
        data_type: "boolean"
      - name: historical_date_range_or_day_had_reordering
        data_type: "boolean"
      - name: case_has_experienced_reordering
        data_type: "boolean"

  - name: forecasting_surgeon_procedure_feature_agg
    columns:
      - name: ds
        data_type: "timestamp"
      - name: surgeon_proc_combo_num
        data_type: "string"
      - name: combo_count
        data_type: "int"
      - name: actual_duration_mean
        data_type: "float64"
      - name: end_offset_mean
        data_type: "float64"
      - name: schedule_actual_diff_normalized_mean
        data_type: "float64"
      - name: actual_duration_var
        data_type: "float64"
      - name: end_offset_var
        data_type: "float64"
      - name: schedule_actual_diff_normalized_var
        data_type: "float64"

  - name: forecasting_standalone_surgeon_procedure_feature_agg
    description: Table of means and variances by first primary surgeon, first primary procedure and condensed procedure categories with site and org mapped used for inference for the simplified, standalone case duration model
    columns:
      - name: ds
        data_type: "timestamp"
      - name: org_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: first_primary_surgeon
        data_type: "string"
        description: "First and last name (LAST, FIRST) of first primary procedure."
      - name: first_primary_procedure
        data_type: "string"
      - name: case_count
        data_type: "int"
        description: "Count of the number of cases for the given first primary surgeon and procedure"
      - name: actual_duration_mean
        data_type: "float64"
      - name: end_offset_mean
        data_type: "float64"
      - name: schedule_actual_diff_normalized_mean
        data_type: "float64"
      - name: actual_duration_var
        data_type: "float64"
      - name: end_offset_var
        data_type: "float64"
      - name: schedule_actual_diff_normalized_var
        data_type: "float64"

  - name: case_features_combined_intermediate
    description: This table currently duplicates `forecasting_case_features_combined` and adds useful columns for analysis
      such as `service_line_id` and `service_line_name`. Eventually, this will be refactored to depend only on core namespace tables. 
      The purpose of this table is to serve as a starting point for analytics that has no impact on the forecasting pipeline.
    columns:
      - name: ds
        data_type: "timestamp"
      - name: case_id
        data_type: "string"
      - name: org_id
        data_type: "string"
      - name: date_of_surgery
        data_type: "date"
      - name: days_since_jan1_2022
        data_type: "int"
      - name: scheduled_start_datetime_local
        data_type: "datetime"
      - name: scheduled_end_datetime_local
        data_type: "datetime"
      - name: actual_start_datetime_local
        data_type: "datetime"
      - name: actual_end_datetime_local
        data_type: "datetime"
      - name: start_event_id
        data_type: "string"
      - name: end_event_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: room
        data_type: "string"
      - name: first_primary_procedure
        data_type: "string"
      - name: first_primary_cpt
        data_type: "string"
      - name: month_of_surgery
        data_type: "int"
      - name: scheduled_starting_hour
        data_type: "int"
      - name: day_of_week
        data_type: "int"
      - name: case_type_short
        data_type: "string"
        description: "Shortened case type used for forecasting. Note that some case types are combined or renamed."
      - name: patient_class
        data_type: "string"
      - name: outpatient
        data_type: "tinyint"
      - name: to_follow_case
        data_type: "tinyint"
      - name: first_case
        data_type: "tinyint"
      - name: last_case
        data_type: "tinyint"
      - name: running_during_lunch
        data_type: "tinyint"
      - name: scheduled_duration
        data_type: "bigint"
      - name: actual_duration
        data_type: "bigint"
      - name: start_offset
        data_type: "bigint"
      - name: end_offset
        data_type: "bigint"
      - name: schedule_actual_diff_normalized
        data_type: "bigint"
      - name: surgeon_count
        data_type: "int"
      - name: first_primary_surgeon
        data_type: "string"
      - name: surgeon_proc_combo_num
        data_type: "string"
        description: "Concatenation of first_primary_surgeon name, first_primary_procedure name, and procedure_count."
      - name: procedure_count
        data_type: "int"
      - name: num_scheduled_cases
        data_type: "int"
      - name: number_of_or_day_different_procedures
        data_type: "int"
      - name: number_of_or_day_different_surgeons
        data_type: "int"
      - name: sum_or_day_scheduled_minutes
        data_type: "int"
      - name: apella_data
        data_type: "tinyint"
      - name: add_on
        data_type: "tinyint"
      - name: prev_flip_case_id
        data_type: "string"
        description: "The case_id of the most recent case that the first primary surgeon has in another room. Definition is taken from flip_room_case_lookup_table."
      - name: is_flip_room
        data_type: "boolean"
        description: "True when the first primary surgeon has at least one case in another room on the same day. Definition is taken from flip_room_case_lookup_table."
      - name: business_unit
        data_type: "string"
      - name: minutes_after_previous_case_scheduled_end
        type: "float"
      - name: cumsum_scheduled_case_duration_so_far
        type: "float"
      - name: number_first_cases_started_at_same_time
        type: "int"
      - name: is_num_phases_match_num_cases
        data_type: "boolean"
      - name: api_date_range_this_or_prev_case_was_reordered
        data_type: "boolean"
      - name: historical_date_range_or_day_had_reordering
        data_type: "boolean"
      - name: case_has_experienced_reordering
        data_type: "boolean"
      - name: site_name
        data_type: "string"
      - name: service_line_id
        data_type: "string"
      - name: service_line_name
        data_type: "string"
        description: the display name of `service_line_id`