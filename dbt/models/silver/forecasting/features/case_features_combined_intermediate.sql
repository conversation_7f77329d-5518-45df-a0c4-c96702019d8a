-- This duplicates `forecasting_case_features_combined`, and adds the service line fields from core cases
-- The end goal is to refactor all of this to reference other core tables, but this is an intermediate solution
--  that allows us to add columns to the forecasting table without modifying the forecasting pipeline
{{ daily_config() }}


with forecasting_case_features_combined as (
    select *
    from {{ ref('forecasting_case_features_combined') }}
    where ds = {{ ds() }}
)

select
    fc.*,
    cc.site_name,
    cc.service_line_id, --only exists in API data
    --TODO: normalize across historical and API cases, map to service_line_id
    coalesce(cc.service_line_name, hc.service_line) as service_line_name
from forecasting_case_features_combined as fc
left outer join {{ ref('core_cases') }} as cc
    on
        fc.case_id = cc.apella_case_id
        and cc.ds = {{ ds() }}
left outer join {{ ref('historical_cases_normalized') }} as hc
    on
        fc.case_id = hc.file_case_id
        and hc.ds = {{ ds() }}
