{{ daily_config() }}

select
    {{ ds() }} as ds,
    block_id,
    block_time_date,
    org_id,
    block_time_ids,
    site_ids,
    room_ids,
    unadjusted_block_duration_minutes,
    adjusted_block_duration_minutes,
    actual_clipped_duration_minutes,
    total_turnover_in_minutes,
    number_of_cases,
    actual_adjusted_utilization,
    scheduled_adjusted_utilization
from {{ ref("block_time_utilization_houston_methodist") }}
where ds = {{ ds() }}
union all
select
    {{ ds() }} as ds,
    block_id,
    block_time_date,
    org_id,
    block_time_ids,
    site_ids,
    room_ids,
    unadjusted_block_duration_minutes,
    adjusted_block_duration_minutes,
    actual_clipped_duration_minutes,
    total_turnover_in_minutes,
    number_of_cases,
    actual_adjusted_utilization,
    scheduled_adjusted_utilization
from {{ ref("block_time_utilization_tampa_general") }}
where ds = {{ ds() }}
