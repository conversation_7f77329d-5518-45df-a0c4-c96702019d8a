{{ daily_config() }}

with unadjusted_block_duration_cte as (
    -- We'll combine this cte with many others to create the final `ingredients` table
    -- On top of calculating unadjusted_block_time, we add site_id and room_id which will
    -- be useful in debugging.
    -- Each cte will be join on block_id and block_time_date, which is the unit of computation
    select
        bt.block_id,
        s.org_id,
        date(bt.start_time, s.timezone) as block_time_date,
        array_agg(bt.id) as block_time_ids,
        array_agg(r.site_id) as site_ids,
        array_agg(bt.room_id) as room_ids,
        sum(timestamp_diff(bt.end_time, bt.start_time, minute)) as unadjusted_block_duration_minutes
    from {{ api_table_snapshot("bronze", "public_block_times", "bt") }}
    inner join {{ api_table_snapshot("bronze", "public_rooms", "r") }}
        on bt.room_id = r.id
    inner join {{ api_table_snapshot("bronze", "public_sites", "s") }}
        on r.site_id = s.id
    group by bt.block_id, block_time_date, s.org_id
),

adjusted_block_duration_cte as (
    -- here we just compute the sum of all the AvailableIntervals inside a block,
    -- TGH calls this the `adjusted_block_duration`
    select
        block_id,
        block_time_date,
        sum(timestamp_diff(interval_end_time, interval_start_time, minute)) as adjusted_block_duration_minutes
    from {{ ref("block_time_available_interval") }}
    where ds = {{ ds () }}
    group by block_id, block_time_date
),

add_clipped_times_cte as (
    -- here we just add clipped times to the available interval start/end time as needed
    select
        ai.block_id,
        ai.block_time_date,
        ai_to_c.score,
        cc.site_timezone,
        cc.scheduled_start_datetime_local,
        cc.scheduled_end_datetime_local,
        cc.actual_start_datetime_local,
        cc.actual_end_datetime_local,
        -- in the not so rare case that actual schedule is completely outside the interval, the clipped duration
        -- should be 0. We could restrict durations, or the actual_start/end times
        -- to think through these, imagine the actual interval being before interval_start or after interval_end
        -- (we should end up with actual_clipped_start = actual_clipped_end
        least(
            greatest(ai.interval_start_time, timestamp(cc.scheduled_start_datetime_local, cc.site_timezone)),
            ai.interval_end_time
        ) as scheduled_clipped_start,
        greatest(
            least(ai.interval_end_time, timestamp(cc.scheduled_end_datetime_local, cc.site_timezone)),
            ai.interval_start_time
        ) as scheduled_clipped_end,
        least(
            greatest(ai.interval_start_time, timestamp(cc.actual_start_datetime_local, cc.site_timezone)),
            ai.interval_end_time
        ) as actual_clipped_start,
        greatest(
            least(ai.interval_end_time, timestamp(cc.actual_end_datetime_local, cc.site_timezone)),
            ai.interval_start_time
        ) as actual_clipped_end
    from {{ ref("block_time_available_interval_to_case") }} as ai_to_c
    inner join {{ ref("core_cases") }} as cc
        on ai_to_c.apella_case_id = cc.apella_case_id
    inner join {{ ref("block_time_available_interval") }} as ai
        on ai_to_c.available_interval_id = ai.id
    where
        ai_to_c.ds = {{ ds() }}
        and cc.ds = {{ ds() }}
        and ai.ds = {{ ds() }}
        and ai_to_c.score >= 4
        and cc.is_case_matched
),

clipped_and_unclipped_durations_cte as (
    -- here we just compute the total actual_unclipped/clipped duration in minutes in each
    -- block time. Remember that each block_time has many cases inside (we are summing
    -- over these cases)
    select
        block_id,
        block_time_date,
        max(timestamp(actual_end_datetime_local, site_timezone)) as actual_end_of_last_case,
        sum(datetime_diff(scheduled_end_datetime_local, scheduled_start_datetime_local, minute))
            as scheduled_unclipped_duration_minutes,
        sum(timestamp_diff(scheduled_clipped_end, scheduled_clipped_start, minute))
            as scheduled_clipped_duration_minutes,
        sum(datetime_diff(actual_end_datetime_local, actual_start_datetime_local, minute))
            as actual_unclipped_duration_minutes,
        sum(timestamp_diff(actual_clipped_end, actual_clipped_start, minute)) as actual_clipped_duration_minutes,
        count(*) as number_of_cases
    from add_clipped_times_cte
    group by block_id, block_time_date
)

select
    {{ ds() }} as ds,
    ubtd.block_id,
    ubtd.block_time_date,
    ubtd.org_id,
    ubtd.block_time_ids,
    ubtd.site_ids,
    ubtd.room_ids,
    ubtd.unadjusted_block_duration_minutes,
    abtd.adjusted_block_duration_minutes,
    -- if there are not cases for this block_id, caud will have no data and the left join
    -- results in null values
    coalesce(caud.scheduled_unclipped_duration_minutes, 0) as scheduled_unclipped_duration_minutes,
    coalesce(caud.scheduled_clipped_duration_minutes, 0) as scheduled_clipped_duration_minutes,
    coalesce(caud.actual_unclipped_duration_minutes, 0) as actual_unclipped_duration_minutes,
    coalesce(caud.actual_clipped_duration_minutes, 0) as actual_clipped_duration_minutes,
    coalesce(caud.number_of_cases, 0) as number_of_cases
from unadjusted_block_duration_cte as ubtd
inner join adjusted_block_duration_cte as abtd
    on
        ubtd.block_id = abtd.block_id
        and ubtd.block_time_date = abtd.block_time_date
left outer join clipped_and_unclipped_durations_cte as caud
-- clipped_and_unclipped durations, only exist for `actual` durations, which implies the case
-- is in the past. We also want to get the common_utilization_ingredients for blocks in
-- the future, that's why we are doing a left join here
    on
        ubtd.block_id = caud.block_id
        and ubtd.block_time_date = caud.block_time_date
