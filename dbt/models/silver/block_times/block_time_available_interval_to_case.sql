{{ daily_config() }}

with interval_with_all_surgeons as (
    -- 1. We add all surgeons that own a block to the available interval
    -- 2. If a block time was divided in more than one interval, we'll
    -- have a row per interval, each with its own id, start/end time
    -- 3. We keep columns that are needed to match to cases:
    --  interval_start/end_time, site, room, surgeons
    select
        ai.id as interval_id,
        ai.block_time_id,
        ai.block_id,
        ai.interval_start_time,
        ai.interval_end_time,
        r.site_id,
        ai.room_id,
        date(ai.interval_start_time, ai.timezone) as block_date,
        array_agg(bs.staff_id) as block_staff_id_array
    from {{ ref("block_time_available_interval") }} as ai
    inner join {{ api_table_snapshot("bronze", "public_block_surgeons", "bs") }}
        on ai.block_id = bs.block_id
    inner join {{ api_table_snapshot("bronze", "public_rooms", "r") }}
        on ai.room_id = r.id
    -- in the `group by` below, once we group by `id` and `block_id` all the rest of the values
    -- are defined and could be taken out of the `group by` and replaced by aggregation any_value above
    -- (or other method)
    where ai.ds = {{ ds() }}
    group by
        ai.id,
        ai.block_time_id,
        ai.block_id,
        ai.room_id,
        ai.interval_start_time,
        ai.interval_end_time,
        ai.timezone,
        r.site_id
),

case_with_all_staff as (
    -- 1. We add all surgeons involved in a case (we use core_case_staff.is_candidate_primary_surgeon)
    -- 2. We keep columns that are needed to match to intervals:
    --  case_start/end_time, site, room, surgeons
    select
        cc.apella_case_id,
        cc.site_id,
        cc.room_id,
        date(cc.scheduled_start_datetime_local) as case_date,
        timestamp(cc.scheduled_start_datetime_local, cc.site_timezone) as scheduled_case_start_timestamp,
        timestamp(cc.scheduled_end_datetime_local, cc.site_timezone) as scheduled_case_end_timestamp,
        case
            when cc.actual_end_datetime_local is not null then timestamp(cc.actual_end_datetime_local, cc.site_timezone)
        end as actual_case_end_timestamp,
        case
            when
                cc.actual_start_datetime_local is not null
                then timestamp(cc.actual_start_datetime_local, cc.site_timezone)
        end as actual_case_start_timestamp,
        array_agg(cs.apella_staff_id) as case_staff_id_array
    from {{ ref("core_cases") }} as cc
    inner join {{ ref("core_case_staff") }} as cs
        on cc.apella_case_id = cs.apella_case_id
    where
        cc.ds = {{ ds() }}
        and cs.ds = {{ ds() }}
        and cc.scheduled_start_datetime_local is not null
        and cc.scheduled_end_datetime_local is not null
        and cs.is_candidate_primary_surgeon
    group by
        cc.apella_case_id,
        cc.site_id,
        cc.room_id,
        cc.scheduled_start_datetime_local,
        cc.scheduled_end_datetime_local,
        cc.actual_start_datetime_local,
        cc.actual_end_datetime_local,
        cc.site_timezone
),

case_interval_scoring_features as (
    -- 1. We explode all cases in a (site, date) with all blocks in the same (site, date).
    -- 2. For each pair, we compute the features that are needed to compute the `score`
    -- of how strong the match is
    -- 3. Later on we'll compute the score and keep the assignment as the match with the highest score
    select
        i.interval_id,
        i.block_time_id,
        i.block_id,
        c.apella_case_id,
        i.site_id,
        i.room_id = c.room_id as room_match,
        array_length(
            array(
                select block_staff_id from unnest(i.block_staff_id_array) as block_staff_id
                intersect distinct
                select case_staff_id from unnest(c.case_staff_id_array) as case_staff_id
            )
        ) > 0 as surgeon_match,
        array_length(i.block_staff_id_array) as block_staff_count,
        -- We compute the amount of overlap in minutes between the case and the interval
        -- (we do this for both scheduled and actual times)
        -- case 1
        --  ----------                  case
        --                 ----------   interval
        --  0 overlap
        --
        --  case 2
        --                 ----------   case
        --  ----------                  interval
        --  0 overlap
        --
        -- case 3
        --  ----------                  case
        --     ----------               interval
        -- 7 units of overlap
        --
        -- case 4
        --     ----------               case
        --  ----------                  interval
        -- 7 units of overlap
        case
            when c.scheduled_case_end_timestamp < i.interval_start_time then 0
            when i.interval_end_time < c.scheduled_case_start_timestamp then 0
            else timestamp_diff(
                least(c.scheduled_case_end_timestamp, i.interval_end_time),
                greatest(c.scheduled_case_start_timestamp, i.interval_start_time),
                minute
            )
        end as scheduled_overlap_minutes,
        case
            when c.actual_case_start_timestamp is null or c.actual_case_end_timestamp is null then null
            when c.actual_case_end_timestamp < i.interval_start_time then 0
            when i.interval_end_time < c.actual_case_start_timestamp then 0
            else timestamp_diff(
                least(c.actual_case_end_timestamp, i.interval_end_time),
                greatest(c.actual_case_start_timestamp, i.interval_start_time),
                minute
            )
        end as actual_overlap_minutes
    from interval_with_all_surgeons as i
    inner join case_with_all_staff as c
        on
            i.site_id = c.site_id
            and i.block_date = c.case_date
),


case_interval_scoring as (
    -- 1. We compute a score for each pair of case and interval
    -- 2. later we keep the pair with the highest score
    select
        interval_id,
        block_time_id,
        block_id,
        apella_case_id,
        room_match,
        surgeon_match,
        block_staff_count,
        scheduled_overlap_minutes,
        actual_overlap_minutes,
        case
            when
                room_match
                and surgeon_match
                and greatest(coalesce(actual_overlap_minutes, 0), scheduled_overlap_minutes) > 0
                then 5
            when surgeon_match and greatest(coalesce(actual_overlap_minutes, 0), scheduled_overlap_minutes) > 0 then 4
            when surgeon_match then 3
            when coalesce(actual_overlap_minutes, scheduled_overlap_minutes) > 0 then 2
            when room_match then 1
            else 0
        end as score
    from case_interval_scoring_features
)


-- We keep the assignment between the case and available interval with the highest score.
-- However, given a higest score, there could be multiple available_intervals matching the
-- same case, how do we decide which one to assign to?
-- example:
--     block_time_1             |--------------------------------|
--     available_interval_1                  |-------------------|
--     block_time_2             |--------------------------------|
--     available_interval_2     |--------------------------------|
--     matching case                    |-------------------|
--
-- We break ties with the actual_overlap_minutes
-- If we still have ties, currently we select a random assignment among those
select
    {{ ds() }} as ds,
    cis.apella_case_id,
    cis.score,
    cis.block_id,
    cis.block_time_id,
    cis.interval_id as available_interval_id
from case_interval_scoring as cis
qualify row_number() over (
    partition by cis.apella_case_id order by
        cis.score desc,
        cis.block_staff_count asc,
        cis.actual_overlap_minutes asc
) = 1
