{{ daily_config() }}

with turnover_cte as (
    -- i_to_c has the apella_case_id we are interested in, but is missing service_line_id
    -- get service_line_id by joining with core_cases
    -- then get the turnover in minutes by service_line_id from hardcoded_turnover_by_customer
    select
        i_to_c.block_id,
        date(cc.scheduled_start_datetime_local) as block_time_date,
        sum(htbc.turnover_in_minutes) as turnover_in_minutes
    from {{ ref("block_time_available_interval_to_case") }} as i_to_c
    inner join {{ ref("core_cases") }} as cc
        on i_to_c.apella_case_id = cc.apella_case_id
    inner join {{ ref("hardcoded_turnover_by_customer") }} as htbc
        on cc.service_line_id = htbc.service_line_id
    where
        htbc.org_id = "tampa_general"
        and i_to_c.ds = {{ ds() }}
        and cc.ds = {{ ds() }}
        and cc.is_case_matched
        and htbc.ds = {{ ds () }}
    group by i_to_c.block_id, date(cc.scheduled_start_datetime_local)
),

expanded_ingredients_cte as (
    select
        cui.block_id,
        cui.block_time_date,
        cui.org_id,
        cui.block_time_ids,
        cui.site_ids,
        cui.room_ids,
        cui.unadjusted_block_duration_minutes,
        cui.adjusted_block_duration_minutes,
        cui.scheduled_unclipped_duration_minutes,
        cui.scheduled_clipped_duration_minutes,
        cui.actual_unclipped_duration_minutes,
        cui.actual_clipped_duration_minutes,
        cui.number_of_cases,
        turnover_cte.turnover_in_minutes
    from {{ ref("block_time_common_utilization_ingredients") }} as cui
    inner join turnover_cte
        on
            cui.block_id = turnover_cte.block_id
            and cui.block_time_date = turnover_cte.block_time_date
    where
        cui.ds = {{ ds() }}
        and cui.org_id = "tampa_general"
)

select
    {{ ds() }} as ds,
    block_id,
    block_time_date,
    org_id,
    site_ids,
    block_time_ids,
    room_ids,
    unadjusted_block_duration_minutes,
    adjusted_block_duration_minutes,
    actual_clipped_duration_minutes,
    turnover_in_minutes as total_turnover_in_minutes,
    number_of_cases,
    100
    * scheduled_clipped_duration_minutes
    / adjusted_block_duration_minutes as scheduled_adjusted_utilization,
    100
    * (actual_clipped_duration_minutes + turnover_in_minutes)
    / adjusted_block_duration_minutes as actual_adjusted_utilization
from expanded_ingredients_cte
