{{ daily_config() }}

with hardcoded_mapping as (
    select
        'Anesthesiology' as service_line_name,
        null as block_name
    union all
    select
        'Cardiothoracic Surgery' as service_line_namae,
        'USF CT Surgery' as block_name
    union all
    select
        'Cardiothoracic Surgery' as service_line_namae,
        'TGMG Thoracic' as block_name
    union all
    select
        'Cardiology' as service_line_namae,
        null as block_name
    union all
    select
        'Dentistry' as service_line_namae,
        null as block_name
    union all
    select
        'General Surgery' as service_line_namae,
        'USF General Surgery' as block_name
    union all
    select
        'General Surgery' as service_line_namae,
        'USF Colorectal' as block_name
    union all
    select
        'General Surgery' as service_line_namae,
        'Surgical Oncology' as block_name
    union all
    select
        'Gynecology' as service_line_namae,
        'USF GYN Onc' as block_name
    union all
    select
        'Neurological Surgery' as service_line_namae,
        'USF Neuro Trauma' as block_name
    union all
    select
        'Neurological Surgery' as service_line_namae,
        'USF Neurosurgery' as block_name
    union all
    select
        'Obstetrics' as service_line_namae,
        null as block_name
    union all
    select
        'Ophthalmology' as service_line_namae,
        null as block_name
    union all
    select
        'Oral & Maxillofacial Surgery' as service_line_namae,
        null as block_name
    union all
    select
        'Orthopaedics' as service_line_namae,
        'Orthopedics' as block_name
    union all
    select
        'Orthopaedics' as service_line_namae,
        'Ortho Trauma' as block_name
    union all
    select
        'ENT' as service_line_namae,
        'USF ENT' as block_name
    union all
    select
        'ENT' as service_line_namae,
        'Tampa Bay Hearing' as block_name
    union all
    select
        'Pain Medicine' as service_line_namae,
        null as block_name
    union all
    select
        'Parathyroid' as service_line_namae,
        null as block_name
    union all
    select
        'Pediatric Surgery' as service_line_namae,
        null as block_name
    union all
    select
        'Plastic Surgery' as service_line_namae,
        'USF Plastics' as block_name
    union all
    select
        'Podiatry' as service_line_namae,
        null as block_name
    union all
    select
        'Robotic Surgery' as service_line_namae,
        'USF Colorectal Robotics' as block_name
    union all
    select
        'Robotic Surgery' as service_line_namae,
        'USF Urology Robotics' as block_name
    union all
    select
        'Robotic Surgery' as service_line_namae,
        'TGMG Thoracic Robotics' as block_name
    union all
    select
        'Robotic Surgery' as service_line_namae,
        'Surgical Oncology Robotics' as block_name
    union all
    select
        'Robotic Surgery' as service_line_namae,
        'USF CT Surgery Robotics' as block_name
    union all
    select
        'Robotic Surgery' as service_line_namae,
        'TGMG Transplant Robotics' as block_name
    union all
    select
        'Thyroid' as service_line_namae,
        null as block_name
    union all
    select
        'Transplant' as service_line_namae,
        'TGMG Transplant' as block_name
    union all
    select
        'Trauma Surgery' as service_line_namae,
        null as block_name
    union all
    select
        'Urology' as service_line_namae,
        null as block_name
    union all
    select
        'Vascular Surgery' as service_line_namae,
        'USF Vascular' as block_name
    union all
    select
        'Vascular Surgery' as service_line_namae,
        'USF Pulmonary' as block_name
)

select
    {{ ds() }} as ds,
    service_line_name,
    block_name
from hardcoded_mapping
