{{ daily_config(produce_latest_snapshot=True) }}

{% set way_in_the_past = "'1970-01-01 00:00:00'" %}
{% set way_in_the_future = "'3000-01-01 00:00:00'" %}

with fake_first_release as (
    -- we append two fake releases, one before and one after the block_time. The end_time of the
    -- fake release in the front is exactly when the block_time
    -- starts. The start_time of the fake release at the ned, is exactly when the block_time ends.
    -- By doing this, we can extract available times as the gaps in between consecutive
    -- releases and don't need to look at block_times any more
    -- this CTE adds the release before the block_time. The next CTE adds the release after the block_time
    --
    -- block time:            ---------------------------------------
    -- release:                                      ----------------
    --faked releases:  -------                                       --------
    --extracted gaps:         -----------------------
    select
        bt.block_id,
        bt.id as block_time_id,
        timestamp({{ way_in_the_past }}) as start_time,
        bt.start_time as end_time
    from {{ api_table_snapshot("bronze", "public_block_times", "bt") }}
),

fake_last_release as (
    -- ideam as before, we append another fake release, this time at the end. The start_time of this
    -- fake release is exactly when the block_time ends
    select
        bt.block_id,
        bt.id as block_time_id,
        bt.end_time as start_time,
        timestamp({{ way_in_the_future }}) as end_time
    from {{ api_table_snapshot("bronze", "public_block_times", "bt") }}
),

releases as (
    -- now the real releases (remove unreleased ones)
    -- we only take into account releases that have been released before
    -- block_date - release_cut_off_days
    select
        bt.block_id,
        btr.block_time_id,
        btr.start_time,
        btr.end_time
    from {{ api_table_snapshot("bronze", "public_block_time_releases", "btr") }}
    inner join {{ api_table_snapshot("bronze", "public_block_times", "bt") }}
        on btr.block_time_id = bt.id
    inner join {{ api_table_snapshot("bronze", "public_rooms", "r") }}
        -- we need to go through rooms to get the site_id
        on bt.room_id = r.id
    inner join {{ api_table_snapshot("bronze", "public_sites", "s") }}
        -- we need the sites to get the timezone
        on r.site_id = s.id
    where
        btr.unreleased_time is null
        and date(btr.released_time, s.timezone)
        <= date_sub(date(bt.start_time, s.timezone), interval 7 day)
),

all_releases as (
    -- put all releases together (fake and real)
    select * from fake_first_release
    union all
    select * from fake_last_release
    union all
    select * from releases
),

with_prev_release as (
    -- now use lag to combine prev release start/end with current release start/end
    select
        block_id,
        block_time_id,
        start_time,
        end_time,
        lag(start_time) over (partition by block_time_id order by start_time) as prev_start_time,
        lag(end_time) over (partition by block_time_id order by start_time) as prev_end_time
    from all_releases
)

-- the available_interval is the time in between releases
-- prev_start_time       prev_end_time        start_time                end_time
--    |-----first_release------|                   |---second release -----|
--                             |-------------------|     this is the generated available time
select
    {{ ds() }} as ds,
    generate_uuid() as id,
    pr.block_id,
    pr.block_time_id,
    date(pr.start_time, s.timezone) as block_time_date,
    pr.prev_end_time as interval_start_time,
    pr.start_time as interval_end_time,
    bt.room_id,
    s.timezone
from with_prev_release as pr
inner join {{ api_table_snapshot("bronze", "public_block_times", "bt") }}
    on pr.block_time_id = bt.id
inner join {{ api_table_snapshot("bronze", "public_rooms", "r") }}
    on bt.room_id = r.id
inner join {{ api_table_snapshot("bronze", "public_sites", "s") }}
    on r.site_id = s.id
where
    pr.prev_end_time < pr.start_time
    and date(pr.start_time, s.timezone) = date(pr.prev_end_time, s.timezone)
