{{ daily_config() }}

with expanded_ingredients_cte as (
    select distinct
        block_id,
        block_time_date,
        org_id,
        site_id,
        unadjusted_block_duration_minutes,
        adjusted_block_duration_minutes,
        scheduled_clipped_duration_minutes,
        actual_clipped_duration_minutes,
        number_of_cases,
        case
            when site_id in ('HMH-OPC18', 'HMH-OPC19') then 27
            when site_id in ('HMH-WT03') then 40
            when site_id in ('HMH-HMW-OR') then 24
        end as turnover_in_minutes
    from {{ ref("block_time_common_utilization_ingredients") }}, unnest(site_ids) as site_id
    where
        ds = {{ ds() }}
        and org_id = 'houston_methodist'
        and site_id in ('HMH-OPC18', 'HMH-OPC19', 'HMH-WT03', 'HMH-HMW-OR')
),

aggregated_over_blocks as (
    select
        block_id,
        block_time_date,
        org_id,
        turnover_in_minutes,
        array_agg(site_id) as site_ids,
        sum(unadjusted_block_duration_minutes) as unadjusted_block_duration_minutes,
        sum(adjusted_block_duration_minutes) as adjusted_block_duration_minutes,
        sum(scheduled_clipped_duration_minutes) as scheduled_clipped_duration_minutes,
        sum(actual_clipped_duration_minutes) as actual_clipped_duration_minutes,
        sum(number_of_cases) as number_of_cases,
        sum(number_of_cases) * turnover_in_minutes as total_turnover_in_minutes
    from expanded_ingredients_cte
    group by block_id, block_time_date, org_id, turnover_in_minutes
)

select
    {{ ds() }} as ds,
    aob.block_id,
    aob.block_time_date,
    aob.org_id,
    ui.block_time_ids,
    aob.site_ids,
    ui.room_ids,
    aob.unadjusted_block_duration_minutes,
    aob.adjusted_block_duration_minutes,
    aob.actual_clipped_duration_minutes,
    aob.total_turnover_in_minutes,
    aob.number_of_cases,
    100
    * aob.scheduled_clipped_duration_minutes
    / aob.adjusted_block_duration_minutes as scheduled_adjusted_utilization,
    100
    * (aob.actual_clipped_duration_minutes + aob.total_turnover_in_minutes)
    / aob.adjusted_block_duration_minutes as actual_adjusted_utilization
from aggregated_over_blocks as aob
inner join {{ ref("block_time_common_utilization_ingredients") }} as ui
    on
        aob.block_time_date = ui.block_time_date
        and aob.block_id = ui.block_id
where ui.ds = {{ ds() }}
