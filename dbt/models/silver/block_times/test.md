This is not a test, but since these queries are somewhat complex I am thinking that setting
an integration test, and asserting results are as expected would be useful.
I don't think our current framework supports this, so I'm just writing ideas for things to test, 
not sure what would be the best way to proceed.

Maybe add cases/time_block modifications below to our cloud-api dev environment and watch
data flow through the system and make sure we are computing the right thing?

```commandline
time increases downward	case	| time_block |	result
		↓  
		0  				should return 0 cases
		1  	add case 1							
		2  				should return 0 cases
		3  		add_block
		4  				should return case 1
		5  	add case 2
		6  				should have 2 cases
		7  		release part of block
		 		(including case 1)
		8  				should return case 2
		9  		release all block 
		10 				should return 0 cases
		11 		unrelease block
		12				should return 2 cases
```

Other ideas to test:
### Simple cases
1. block_time exists, case is added inside the block -> case reflect block
1. case is added outside a block -> we return no cases
1. case partially overlaps with block_time, on each end (2 tests)
1. case for sureon in block_id1 is scheduled in a different room from what the block claims
1. case is added inside a block at t1, then at a later time t2, case is moved in time
to outside the block_time, in between t1 and t2, we have block information, after t2 we don't
1. case is added inside a block at t1, then at a later time t2, case is moved to a different room.
   We should get a `case` In between t1 and t2, but also after t2
1. case is added inside a block at time t1, then the block is completely released at time t2. In
this case, we have block information in between t1 and t2, but no information after t2
