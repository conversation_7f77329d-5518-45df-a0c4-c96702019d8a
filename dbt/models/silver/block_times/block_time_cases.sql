{{ daily_config(produce_latest_snapshot=True) }}

-- here we just add clipped times to the available interval start/end time as needed
select
    {{ ds() }} as ds,
    ai.block_id,
    ai.block_time_date,
    ai_to_c.score,
    cc.apella_case_id,
    cc.room_id,
    cc.site_id,
    cc.site_timezone,
    cc.status,
    case
        when
            cc.actual_start_datetime_local is not null and cc.actual_end_datetime_local is not null
            then datetime_diff(cc.actual_end_datetime_local, cc.actual_start_datetime_local, second)
    end as actual_case_seconds,
    datetime_diff(cc.scheduled_end_datetime_local, cc.scheduled_start_datetime_local, second) as scheduled_case_seconds,
    -- in the not so rare case that actual schedule is completely outside the interval, the clipped duration
    -- should be 0. We could restrict durations, or the actual_start/end times
    -- to think through these, imagine the actual interval being before interval_start or after interval_end
    -- (we should end up with actual_clipped_start = actual_clipped_end
    timestamp_diff(greatest(
        least(ai.interval_end_time, timestamp(cc.scheduled_end_datetime_local, cc.site_timezone)),
        ai.interval_start_time
    ), least(
        greatest(ai.interval_start_time, timestamp(cc.scheduled_start_datetime_local, cc.site_timezone)),
        ai.interval_end_time
    ), second) as utilized_scheduled_seconds,
    case
        when cc.actual_start_datetime_local is not null and cc.actual_end_datetime_local is not null
            then timestamp_diff(greatest(
                least(ai.interval_end_time, timestamp(cc.actual_end_datetime_local, cc.site_timezone)),
                ai.interval_start_time
            ), least(
                greatest(ai.interval_start_time, timestamp(cc.actual_start_datetime_local, cc.site_timezone)),
                ai.interval_end_time
            ), second)
    end as utilized_actual_seconds
from {{ ref("block_time_available_interval_to_case") }} as ai_to_c
inner join {{ ref("core_cases") }} as cc
    on ai_to_c.apella_case_id = cc.apella_case_id
inner join {{ ref("block_time_available_interval") }} as ai
    on ai_to_c.available_interval_id = ai.id
where
    ai_to_c.ds = {{ ds() }}
    and cc.ds = {{ ds() }}
    and ai.ds = {{ ds() }}
    and ai_to_c.score > 0
