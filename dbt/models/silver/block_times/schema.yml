version: 1

models:
  - name: block_time_available_interval
    description: >
      block time available intervals is what is left over from a block_time once releases are 
      taken into account. Most of the work MLDS cares about is related to this table (not blocks
      or block_times).
      
      example 1:
          |----------------block time ---------------------|
                          no release
          |---------------available interval---------------|
      
      example 2:
          |----------------block time ---------------------|
          |---------first_release (everything)-------------|
                            no available time
      
      example 3:
          |----------------block time ---------------------|
          |--first_release--|      |---second release -----|
                            |------|     this is the generated available time
      example 4:
          |----------------block time ---------------------|
                       |--first_release--|
          |------------|                 |-----------------|   this is the generated available time
    columns:
      - name: ds
        data_type: "timestamp"
      - name: id
        data_type: "string"
      - name: block_id
        data_type: "string"
      - name: block_time_id
        data_type: "string"
      - name: block_time_date
        data_type: "date"
      - name: interval_start_time
        data_type: "timestamp"
      - name: interval_end_time
        data_type: "timestamp"
      - name: room_id
        data_type: "string"
      - name: timezone
        data_type: "string"

  - name: block_time_available_interval_to_case
    description: >
      Link cases to available_interval to cases that should be considered when computing
      utilization.
    columns:
      - name: ds
        data_type: "timestamp"
      - name: block_id
        data_type: "string"
      - name: block_time_id
        data_type: "string"
        description: "For back compatibility, will be removed soon"
      - name: available_interval_id
        data_type: "string"
        description: "For back compatibility, will be removed soon"
      - name: apella_case_id
        data_type: "string"
      - name: score
        data_type: "int"

  - name: block_time_cases
    description: >
      Table is block_time_available_interval_to_case along with additional details about the 
      utilized durations.
    columns: 
      - name: ds
        data_type: "timestamp"
      - name: block_id
        data_type: "string"
      - name: block_time_date
        data_type: "date"
      - name: score
        data_type: "int"
      - name: apella_case_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: site_timezone
        data_type: "string"
      - name: actual_case_seconds
        data_type: "int"
      - name: scheduled_case_seconds
        data_type: "int"
      - name: utilized_scheduled_seconds
        data_type: "int"
      - name: utilized_actual_seconds
        data_type: "int"

  - name: block_time_common_utilization_ingredients
    description: >
      This model has many columns that are common in computing utilization. Since each customer
      defines utilization in a slightly different way, not all columns are used by all customers.
    columns:
      - name: ds
        data_type: "timestamp"
      - name: block_id
        data_type: "string"
      - name: block_time_date
        data_type: "date"
      - name: org_id
        data_type: "string"
      - name: block_time_ids
        data_type: "array(string)"
      - name: site_ids
        data_type: "array(string)"
      - name: room_ids
        data_type: "array(string)"
      - name: unadjusted_block_duration_minutes
        data_type: "int"
        tests:
          - not_null
      - name: adjusted_block_duration_minutes
        data_type: "int"
        tests:
          - not_null
      - name: scheduled_unclipped_duration_minutes
        data_type: "int"
        tests:
          - not_null
      - name: scheduled_clipped_duration_minutes
        data_type: "int"
        tests:
          - not_null
      - name: actual_unclipped_duration_minutes
        data_type: "int"
        tests:
          - not_null
      - name: actual_clipped_duration_minutes
        data_type: "int"
        tests:
          - not_null
      - name: number_of_cases
        data_type: "int"
        tests:
          - not_null
      - name: actual_end_of_last_case
        data_type: "timestamp"


  - name: block_time_utilization_all_customers
    description: >
      In this table, we compute several forms of utilization.
      scheduled: means we are not using `actual` times but what was scheduled. 
      actual: means we are using `actual` times
      adjusted: means that we remove released time from the denominator.
      unadjusted: means tat we do not remove released time from the denominator
      all block_time_utilization tables should have the same schema. If you modify one, modify all
    columns:
      - name: ds
        data_type: "timestamp"
      - name: block_id
        data_type: "string"
      - name: block_time_date
        data_type: "date"
      - name: block_time_ids
        data_type: "array(string)"
      - name: room_ids
        data_type: "array(string)"
      - name: org_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: unadjusted_block_duration_minutes
        data_type: "int"
        description: >
          This is the length of the original block time (in minutes) without taking releases into account
        tests:
          - not_null
      - name: adjusted_block_duration_minutes
        data_type: "int"
        description: >
          This is the length of the block time (in minutes) after releases are taken into account
        tests:
          - not_null
      - name: actual_clipped_duration_minutes
        data_type: "int"
        description: >
          This is the sum of the actual_clipped minutes for each case that overlaps with
          an available interval
        tests:
          - not_null
      - name: total_turnover_in_minutes
        data_type: "int"
        tests:
          - not_null
      - name: number_of_cases
        data_type: "int"
        tests:
          - not_null
      - name: scheduled_adjusted_utilization
        data_type: "float"
        tests:
          - not_null
      - name: actual_adjusted_utilization
        data_type: "float"
        tests:
          - not_null


  - name: block_time_utilization_houston_methodist
    description: >
      In this table, we compute several forms of utilization.
      scheduled: means we are not using `actual` times but what was scheduled. 
      actual: means we are using `actual` times
      adjusted: means that we remove released time from the denominator.
      unadjusted: means tat we do not remove released time from the denominator
      all block_time_utilization tables should have the same schema. If you modify one, modify all
    columns:
      - name: ds
        data_type: "timestamp"
      - name: block_id
        data_type: "string"
      - name: block_time_date
        data_type: "date"
      - name: block_time_ids
        data_type: "array(string)"
      - name: room_ids
        data_type: "array(string)"
      - name: org_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: unadjusted_block_duration_minutes
        data_type: "int"
        description: >
          This is the length of the original block time (in minutes) without taking releases into account
        tests:
          - not_null
      - name: adjusted_block_duration_minutes
        data_type: "int"
        description: >
          This is the length of the block time (in minutes) after releases are taken into account
        tests:
          - not_null
      - name: actual_clipped_duration_minutes
        data_type: "int"
        description: >
          This is the sum of the actual_clipped minutes for each case that overlaps with
          an available interval
        tests:
          - not_null
      - name: total_turnover_in_minutes
        data_type: "int"
        tests:
          - not_null
      - name: number_of_cases
        data_type: "int"
        tests:
          - not_null
      - name: scheduled_adjusted_utilization
        data_type: "float"
        tests:
          - not_null
      - name: actual_adjusted_utilization
        data_type: "float"
        tests:
          - not_null


  - name: block_time_utilization_tampa_general
    description: >
      In this table, we compute several forms of utilization.
      scheduled: means we are not using `actual` times but what was scheduled. 
      actual: means we are using `actual` times
      adjusted: means that we remove released time from the denominator.
      unadjusted: means tat we do not remove released time from the denominator
      all block_time_utilization tables should have the same schema. If you modify one, modify all
    columns:
      - name: ds
        data_type: "timestamp"
      - name: block_id
        data_type: "string"
      - name: block_time_date
        data_type: "date"
      - name: block_time_ids
        data_type: "array(string)"
      - name: room_ids
        data_type: "array(string)"
      - name: org_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: unadjusted_block_duration_minutes
        data_type: "int"
        description: >
          This is the length of the original block time (in minutes) without taking releases into account
        tests:
          - not_null
      - name: adjusted_block_duration_minutes
        data_type: "int"
        description: >
          This is the length of the block time (in minutes) after releases are taken into account
        tests:
          - not_null
      - name: actual_clipped_duration_minutes
        data_type: "int"
        description: >
          This is the sum of the actual_clipped minutes for each case that overlaps with
          an available interval
        tests:
          - not_null
      - name: total_turnover_in_minutes
        data_type: "int"
        tests:
          - not_null
      - name: number_of_cases
        data_type: "int"
        tests:
          - not_null
      - name: scheduled_adjusted_utilization
        data_type: "float"
        tests:
          - not_null
      - name: actual_adjusted_utilization
        data_type: "float"
        tests:
          - not_null


  - name: block_time_tgh_service_line_to_block_name.sql
    description: >
      TGH is requesting some analysis that requires to map back and forth between
      service_lines and blocks. Matching is not perfect. This was originally done by Tierney
      and modifications might be needed
      A service line may map to zero, one or more than one block and vice versa.
      A service line that mapping to a null block does not necessarily indicate we know that service 
      line has no block, it might just mean we aren't sure how to map it.
      gsheet: https://docs.google.com/spreadsheets/d/12MX-EBzU3Yqzoba1PmJ89HD9sqGTm0xa-xJrmqGkWIw/edit?gid=0#gid=0
      slack: https://apella-workspace.slack.com/archives/C076GFZQ1LP/p1720547650761269
    columns:
      - name: ds
        data_type: "timestamp"
      - name: service_line_name
        data_type: "string"
      - name: block_name
        data_type: "string"
