{{ daily_config() }}

with adding_site_and_days_since_epoch as (
    select
        cud.unexpected_delay_minutes,
        cc.site_id,
        date(cc.scheduled_start_datetime_local) as surgery_date,
        date_diff(date(cc.scheduled_start_datetime_local), '1970-01-01', day) as days_since_epoch
    from {{ ref("case_unexpected_delays") }} as cud
    left outer join {{ ref("core_cases") }} as cc
        on cud.apella_case_id = cc.apella_case_id
    where
        cud.ds = {{ ds() }}
        and cc.ds = {{ ds() }}
        and cc.is_case_matched
),

aggregating_per_site_and_surgery_date as (
    select
        site_id,
        surgery_date,
        days_since_epoch,
        sum(unexpected_delay_minutes) as total_unexpected_delay_minutes,
        count(*) as case_count
    from adding_site_and_days_since_epoch
    group by site_id, surgery_date, days_since_epoch
),

running_28d_total as (
    select
        site_id,
        surgery_date,
        sum(
            total_unexpected_delay_minutes) over
        (
            partition by site_id order by days_since_epoch
            range between 28 preceding and current row
        ) as total_unexpected_delay_minutes_28d,
        sum(
            case_count) over
        (
            partition by site_id order by days_since_epoch
            range between 28 preceding and current row
        ) as case_count_28d
    from aggregating_per_site_and_surgery_date
)

select
    {{ ds() }} as ds,
    site_id,
    surgery_date,
    total_unexpected_delay_minutes_28d,
    case_count_28d,
    total_unexpected_delay_minutes_28d / case_count_28d as avg_unexpected_delay_minutes
from running_28d_total
