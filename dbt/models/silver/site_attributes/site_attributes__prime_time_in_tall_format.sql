{{ daily_config() }}

with site_and_room_configs_combined as (
    -- we have to combine the room and site prime time configs
    select
        -- this is just selecting columns from the room prime time configs. Since we're going to
        -- to do a `union all` with rows from the site, we are explicit about the column names
        -- and order so that both are the same
        rc.room_id,
        rc.sunday_start_time,
        rc.sunday_end_time,
        rc.monday_start_time,
        rc.monday_end_time,
        rc.tuesday_start_time,
        rc.tuesday_end_time,
        rc.wednesday_start_time,
        rc.wednesday_end_time,
        rc.friday_start_time,
        rc.friday_end_time,
        rc.thursday_start_time,
        rc.thursday_end_time,
        rc.saturday_start_time,
        rc.saturday_end_time,
        rc.updated_time
    from {{ api_table_snapshot("bronze", "public_room_prime_time_configs", "rc") }}
    union all
    select
        -- for site prime time configs, we have to join with the rooms table to get the room_id
        r.id as room_id,
        sc.sunday_start_time,
        sc.sunday_end_time,
        sc.monday_start_time,
        sc.monday_end_time,
        sc.tuesday_start_time,
        sc.tuesday_end_time,
        sc.wednesday_start_time,
        sc.wednesday_end_time,
        sc.friday_start_time,
        sc.friday_end_time,
        sc.thursday_start_time,
        sc.thursday_end_time,
        sc.saturday_start_time,
        sc.saturday_end_time,
        sc.updated_time
    from {{ api_table_snapshot("bronze", "public_site_prime_time_configs", "sc") }}
    inner join {{ api_table_snapshot("bronze", "public_rooms", "r") }}
        on sc.site_id = r.site_id
),

dedupe_room as (
    -- in the case where a room is defined through the site_config and the room_config
    -- we'll have two rows with the same room_id, keep the latest one
    select
        room_id,
        min_by(sunday_start_time, updated_time) as sunday_start_time,
        min_by(sunday_end_time, updated_time) as sunday_end_time,
        min_by(monday_start_time, updated_time) as monday_start_time,
        min_by(monday_end_time, updated_time) as monday_end_time,
        min_by(tuesday_start_time, updated_time) as tuesday_start_time,
        min_by(tuesday_end_time, updated_time) as tuesday_end_time,
        min_by(wednesday_start_time, updated_time) as wednesday_start_time,
        min_by(wednesday_end_time, updated_time) as wednesday_end_time,
        min_by(friday_start_time, updated_time) as friday_start_time,
        min_by(friday_end_time, updated_time) as friday_end_time,
        min_by(thursday_start_time, updated_time) as thursday_start_time,
        min_by(thursday_end_time, updated_time) as thursday_end_time,
        min_by(saturday_start_time, updated_time) as saturday_start_time,
        min_by(saturday_end_time, updated_time) as saturday_end_time
    from site_and_room_configs_combined
    group by 1
),

unpivot_start_times as (
    -- original public_room/site_prime_time_configs have many many columns. Pivot it to have just
    -- start_time, end_time, dow and room_id. I can't get the unpivot to work on several columns at
    -- once, therefor we unpivot the `starts` first and then the `ends`
    select *
    from dedupe_room
    unpivot (
        start_time for start_dow in
        (
            sunday_start_time,
            monday_start_time,
            tuesday_start_time,
            wednesday_start_time,
            thursday_start_time,
            friday_start_time,
            saturday_start_time
        )
    )
),

unpivot_end_times as (
    -- unpivot the ends here, this is working off of unpivot_start_times, read the comment there.
    select *
    from unpivot_start_times
    unpivot (
        end_time for end_dow in
        (
            sunday_end_time,
            monday_end_time,
            tuesday_end_time,
            wednesday_end_time,
            thursday_end_time,
            friday_end_time,
            saturday_end_time
        )
    )
),

extracting_dow as (
    -- now replace strings like monday_start_time by `monday` and `monday_end_time` by `monday`
    select
        start_time,
        end_time,
        room_id,
        split(start_dow, '_')[offset(0)] as start_dow,
        split(end_dow, '_')[offset(0)] as end_dow
    from unpivot_end_times
)

select
    {{ ds() }} as ds,
    room_id,
    start_dow as dow_name,
    case
        when start_dow = 'sunday' then 1
        when start_dow = 'monday' then 2
        when start_dow = 'tuesday' then 3
        when start_dow = 'wednesday' then 4
        when start_dow = 'thursday' then 5
        when start_dow = 'friday' then 6
        when start_dow = 'saturday' then 7
    end as dow_int,
    start_time,
    end_time
from extracting_dow
where start_dow = end_dow
