
version: 1

models:
  - name: site_unexpected_delays_28d
    description: >
      Running average over a 28 day window of case_unexpected_delays. See that model for documentation

    columns:
      - name: ds
        data_type: "timestamp"
      - name: site_id
        data_type: "string"
      - name: surgery_date
        data_type: "date"
      - name: total_unexpected_delay_minutes_28d
        data_type: "int"
        tests:
          - not_null
          - accepted_range:
              min_value: 0
      - name: case_count_28d
        data_type: "int"
        tests:
          - not_null
          - accepted_range:
              min_value: 0
      - name: avg_unexpected_delay_minutes
        data_type: "float"
        tests:
          - not_null
          - accepted_range:
              min_value: 0

  - name: site_forward_mobility_28d
    description: >
      Forward mobility running average over 28 day windows
    columns:
      - name: ds
        data_type: "timestamp"
      - name: site_id
        data_type: "string"
      - name: surgery_date
        data_type: "date"
      - name: total_minutes_case_started_before_schedule_28d
        data_type: "int"
        tests:
          - not_null
          - accepted_range:
              min_value: 0
      - name: total_minutes_room_open_before_schedule_28d
        data_type: "int"
        tests:
          - not_null
          - accepted_range:
              min_value: 0
      - name: forward_mobility_28d
        data_type: "float"
        tests:
          - not_null
          - accepted_range:
              min_value: 0

  - name: site_attributes__prime_time_in_tall_format
    description: >
      Pivot public_room_prime_time_configs to go from wide to tall format
    columns:
      - name: ds
        data_type: "timestamp"
      - name: room_id
        data_type: "string"
      - name: dow_name
        data_type: "string"
        description: one of the 7 days of the week, full name in lowercase
      - name: dow_int
        data_type: "int"
        description: Sunday corresponds to 1, Monday is 2, ... Saturday is 7
      - name: start_time
        data_type: "time"
      - name: end_time
        data_type: "time"