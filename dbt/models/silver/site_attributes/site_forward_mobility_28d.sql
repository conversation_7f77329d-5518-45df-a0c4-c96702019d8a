{{ daily_config() }}

with adding_site_and_days_since_epoch as (
    select
        cfw.minutes_room_open_before_schedule,
        cfw.minutes_case_started_before_schedule,
        cc.site_id,
        date(cc.scheduled_start_datetime_local) as surgery_date,
        date_diff(date(cc.scheduled_start_datetime_local), '1970-01-01', day) as days_since_epoch
    from {{ ref("case_forward_mobility") }} as cfw
    left outer join {{ ref("core_cases") }} as cc
        on cfw.apella_case_id = cc.apella_case_id
    where
        cfw.ds = {{ ds() }}
        and cc.ds = {{ ds() }}
),

aggregating_per_site_and_surgery_date as (
    select
        site_id,
        surgery_date,
        days_since_epoch,
        sum(minutes_room_open_before_schedule) as total_minutes_room_open_before_schedule,
        sum(minutes_case_started_before_schedule) as total_minutes_case_started_before_schedule
    from adding_site_and_days_since_epoch
    group by site_id, surgery_date, days_since_epoch
),

running_28d_total as (
    select
        site_id,
        surgery_date,
        sum(
            total_minutes_case_started_before_schedule) over
        (
            partition by site_id order by days_since_epoch
            range between 28 preceding and current row
        ) as total_minutes_case_started_before_schedule_28d,
        sum(
            total_minutes_room_open_before_schedule) over
        (
            partition by site_id order by days_since_epoch
            range between 28 preceding and current row
        ) as total_minutes_room_open_before_schedule_28d
    from aggregating_per_site_and_surgery_date
)

select
    {{ ds() }} as ds,
    site_id,
    surgery_date,
    total_minutes_case_started_before_schedule_28d,
    total_minutes_room_open_before_schedule_28d,
    total_minutes_case_started_before_schedule_28d / total_minutes_room_open_before_schedule_28d as forward_mobility_28d
from running_28d_total
