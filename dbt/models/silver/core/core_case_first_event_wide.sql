{{ daily_config() }}

with in_local_time as (
    -- convert time in tall table to local time
    select
        tall.apella_case_id,
        tall.source_type,
        tall.event_type_id,
        datetime(tall.event_time, c.site_timezone) as event_datetime_local
    from {{ ref("core_case_event_tall") }} as tall
    inner join {{ ref("core_cases") }} as c
        on tall.apella_case_id = c.apella_case_id
    where
        tall.ds = {{ ds() }}
        and c.ds = {{ ds() }}
        and c.is_case_matched
),

case_events_with_prefix as (
    -- append OBX or Apella prefix to event_type_id
    -- this is only for events that are within a wheels_in/out
    select
        apella_case_id,
        event_datetime_local,
        concat(
            if(source_type = 'OBX', 'OBX', 'Apella'),
            '_',
            event_type_id
        ) as event_type_id
    from in_local_time
),

-- in table `core_turnover_events` cols are named from the event point of view (prev/next refer to
-- the apella_case_id). Here we need them backwards, meaning that we have a case_id and we want
-- to refer to the prev_turnover_event or the next_turnover_event.
-- don't get confused about who is prev/next
--
-- In the core_turnover_events
-- prev_apella_case_id       turnver_over_event        next_apella_case_id
-- here
--      apella_case_id       next_turover_event
--                           prev_turover_event            apella_case_id
next_turnover_events as (
    select
        t.prev_apella_case_id as apella_case_id,
        datetime(t.start_time, c.timezone) as event_datetime_local,
        concat('Apella_', t.event_type_id, '_next') as event_type_id
    from {{ ref("core_turnover_events") }} as t
    inner join {{ api_table_snapshot("bronze", "public_sites", "c") }}
        on t.site_id = c.id
    where t.ds = {{ ds() }}
),

prev_turnover_events as (
    select
        t.next_apella_case_id as apella_case_id,
        datetime(t.start_time, c.timezone) as event_datetime_local,
        concat('Apella_', t.event_type_id, '_prev') as event_type_id
    from {{ ref("core_turnover_events") }} as t
    inner join {{ api_table_snapshot("bronze", "public_sites", "c") }}
        on t.site_id = c.id
    where t.ds = {{ ds() }}
),

all_events as (
    select
        apella_case_id,
        event_datetime_local,
        event_type_id
    from case_events_with_prefix
    union all
    select
        apella_case_id,
        event_datetime_local,
        event_type_id
    from next_turnover_events
    union all
    select
        apella_case_id,
        event_datetime_local,
        event_type_id
    from prev_turnover_events
)

select
    {{ ds() }} as ds,
    *
from all_events
pivot (min(event_datetime_local) for event_type_id in (
    -- these are all the events we are considering from the prev turnover
    'Apella_anesthesia_cart_workstation_cleaning_start_prev',
    'Apella_anesthesia_machine_cleaning_start_prev',
    'Apella_anesthesia_medication_station_cleaning_start_prev',
    'Apella_back_table_cleaning_start_prev',
    'Apella_back_table_cleared_prev',
    'Apella_back_table_open_prev',
    'Apella_boom_mounted_monitors_cleaning_start_prev',
    'Apella_boom_mounted_overhead_light_cleaning_start_prev',
    'Apella_case_cart_in_prev',
    'Apella_case_cart_out_prev',
    'Apella_ceiling_cleaning_start_prev',
    'Apella_chair_cleaning_start_prev',
    'Apella_cleaning_crew_in_prev',
    'Apella_cleaning_crew_out_prev',
    'Apella_cleaning_end_prev',
    'Apella_cleaning_start_prev',
    'Apella_da_vinci_instrument_opening_prev',
    'Apella_da_vinci_instrument_use_prev',
    'Apella_da_vinci_robot_cleaning_start_prev',
    'Apella_da_vinci_surgeon_console_cleaning_start_prev',
    'Apella_da_vinci_tray_opening_prev',
    'Apella_door_to_the_core_cleaning_start_prev',
    'Apella_endo_pack_open_prev',
    'Apella_equipment_cleaning_start_prev',
    'Apella_floor_cleaning_start_prev',
    'Apella_floor_mopping_prev',
    'Apella_furniture_cleaning_start_prev',
    'Apella_instrument_tray_opening_prev',
    'Apella_lights_off_prev',
    'Apella_lights_on_prev',
    'Apella_mayo_stand_cleaning_start_prev',
    'Apella_mop_in_prev',
    'Apella_mop_out_prev',
    'Apella_mop_out_turnover_prev',
    'Apella_mopping_floor_start_prev',
    'Apella_no_mop_prev',
    'Apella_nursing_fixed_work_station_cleaning_start_prev',
    'Apella_nursing_mobile_workstation_cleaning_start_prev',
    'Apella_nursing_station_cleaning_start_prev',
    'Apella_or_lights_and_booms_cleaning_start_prev',
    'Apella_or_table_cleaning_start_prev',
    'Apella_or_table_ready_prev',
    'Apella_post_operative_prev',
    'Apella_pre_operative_prev',
    'Apella_prep_table_cleaning_start_prev',
    'Apella_robot_drape_end_prev',
    'Apella_robot_drape_start_prev',
    'Apella_room_ready_prev',
    'Apella_setup_end_prev',
    'Apella_setup_start_prev',
    'Apella_sterile_pack_on_back_table_prev',
    'Apella_sweeping_floor_start_start_prev',
    'Apella_terminal_clean_end_prev',
    'Apella_terminal_clean_start_prev',
    'Apella_trash_out_prev',
    'Apella_turn_over_clean_prev',
    'Apella_turn_over_idle_prev',
    'Apella_turn_over_open_prev',
    'Apella_under_OR_equipment_cleaning_start_prev',
    'Apella_under_OR_table_pads_cleaning_start_prev',
    'Apella_wall_mopping_prev',
    'Apella_waste_can_cleaning_start_prev',
    -- these are all the events we are considering from the next turnover
    'Apella_anesthesia_cart_workstation_cleaning_start_next',
    'Apella_anesthesia_machine_cleaning_start_next',
    'Apella_anesthesia_medication_station_cleaning_start_next',
    'Apella_back_table_cleaning_start_next',
    'Apella_back_table_cleared_next',
    'Apella_back_table_open_next',
    'Apella_boom_mounted_monitors_cleaning_start_next',
    'Apella_boom_mounted_overhead_light_cleaning_start_next',
    'Apella_case_cart_in_next',
    'Apella_case_cart_out_next',
    'Apella_ceiling_cleaning_start_next',
    'Apella_chair_cleaning_start_next',
    'Apella_cleaning_crew_in_next',
    'Apella_cleaning_crew_out_next',
    'Apella_cleaning_end_next',
    'Apella_cleaning_start_next',
    'Apella_da_vinci_instrument_opening_next',
    'Apella_da_vinci_instrument_use_next',
    'Apella_da_vinci_robot_cleaning_start_next',
    'Apella_da_vinci_surgeon_console_cleaning_start_next',
    'Apella_da_vinci_tray_opening_next',
    'Apella_door_to_the_core_cleaning_start_next',
    'Apella_endo_pack_open_next',
    'Apella_equipment_cleaning_start_next',
    'Apella_floor_cleaning_start_next',
    'Apella_floor_mopping_next',
    'Apella_furniture_cleaning_start_next',
    'Apella_instrument_tray_opening_next',
    'Apella_lights_off_next',
    'Apella_lights_on_next',
    'Apella_mayo_stand_cleaning_start_next',
    'Apella_mop_in_next',
    'Apella_mop_out_next',
    'Apella_mop_out_turnover_next',
    'Apella_mopping_floor_start_next',
    'Apella_no_mop_next',
    'Apella_nursing_fixed_work_station_cleaning_start_next',
    'Apella_nursing_mobile_workstation_cleaning_start_next',
    'Apella_nursing_station_cleaning_start_next',
    'Apella_or_lights_and_booms_cleaning_start_next',
    'Apella_or_table_cleaning_start_next',
    'Apella_or_table_ready_next',
    'Apella_post_operative_next',
    'Apella_pre_operative_next',
    'Apella_prep_table_cleaning_start_next',
    'Apella_robot_drape_end_next',
    'Apella_robot_drape_start_next',
    'Apella_room_ready_next',
    'Apella_setup_end_next',
    'Apella_setup_start_next',
    'Apella_sterile_pack_on_back_table_next',
    'Apella_sweeping_floor_start_start_next',
    'Apella_terminal_clean_end_next',
    'Apella_terminal_clean_start_next',
    'Apella_trash_out_next',
    'Apella_turn_over_clean_next',
    'Apella_turn_over_idle_next',
    'Apella_turn_over_open_next',
    'Apella_under_OR_equipment_cleaning_start_next',
    'Apella_under_OR_table_pads_cleaning_start_next',
    'Apella_wall_mopping_next',
    'Apella_waste_can_cleaning_start_next',
    -- these are the Apella events we generate that are associated with a case_id
    'Apella_anesthesia_draping',
    'Apella_anesthesia_ready',
    'Apella_anesthesia_undraping',
    'Apella_back_table_cleared',
    'Apella_back_table_open',
    'Apella_case_cart_in',
    'Apella_case_cart_out',
    'Apella_endo_pack_open',
    'Apella_intubation',
    'Apella_patient_not_intubated',
    'Apella_lights_off',
    'Apella_lights_on',
    'Apella_mop_in',
    'Apella_mop_out',
    'Apella_no_case_cart',
    'Apella_no_patient',
    'Apella_patient_draped',
    'Apella_patient_on_hospital_bed',
    'Apella_patient_on_or_table',
    'Apella_patient_undraped',
    'Apella_patient_wheels_in',
    'Apella_patient_wheels_out',
    'Apella_patient_xfer_to_bed',
    'Apella_patient_xfer_to_or_table',
    'Apella_post_operative',
    'Apella_pre_operative',
    'Apella_sterile_pack_on_back_table',
    'Apella_surgery',
    'Apella_terminal_clean_end',
    'Apella_terminal_clean_start',
    'Apella_turn_over_clean',
    'Apella_turn_over_idle',
    'Apella_turn_over_open',
    -- these are the OBX events
    'OBX_OBSERVED_ANESTHESIA_AVAILABLE',
    'OBX_OBSERVED_ANESTHESIA_FINISH',
    'OBX_OBSERVED_ANESTHESIA_READY',
    'OBX_OBSERVED_ANESTHESIA_START',
    'OBX_OBSERVED_AT_CECUM',
    'OBX_OBSERVED_CASE_CLOSING',
    'OBX_OBSERVED_CASE_FINISH',
    'OBX_OBSERVED_CASE_START',
    'OBX_OBSERVED_CATHLAB_TO_OR',
    'OBX_OBSERVED_CLEANUP_COMPLETE',
    'OBX_OBSERVED_CLEANUP_START',
    'OBX_OBSERVED_DECISION_TIME',
    'OBX_OBSERVED_DISCHARGE_CRITERIA_MET',
    'OBX_OBSERVED_EPIDURAL_TO_C_SECTION',
    'OBX_OBSERVED_HYSTEROTOMY',
    'OBX_OBSERVED_INDUCTION',
    'OBX_OBSERVED_IN_EXTENDED_RECOVERY',
    'OBX_OBSERVED_IN_FACILITY',
    'OBX_OBSERVED_IN_HOLDING_AREA',
    'OBX_OBSERVED_IN_PACU',
    'OBX_OBSERVED_IN_PHASE_II',
    'OBX_OBSERVED_IN_PRE_PROCEDURE',
    'OBX_OBSERVED_IN_PROCEDURAL_RECOVERY',
    'OBX_OBSERVED_IN_ROOM',
    'OBX_OBSERVED_OPNOTE_VERIFIED',
    'OBX_OBSERVED_OUT_OF_EXTENDED_RECOVERY',
    'OBX_OBSERVED_OUT_OF_HOLDING_AREA',
    'OBX_OBSERVED_OUT_OF_PACU',
    'OBX_OBSERVED_OUT_OF_PACU_2ND_TIME',
    'OBX_OBSERVED_OUT_OF_PACU_3RD_TIME',
    'OBX_OBSERVED_OUT_OF_PHASE_II',
    'OBX_OBSERVED_OUT_OF_PHASE_II_2ND_TIME',
    'OBX_OBSERVED_OUT_OF_PRE_PROCEDURE',
    'OBX_OBSERVED_OUT_OF_PROCEDURAL_RECOVERY',
    'OBX_OBSERVED_OUT_OF_ROOM',
    'OBX_OBSERVED_PATIENT_MOVED_TO_ANOTHER_OR_ROOM',
    'OBX_OBSERVED_PATIENT_SENT_FOR',
    'OBX_OBSERVED_PATIENT_TRANSFER_TO_HOSPITAL_ROOM',
    'OBX_OBSERVED_PHASE_II_CARE_COMPLETE',
    'OBX_OBSERVED_PHYSICIAN_AVAILABLE',
    'OBX_OBSERVED_PREP_COMPLETE',
    'OBX_OBSERVED_PREP_START',
    'OBX_OBSERVED_PRE_PROCEDURE_COMPLETE',
    'OBX_OBSERVED_PROCEDURAL_CARE_COMPLETE',
    'OBX_OBSERVED_PROCEDURE_FINISH',
    'OBX_OBSERVED_RETURN_TO_OR',
    'OBX_OBSERVED_RETURN_TO_PACU',
    'OBX_OBSERVED_RETURN_TO_PACU_3RD_TIME',
    'OBX_OBSERVED_RETURN_TO_PHASE_II',
    'OBX_OBSERVED_SEDATION_END',
    'OBX_OBSERVED_SEDATION_START',
    'OBX_OBSERVED_SETUP_COMPLETE',
    'OBX_OBSERVED_SETUP_START',
    'OBX_OBSERVED_TIMEOUT_ANESTHESIA',
    'OBX_OBSERVED_TIMEOUT_DEBRIEF',
    'OBX_OBSERVED_TIMEOUT_FIRE_SAFETY',
    'OBX_OBSERVED_TIMEOUT_PREINCISION',
    'OBX_OBSERVED_TIMEOUT_PREPROCEDURE',
    'OBX_OBSERVED_TO_PHASE_II'
))
