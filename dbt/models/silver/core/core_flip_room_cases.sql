{{ daily_config() }}

select
    {{ ds() }} as ds,
    cc.apella_case_id,
    cc.site_id,
    cc.room_id,
    s_iou.staff_id,
    s_iou.case_date,
    cc.scheduled_start_datetime_local,
    cc.scheduled_end_datetime_local,
    cc.actual_start_datetime_local,
    cc.actual_end_datetime_local,
    row_number()
        over (partition by s_iou.case_date, s_iou.staff_id, cc.room_id order by cc.scheduled_start_datetime_local)
        as case_order_in_room,
    row_number()
        over (partition by s_iou.case_date, s_iou.staff_id, cc.site_id order by cc.scheduled_start_datetime_local)
        as case_order_in_site
from {{ ref("staff_intersection_over_union") }} as s_iou
left outer join {{ ref("core_cases") }} as cc
    on
        cc.room_id in unnest(s_iou.room_ids)
        and date(cc.scheduled_start_datetime_local) = s_iou.case_date
-- We're joining on core_case_staff to filter results to only primary surgeons (staff_intersection_over_union
-- is not limited to surgeons). We join on apella_case_id and staff_id and enforce `is_candidate_primary_surgeon` is
-- true. We are not adding any columns from core_case_staff
left outer join {{ ref("core_case_staff") }} as ccs
    on
        cc.apella_case_id = ccs.apella_case_id
        and s_iou.staff_id = ccs.apella_staff_id
where
    s_iou.ds = {{ ds() }}
    and cc.ds = {{ ds() }}
    and ccs.ds = {{ ds() }}
    -- next line removes overlap in same room to focus on flip room application here
    and array_length(s_iou.room_ids) > 1
    and ccs.is_candidate_primary_surgeon = true
    and cc.is_case_matched
