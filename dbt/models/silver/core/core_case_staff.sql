{{ daily_config(produce_latest_snapshot=True) }}

select
    {{ ds() }} as ds,
    case_staff.created_time as api_created_timestamp_utc,
    case_staff.updated_time as api_updated_timestamp_utc,
    case_staff.staff_id as apella_staff_id,
    staff.external_staff_id as customer_staff_id,
    case_staff.case_id as apella_case_id,
    case_staff.role,
    staff.first_name,
    staff.last_name,
    staff.org_id,
    case_staff.role in unnest([
        'Primary Surgeon',
        'Primary',
        'Surgeon 1',
        'Surgeon',
        'Physician'
    ]) as is_candidate_primary_surgeon
from {{ api_table_snapshot("bronze", "public_case_staff", "case_staff") }}
inner join {{ api_table_snapshot("bronze", "public_staff", "staff") }}
    on case_staff.staff_id = staff.id
where
    case_staff.case_id is not null
    and case_staff.archived_time is null
    and staff.archived_time is null
