{{ daily_config() }}


with core_cases_in_utc as (
    select
        org_id,
        site_id,
        room_id,
        apella_case_id,
        actual_end_datetime_local,
        timestamp(actual_start_datetime_local, site_timezone) as actual_start_timestamp_utc,
        timestamp(actual_end_datetime_local, site_timezone) as actual_end_timestamp_utc
    from {{ ref("core_cases") }}
    where
        ds = {{ ds() }}
        and is_case_matched
),

next_case_and_prev_case as (
    select
        apella_case_id as next_apella_case_id,
        actual_start_timestamp_utc as next_case_start,
        actual_end_timestamp_utc as next_case_end,
        org_id,
        site_id,
        room_id,
        lag(apella_case_id)
            over (
                partition by org_id, site_id, room_id
                order by actual_start_timestamp_utc asc
            )
            as prev_apella_case_id,
        lag(actual_start_timestamp_utc)
            over (
                partition by org_id, site_id, room_id
                order by actual_start_timestamp_utc asc
            )
            as prev_case_start,
        lag(actual_end_timestamp_utc)
            over (
                partition by org_id, site_id, room_id
                order by actual_start_timestamp_utc asc
            )
            as prev_case_end
    from core_cases_in_utc
)

select
    {{ ds() }} as ds,
    b.id as event_id,
    b.org_id,
    b.site_id,
    b.room_id,
    b.start_time,
    b.event_type_id,
    b.source_type,
    b.version,
    a.next_apella_case_id,
    a.prev_apella_case_id
from next_case_and_prev_case as a
inner join {{ api_table_snapshot("bronze", "public_events", "b") }}
    on
        a.org_id = b.org_id
        and a.site_id = b.site_id
        and a.room_id = b.room_id
        and a.prev_case_end < b.start_time
        and a.next_case_start > b.start_time
where b.source_type in ('human_gt', 'prediction')
