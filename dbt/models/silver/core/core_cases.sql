{{ daily_config(produce_latest_snapshot=True) }}

with core_case_phases as (
    select
        apella_case_id,
        org_id,
        room_id,
        room_name,
        site_id,
        site_name,
        site_timezone,
        phase_id,
        phase_start_datetime_local,
        phase_end_datetime_local,
        phase_duration_minutes,
        api_created_timestamp_utc,
        api_updated_timestamp_utc
    from {{ ref("core_case_phases") }}
    where
        ds = {{ ds() }}
        and phase_type_id = 'CASE'
),

core_case_phases_deduped as (
    select *
    from core_case_phases
    where apella_case_id is null

    union all

    -- If case matched, then use the latest CASE phase.
    select *
    from core_case_phases
    where apella_case_id is not null
    qualify row_number() over (
        partition by apella_case_id
        order by api_updated_timestamp_utc desc
    ) = 1
)

select
    {{ ds() }} as ds,
    coalesce(cases.org_id, phases.org_id) as org_id,
    orgs.name as org_name,
    coalesce(scheduled_sites.timezone, phases.site_timezone) as site_timezone,
    (cases.case_id is not null and phases.phase_id is not null) as is_case_matched,

    -- Scheduled
    cases.case_id as apella_case_id,
    cases.external_case_id as customer_case_id,
    cases.site_id as site_id,
    scheduled_sites.name as site_name,
    cases.room_id as room_id,
    rooms.name as room_name,
    cases.service_line_id,
    service_line.name as service_line_name,
    case_derived_props.is_in_flip_room,
    case_derived_props.is_first_case,
    cases.is_add_on,
    datetime(cases.scheduled_start_time, scheduled_sites.timezone) as scheduled_start_datetime_local,
    datetime(cases.scheduled_end_time, scheduled_sites.timezone) as scheduled_end_datetime_local,
    -- Use round(seconds/60) to ensure we are rounding to the closest minute, and not just
    -- discarding the seconds.
    cast(round(timestamp_diff(
        cases.scheduled_end_time,
        cases.scheduled_start_time,
        second
    ) / 60) as int64) as scheduled_duration_minutes,
    cases.status,
    cases.patient_class as patient_classification_id,
    case
        when cases.patient_class = 'INPATIENT' then 'Inpatient'
        when cases.patient_class = 'EMERGENCY' then 'Emergency'
        when cases.patient_class = 'HOSPITAL_OUTPATIENT_SURGERY' then 'Outpatient'
        when cases.patient_class = 'PRE_ADMIT' then 'Pre Admit'
        when cases.patient_class = 'SURGERY_ADMIT' then 'Surgery Admit'
        when cases.patient_class = 'OBSERVATION' then 'Observation'
    end as patient_classification_name,
    cases.case_classification_types_id as case_classification_id,
    case_class_types.name as case_classification_name,
    cases.created_time as scheduled_api_created_timestamp_utc,
    cases.updated_time as scheduled_api_updated_timestamp_utc,

    -- Actual
    phases.phase_id as case_phase_id,
    phases.site_id as actual_site_id,
    phases.site_name as actual_site_name,
    phases.room_id as actual_room_id,
    phases.room_name as actual_room_name,
    phases.phase_duration_minutes as actual_duration_minutes,
    phases.phase_start_datetime_local as actual_start_datetime_local,
    phases.phase_end_datetime_local as actual_end_datetime_local,
    phases.api_created_timestamp_utc as actual_api_created_timestamp_utc,
    phases.api_updated_timestamp_utc as actual_api_updated_timestamp_utc

from {{ api_table_snapshot("bronze", "public_cases", "cases") }}
full outer join core_case_phases_deduped as phases
    on cases.case_id = phases.apella_case_id
left outer join {{ api_table_snapshot("bronze", "public_case_classification_types", "case_class_types") }}
    on
        cases.case_classification_types_id = case_class_types.id
        and cases.org_id = case_class_types.org_id
left outer join {{ api_table_snapshot("bronze", "public_sites", "scheduled_sites") }}
    on cases.site_id = scheduled_sites.id
left outer join {{ api_table_snapshot("bronze", "public_rooms", "rooms") }}
    on cases.room_id = rooms.id
left outer join {{ api_table_snapshot("bronze", "public_organizations", "orgs") }}
    on coalesce(cases.org_id, phases.org_id) = orgs.id
left outer join {{ api_table_snapshot("bronze", "public_service_lines", "service_line") }}
    on
        cases.org_id = service_line.org_id
        and cases.service_line_id = service_line.id
left outer join {{ api_table_snapshot("bronze", "public_case_derived_properties", "case_derived_props") }}
    on cases.case_id = case_derived_props.case_id
