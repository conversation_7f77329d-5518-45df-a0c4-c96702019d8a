{{ daily_config() }}

with case_features as (
    -- TODO: ideally, forecasting_case_features_combined will be
    --  replaced with a more core cases table
    select
        site_id,
        room,
        case_id,
        date_of_surgery,
        scheduled_duration,
        actual_duration,
        scheduled_start_datetime_local,
        first_primary_surgeon,
        first_primary_surgeon_id,
        add_on,
        timestamp_add(
            scheduled_start_datetime_local, interval scheduled_duration minute
        ) as scheduled_end_datetime_local,
        timestamp_add(
            scheduled_start_datetime_local, interval start_offset minute
        ) as actual_start_datetime_local,
        timestamp_add(
            scheduled_start_datetime_local,
            interval start_offset + actual_duration minute
        ) as actual_end_datetime_local
    from {{ ref("forecasting_case_features_combined") }}
    -- we only have patient draped/undraped events for apella data
    where
        ds = {{ ds() }}
        and apella_data = 1
),

max_created_drape_events as (
    select
        case_id,
        event_type_id,
        event_start_datetime_local,
        event_created_time_inv
    from (
        select
            case_events.case_id,
            case_events.event_type_id,
            datetime(case_events.start_time, sites.timezone)
                as event_start_datetime_local,
            -- TODO: confirm that max created_time is same as max version
            row_number()
                over (
                    partition by case_events.case_id, case_events.event_type_id
                    order by case_events.created_time desc
                )
                as event_created_time_inv
        from {{ ref("case_events") }} as case_events
        inner join {{ source("bronze", "public_sites") }} as sites
            on sites.id = case_events.site_id
        where
            case_events.ds = {{ ds() }}
            and case_events.event_type_id in (
                'patient_draped', 'patient_undraped'
            )
    )
    where event_created_time_inv = 1
),

-- part 1 of flip room definition: must have at least
-- two of the same surgeon in same or-day
-- begin by counting the number of cases a surgeon has in each or-day
or_days_with_duplicate_surgeons as (
    select
        site_id,
        room,
        date_of_surgery,
        first_primary_surgeon,
        first_primary_surgeon_id,
        count(distinct case_id) as num_cases_surgeon_room,
        sum(scheduled_duration) as total_scheduled_duration_surgeon_room,
        min(scheduled_start_datetime_local)
            as earliest_sched_start_surgeon_room,
        max(scheduled_end_datetime_local) as latest_sched_end_surgeon_room
    from case_features
    group by
        site_id,
        room,
        date_of_surgery,
        first_primary_surgeon,
        first_primary_surgeon_id
    -- don't include surgeons who schedule their first case past 5
    having min(extract(hour from scheduled_start_datetime_local)) < 17
),

-- ensure at least 2 cases per surgeon in the OR-day
-- assign the "main surgeon" per OR-day, but we still have
-- to tie the OR-days into flip room blocks
main_surgeon as (

    select
        site_id,
        room,
        date_of_surgery,
        first_primary_surgeon as main_surgeon_in_or_day,
        first_primary_surgeon_id as main_surgeon_in_or_day_id,
        num_cases_surgeon_room,
        total_scheduled_duration_surgeon_room
    from (
        select
            site_id,
            room,
            date_of_surgery,
            first_primary_surgeon,
            first_primary_surgeon_id,
            num_cases_surgeon_room,
            total_scheduled_duration_surgeon_room,
            earliest_sched_start_surgeon_room,
            latest_sched_end_surgeon_room,
            row_number() over (
                partition by site_id, room, date_of_surgery
                -- assign main surgeon as the one with most cases.
                -- use total duration as tiebreaker if needed
                order by
                    num_cases_surgeon_room desc,
                    total_scheduled_duration_surgeon_room desc
            ) as order_most_freq_surgeon
        from or_days_with_duplicate_surgeons
        where num_cases_surgeon_room >= 2
    )
    where order_most_freq_surgeon = 1

),

-- part 2 of flip room definition:
-- to count as a "flip room block", there must be multiple
-- rooms in the same date that have the same main surgeon.
-- here, identify the surgeons and dates that match this criteria
surgeons_with_flip_block as (
    select
        site_id,
        date_of_surgery,
        main_surgeon_in_or_day,
        count(distinct room) as num_rooms_for_main_surgeon

    from main_surgeon
    group by
        site_id,
        date_of_surgery,
        main_surgeon_in_or_day
    having count(distinct room) >= 2
),

-- now we need the OR associated with the flip room blocks
-- need the OR because we want to get ALL cases that happened
-- in a flip room block, not just cases belonging to the
-- main_surgeon_in_or_day so, it's not enough to just filter
-- on the cases with first_primary_surgeon
-- equal to the main_surgeon_in_or_day
flip_or_days as (
    select
        room,
        date_of_surgery,
        main_surgeon_in_or_day,
        total_scheduled_duration_for_main_surgeon_in_room,
        earliest_sched_start_surgeon_room,
        latest_sched_end_surgeon_room,
        flip_block_name,
        row_number() over (
            partition by date_of_surgery, main_surgeon_in_or_day
            order by total_scheduled_duration_for_main_surgeon_in_room desc
        ) as room_order_in_flip_block
    from (
        select distinct
            or_day_with_dup.room,
            surgeons_with_flip_block.date_of_surgery,
            surgeons_with_flip_block.main_surgeon_in_or_day,
            or_day_with_dup.total_scheduled_duration_surgeon_room
                as total_scheduled_duration_for_main_surgeon_in_room,
            or_day_with_dup.earliest_sched_start_surgeon_room,
            or_day_with_dup.latest_sched_end_surgeon_room,
            concat(
                surgeons_with_flip_block.site_id, '_',
                surgeons_with_flip_block.main_surgeon_in_or_day, '_',
                surgeons_with_flip_block.date_of_surgery
            ) as flip_block_name
        from surgeons_with_flip_block
        inner join or_days_with_duplicate_surgeons as or_day_with_dup
            on
                surgeons_with_flip_block.date_of_surgery
                = or_day_with_dup.date_of_surgery
                and surgeons_with_flip_block.main_surgeon_in_or_day
                = or_day_with_dup.first_primary_surgeon
        where or_day_with_dup.num_cases_surgeon_room >= 2
    )
),

-- calculate whether the rooms in a flip room block have
-- overlapping scheduled times
scheduled_stretch_across_flip_block as (
    select
        flip_block_name,
        min(flip_or_days.latest_sched_end_surgeon_room)
            as earliest_sched_end_in_flip_block,
        max(flip_or_days.earliest_sched_start_surgeon_room)
            as latest_sched_start_in_flip_block,
        timestamp_diff(
            min(flip_or_days.latest_sched_end_surgeon_room),
            max(flip_or_days.earliest_sched_start_surgeon_room),
            minute
        ) > 0 as scheduled_time_for_main_surgeon_overlaps
    from flip_or_days
    group by flip_block_name
),

-- find all the cases that are in flip room block OR-days
-- these cases will include surgeons who do not "own" that flip room block
cases_in_flip_blocks as (

    select
        {{ ds() }} as ds,
        case_features.site_id,
        case_features.room,
        case_features.date_of_surgery,
        case_features.scheduled_start_datetime_local,
        case_features.scheduled_end_datetime_local,
        case_features.case_id,
        case_features.first_primary_surgeon as case_first_primary_surgeon,
        case_features.first_primary_surgeon_id as case_first_primary_surgeon_id,
        flip_or_days.main_surgeon_in_or_day as flip_block_main_surgeon,
        flip_or_days.total_scheduled_duration_for_main_surgeon_in_room,
        flip_or_days.earliest_sched_start_surgeon_room
            as earliest_sched_start_for_main_surgeon_in_room,
        flip_or_days.latest_sched_end_surgeon_room
            as latest_sched_end_for_main_surgeon_in_room,
        scheduled_stretch.scheduled_time_for_main_surgeon_overlaps,
        flip_or_days.room_order_in_flip_block,
        flip_or_days.flip_block_name,
        case_features.actual_duration,
        case_features.actual_start_datetime_local,
        case_features.actual_end_datetime_local,
        patient_draped_events.event_start_datetime_local
            as patient_draped_datetime_local,
        patient_undraped_events.event_start_datetime_local
            as patient_undraped_datetime_local,
        lag(case_features.case_id)
            over (
                partition by flip_or_days.flip_block_name
                order by case_features.scheduled_start_datetime_local
            )
            as case_id_for_prev_case_in_flip_block,
        lag(case_features.first_primary_surgeon)
            over (
                partition by flip_or_days.flip_block_name
                order by case_features.scheduled_start_datetime_local
            )
            as first_primary_surgeon_for_prev_case_in_flip_block,
        row_number()
            over (
                partition by flip_or_days.flip_block_name
                order by case_features.scheduled_start_datetime_local
            )
            as case_order_in_flip_block,
        timestamp_diff(
            timestamp(patient_draped_events.event_start_datetime_local),
            lag(timestamp(patient_undraped_events.event_start_datetime_local))
                over (
                    partition by flip_or_days.flip_block_name
                    order by case_features.scheduled_start_datetime_local
                ), minute
        )
            as duration_undrape_of_prev_case_in_flip_block_to_drape,
        timestamp_diff(
            timestamp(case_features.scheduled_start_datetime_local),
            lag(timestamp(
                case_features.scheduled_end_datetime_local
            ))
                over (
                    partition by flip_or_days.flip_block_name
                    order by case_features.scheduled_start_datetime_local
                ), minute
        )
            as duration_sched_end_of_prev_case_in_flip_block_to_sched_start,
        case_features.add_on,
        lag(case_features.room) over (
            partition by
                case_features.site_id,
                case_features.first_primary_surgeon,
                case_features.date_of_surgery
            order by case_features.scheduled_start_datetime_local
        ) as case_surgeon_previous_room
    from case_features
    inner join flip_or_days
        on
            case_features.date_of_surgery = flip_or_days.date_of_surgery
            and case_features.room = flip_or_days.room
    inner join scheduled_stretch_across_flip_block as scheduled_stretch
        on
            flip_or_days.flip_block_name
            = scheduled_stretch.flip_block_name
    left outer join max_created_drape_events as patient_draped_events
        on
            case_features.case_id = patient_draped_events.case_id
            and patient_draped_events.event_type_id = 'patient_draped'
    left outer join max_created_drape_events as patient_undraped_events
        on
            case_features.case_id = patient_undraped_events.case_id
            and patient_undraped_events.event_type_id = 'patient_undraped'

)

select
    *,
    coalesce(room != case_surgeon_previous_room, false)
        as case_surgeon_changed_room
from cases_in_flip_blocks
