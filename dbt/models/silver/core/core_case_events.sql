{{ daily_config(produce_latest_snapshot=True) }}

select
    {{ ds() }} as ds,
    e.id as event_id,
    e.start_time as event_time,
    cc.org_id,
    cc.site_id,
    cc.room_id,
    cc.apella_case_id as case_id,
    e.source_type,
    e.source,
    e.event_type_id,
    e.created_time as event_created_time,
    e.updated_time as event_updated_time
from {{ ref("core_cases") }} as cc
inner join {{ api_table_snapshot("bronze", "public_events", "e") }}
    on
        cc.org_id = e.org_id
        and cc.site_id = e.site_id
        and cc.room_id = e.room_id
where
    cc.ds = {{ ds() }}
    and e.deleted_at is null
    and e.source_type in ('human_gt', 'prediction')
    and (
        e.start_time between timestamp(cc.actual_start_datetime_local, cc.site_timezone)
        and timestamp(cc.actual_end_datetime_local, cc.site_timezone)
    )

union all

select
    {{ ds() }} as ds,
    obx.id as event_id,
    obx.observation_time as event_time,
    cc.org_id,
    cc.site_id,
    cc.room_id,
    cc.apella_case_id as case_id,
    'ehr' as source_type,
    'ehr' as `source`,
    obx.type_id as event_type_id,
    obx.created_time as event_created_time,
    obx.updated_time as event_updated_time
from {{ ref("core_cases") }} as cc
inner join {{ api_table_snapshot("bronze", "public_observations", "obx") }}
    on cc.apella_case_id = obx.case_id
where cc.ds = {{ ds() }}
