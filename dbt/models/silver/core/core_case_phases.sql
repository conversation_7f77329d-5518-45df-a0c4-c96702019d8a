{{ daily_config(produce_latest_snapshot=True) }}

with all_phases as (
    select
        phases.id as phase_id,
        phases.case_id as apella_case_id,
        phases.org_id,
        phases.room_id as room_id,
        rooms.name as room_name,
        sites.id as site_id,
        sites.name as site_name,
        sites.timezone as site_timezone,
        phases.type_id as phase_type_id,
        start_events.id as start_event_id,
        end_events.id as end_event_id,
        start_events.start_time as phase_start_timestamp_utc,
        end_events.start_time as phase_end_timestamp_utc,
        phases.created_time as api_created_timestamp_utc,
        phases.updated_time as api_updated_timestamp_utc,
        -- Use round(seconds/60) to ensure we are rounding to the closest minute, and not just
        -- discarding the seconds.
        cast(round(timestamp_diff(
            end_events.start_time,
            start_events.start_time,
            second
        ) / 60) as int64) as phase_duration_minutes,
        extract(date from datetime(start_events.start_time, sites.timezone))
            as phase_date_local,
        datetime(start_events.start_time, sites.timezone)
            as phase_start_datetime_local,
        datetime(end_events.start_time, sites.timezone)
            as phase_end_datetime_local
    from {{ api_table_snapshot("bronze", "public_phases", alias="phases") }}
    inner join
        {{ api_table_snapshot("bronze", "public_events", alias="start_events") }}
        on phases.start_event_id = start_events.id
    inner join {{ api_table_snapshot("bronze", "public_events", alias="end_events") }}
        on phases.end_event_id = end_events.id
    inner join {{ api_table_snapshot("bronze", "public_sites", alias="sites") }}
        on phases.site_id = sites.id
    inner join {{ api_table_snapshot("bronze", "public_rooms", "rooms") }}
        on phases.room_id = rooms.id
    left outer join {{ api_table_snapshot("bronze", "public_cases", "cases") }}
        on phases.case_id = cases.case_id
    where
        -- TODO(DATA-3472): Filter out phases that are part of canceled cases as these are erroneous
        (cases.status != 'canceled' or cases.status is null)
        and phases.source_type = 'unified'
        and start_events.deleted_at is null
        and end_events.deleted_at is null
        and phases.status = 'VALID'
        and phases.room_id is not null
),

parent_case_phases as (
    select *
    from all_phases
    where
        phase_type_id = 'CASE'
),

child_case_phases as (
    select
        all_phases.phase_id,
        all_phases.org_id,
        all_phases.room_id,
        all_phases.room_name,
        all_phases.site_id,
        all_phases.site_name,
        all_phases.site_timezone,
        all_phases.phase_type_id,
        all_phases.start_event_id,
        all_phases.end_event_id,
        all_phases.phase_start_timestamp_utc,
        all_phases.phase_end_timestamp_utc,
        all_phases.phase_date_local,
        all_phases.phase_start_datetime_local,
        all_phases.phase_end_datetime_local,
        all_phases.phase_duration_minutes,
        parent_case_phases.phase_id as parent_case_phase_id,
        parent_case_phases.apella_case_id,
        all_phases.api_created_timestamp_utc,
        all_phases.api_updated_timestamp_utc
    from all_phases
    inner join {{ api_table_snapshot("bronze", "public_phase_relationships", "phase_relationships") }}
        on all_phases.phase_id = phase_relationships.child_phase_id
    inner join parent_case_phases
        on phase_relationships.parent_phase_id = parent_case_phases.phase_id
)

select
    {{ ds() }} as ds,
    *
from (
    select
        phase_id,
        org_id,
        room_id,
        room_name,
        site_id,
        site_name,
        site_timezone,
        phase_type_id,
        start_event_id,
        end_event_id,
        phase_start_timestamp_utc,
        phase_end_timestamp_utc,
        phase_date_local,
        phase_start_datetime_local,
        phase_end_datetime_local,
        phase_duration_minutes,
        null as parent_case_phase_id,
        apella_case_id,
        api_created_timestamp_utc,
        api_updated_timestamp_utc
    from parent_case_phases

    union all

    select
        phase_id,
        org_id,
        room_id,
        room_name,
        site_id,
        site_name,
        site_timezone,
        phase_type_id,
        start_event_id,
        end_event_id,
        phase_start_timestamp_utc,
        phase_end_timestamp_utc,
        phase_date_local,
        phase_start_datetime_local,
        phase_end_datetime_local,
        phase_duration_minutes,
        parent_case_phase_id,
        apella_case_id,
        api_created_timestamp_utc,
        api_updated_timestamp_utc
    from child_case_phases
) as a
