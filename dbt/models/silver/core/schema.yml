version: 1

models:
  - name: flip_room_utilization
    description: >
      This table identifies groups of ORs where the same surgeon has at least two cases in at least two rooms
      on the same day. Each pair of ORs is referred to as a "flip room block" and assigned to a main surgeon
      (the surgeon with the most number of cases in the OR-day, or highest scheduled duration if there is a tie
      in the number of cases).
      Cases in these flip room blocks not belonging to the main surgeon are also included in the table, as the goal
      of this table is to understand how well the flip rooms are being used, which could include use by surgeons who
      don't "own" the flip room block.
    columns:
      - name: ds
        data_type: "timestamp"
      - name: site_id
        data_type: "string"
      - name: room
        data_type: "string"
      - name: date_of_surgery
        data_type: "date"
      - name: scheduled_start_datetime_local
        data_type: "timestamp"
      - name: scheduled_end_datetime_local
        data_type: "timestamp"
      - name: case_id
        data_type: "string"
      - name: case_first_primary_surgeon
        data_type: "string"
        description: "The first primary surgeon of the case, as defined in case_features.
          This surgeon might not be the owner of the flip room block."
      - name: flip_block_main_surgeon
        data_type: "string"
        description: "The main surgeon in the flip room block is the surgeon with the highest number of cases; or, the highest scheduled duration if a tie-breaker is needed."
      - name: total_scheduled_duration_for_main_surgeon_in_room
        data_type: "float"
      - name: earliest_sched_start_for_main_surgeon_in_room
        data_type: "timestamp"
        description: "The scheduled start time for the earliest case belonging to the main surgeon in the OR. This is used to identify whether the main surgeon's active scheduled hours overlap across ORs."
      - name: latest_sched_end_for_main_surgeon_in_room
        data_type: "timestamp"
        description: "The scheduled end time for the latest case belonging to the main surgeon in the OR. This is used to identify whether the main surgeon's active scheduled hours overlap across ORs."
      - name: scheduled_time_for_main_surgeon_overlaps_across_rooms
        data_type: "boolean"
        description: "Identifies whether the main surgeon's scheduled hours overlap across rooms. This column is provided if we want to further constrain the definition of flip room blocks to include only overlapping scheduled times."
      - name: room_order_in_flip_block
        data_type: "int"
        description: "Within a flip block, rooms are given labels of 1, 2, 3 based on the main surgeon's total scheduled duration in the room, where 1 means the highest scheduled duration."
      - name: flip_block_name
        data_type: "string"
        description: "The concatentation of the site_id, main surgeon, and date of the flip room block"
      - name: actual_duration
        data_type: "float"
        description: "Actual duration of the case in minutes"
      - name: actual_start_datetime_local
        data_type: "timestamp"
        description: "Actual start datetime of the case"
      - name: actual_end_datetime_local
        data_type: "timestamp"
        description: "Actual end datetime of the case"
      - name: patient_draped_datetime_local
        data_type: "timestamp"
        description: "Timestamp of patient draped event with the highest created time in case_events table"
      - name: patient_undraped_datetime_local
        data_type: "timestamp"
        description: "Timestamp of patient undraped event with the highest created time in case_events table"
      - name: case_id_for_prev_case_in_flip_block
        data_type: "string"
        description: "The case_id of the case with the most recent scheduled start time in the flip room block. This case probably belongs to another room and may have a different first primary surgeon."
      - name: first_primary_surgeon_for_prev_case_in_flip_block
        data_type: "string"
        description: "The first primary surgeon of the case with the most recent scheduled start time in the flip room block."
      - name: case_order_in_flip_block
        data_type: "int"
        description: "The case order of all cases scheduled in the flip room block, based on scheduled start time (with 1 being the earliest)"
      - name: duration_undrape_of_prev_case_in_flip_block_to_drape
        data_type: "float"
        description: "The minutes between the patient undraped timestamp of the previous case in the flip room block to the patient draped timestamp of the current case. Negative values indicate patient was undraped in the previous case after patient was draped in current case."
      - name: duration_sched_end_of_prev_case_in_flip_block_to_sched_start
        data_type: "float"
        description: "The minutes between the scheduled end of the previous case in the flip room block to the scheduled start of the current case. Negative values indicate previous case scheduled end was after the current case scheduled start."
      - name: add_on
        data_type: "tinyint"
      - name: case_surgeon_previous_room
        data_type: "string"
        description: "The room where the case's first primary surgeon previous had a scheduled case"
      - name: case_surgeon_changed_room
        data_type: "boolean"
        description: "Identifies surgeons whose consecutive cases changed rooms"
  - name: core_turnover_events
    description: >
      This table contains the events that are outside a CASE (outside wheels-in to wheels-out).
      There is one nuance to this table:
          - If we have wheels_in/out that are not associated with an apella_case_id, we'll not
          know about them and events in between those wheel_in/out will be included
      Note that we are not partitioning by date, a case straddling midnight should work just
      fine.
    columns:
      - name: event_id
        data_type: "string"
        description: "the id of the event in bronze.public_events"
      - name: ds
        data_type: "timestamp"
      - name: start_time
        data_type: "timestamp"
        description: "the time in UTC corresponding to this event"
      - name: site_id
        data_type: "string"
      - name: org_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: event_type_id
        data_type: "string"
        description: "the `name` of the event, this is the event_type_id as it appears in bronze.public_events"
      - name: source_type
        data_type: "string"
      - name: version
        data_type: "int"
      - name: prev_apella_case_id
        data_type: "string"
        description: "the id of the case prior to this event, is the case_id in bronze.phases
        table. This case can be a few days before than the event, for example matching events on
        Monday to previous cases on Friday."
      - name: next_apella_case_id
        data_type: "string"
        description: "the id of the case following this event, is the case_id in bronze.phases
        table. This case can be a few days later than the event, for example matching events on
        Friday to cases on Monday."

  - name: core_case_phases
    description: >
      This table contains the phase hierarchy of a case, as described [here](https://www.notion.so/apella/Phases-1ba24dac85984ae480fe0d39c7a3bdf4). This means, the parent phase (CASE) and all of its children (pre/intra/post operative phases).
      There are two nuances to this table:
        - This table only includes case phases that were matched to an EHR case
        - A given case can have multiple child case phases (e.g., more than one intra operative phase)
    columns:
      - name: ds
        data_type: "timestamp"
      - name: phase_id
        data_type: "string"
        description: "the id of the phase in bronze.public_phases"
      - name: apella_case_id
        data_type: "string"
        description: "Apella's unique identifier of the case. Guaranteed to not be null because
        only phases that are able to be case-matched are available in this table."
      - name: parent_case_phase_id
        data_type: "string"
        description: "the phase ID of type CASE that this phase is part of, guaranteed to not be null."
      - name: org_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: site_name
        data_type: "string"
      - name: site_timezone
        data_type: "string"
      - name: phase_type_id
        data_type: "string"
        description: "the name of the phase within the case"
      - name: start_event_id
        data_type: "string"
      - name: end_event_id
        data_type: "string"
      - name: phase_start_timestamp_utc
        data_type: "timestamp"
      - name: phase_end_timestamp_utc
        data_type: "timestamp"
      - name: phase_start_datetime_local
        data_type: "datetime"
        description: "the local datetime the phase started"
      - name: phase_end_datetime_local
        data_type: "datetime"
        description: "the local datetime the phase ended"
      - name: phase_duration_minutes
        data_type: "bigint"
        description: "the duration of the phase in minutes"
      - name: api_created_timestamp_utc
        data_type: datetime
        description: the created timestamp of the case, according to when the API database received the initial write
      - name: api_updated_timestamp_utc
        data_type: datetime
        description: the latest update timestamp of the case in `public.cases`, according to when the API database received the update

  - name: core_cases
    description: >
      This table only contains cases from the API database. It does NOT contain any historical cases received as part of user onboarding.
      When `apella_case_id` is null, that means there is no scheduled case information. When `case_phase_id` is null, that means there is no observed CASE phase.
      To look at cases that take place over a certain timeframe, use scheduled_start_datetime_local, not api_created_timestamp_utc, because some sites send cases over the API long after they actually took place for idiosyncratic reasons.
      The table also contains some extra columns for convenience.
    columns:
      - name: ds
        data_type: timestamp
      - name: org_id
        data_type: string
      - name: org_name
        data_type: string
      - name: site_id
        data_type: string
        description: For the scheduled case
      - name: site_name
        data_type: string
        description: For the scheduled case
      - name: room_id
        data_type: string
        description: For the scheduled case
      - name: room_name
        data_type: string
        description: For the scheduled case
      - name: actual_site_id
        data_type: string
        description: For the observed CASE phase, which could be different from where the case was originally scheduled.
      - name: actual_site_name
        data_type: string
        description: For the observed CASE phase, which could be different from where the case was originally scheduled.
      - name: actual_room_id
        data_type: string
        description: For the observed CASE phase, which could be different from where the case was originally scheduled.
      - name: actual_room_name
        data_type: string
        description: For the observed CASE phase, which could be different from where the case was originally scheduled.
      - name: apella_case_id
        data_type: string
        description: the ID of the case as generated by the API Server
      - name: customer_case_id
        data_type: string
        description: the ID of the case as generated by the client and sent via our EHR integration
      - name: case_phase_id
        data_type: string
        description: the ID of the matched phase of type `CASE`
      - name: is_add_on
        data_type: boolean
      - name: scheduled_start_datetime_local
        data_type: datetime
        description: the scheduled start datetime of the case converted to the timezone of the corresponding site
      - name: scheduled_end_datetime_local
        data_type: datetime
        description: the scheduled end datetime of the case converted to the timezone of the corresponding site
      - name: scheduled_duration_minutes
        data_type: "bigint"
        description: "the duration of the scheduled case in minutes"
      - name: actual_start_datetime_local
        data_type: datetime
        description: the actual start datetime of the case converted to the timezone of the corresponding site, as determined by our phase generation pipeline
      - name: actual_end_datetime_local
        data_type: datetime
        description: the actual end datetime of the case converted to the timezone of the corresponding site, as determined by our phase generation pipeline
      - name: actual_duration_minutes
        data_type: "bigint"
        description: "the duration of the detected case phase in minutes"
      - name: site_timezone
        data_type: string
      - name: case_status
        data_type: string
        description: the status of the case, as pulled from the API Server `cases` table. e.g., `scheduled` or `cancelled`
      - name: patient_classification_id
        data_type: string
      - name: patient_classification_name
        data_type: string
        description: the display name of `patient_classification_id`
      - name: case_classification_id
        data_type: string
      - name: case_classification_name
        data_type: string
        description: the display name of `case_classification_id`
      - name: scheduled_api_created_timestamp_utc
        data_type: datetime
        description: the created timestamp of the scheduled case, according to when the API database received the initial write
      - name: scheduled_api_updated_timestamp_utc
        data_type: datetime
        description: the latest update timestamp of the case in `public.cases`, according to when the API database received the update
      - name: actual_api_created_timestamp_utc
        data_type: datetime
        description: the created timestamp of the CASE phase, according to when the API database received the initial write
      - name: actual_api_updated_timestamp_utc
        data_type: datetime
        description: the latest update timestamp of the CASE phase in `public.phases`, according to when the API database received the update
      - name: service_line_id
        data_type: "string"
      - name: service_line_name
        data_type: string
        description: the display name of `service_line_id`
      - name: is_in_flip_room
        data_type: boolean
        description: whether the case is part of a flip room, as defined by the product (see https://www.notion.so/apella/Business-Rules-Determining-a-Flip-Room-mini-PRD-24d0ea8c67c04866815c91b6c1d03de0). Only available when `apella_case_id` is not null.
      - name: is_first_case
        data_type: boolean
        description: whether the case is the first case of the day, as defined by the product (see https://www.notion.so/apella/First-Case-On-Time-Starts-FCOTS-829d23c2d1e74dfd8fced23574734dd0). Only available when `apella_case_id` is not null.

  - name: core_turnover_phases
    description: >
      This table contains the phase hierarchy of a TURNOVER, as described [here](https://www.notion.so/apella/Phases-1ba24dac85984ae480fe0d39c7a3bdf4).
      There are two nuances to this table:
        - This table only includes TURNOVER and TURNOVER_CLEAN phases that have matched EHR cases before and after
        - TURNOVER_OPEN doesn't always have a prev_apella_case_id, as the first case of the day does not have a full TURNOVER phase
    columns:
      - name: ds
        data_type: "timestamp"
      - name: phase_id
        data_type: "string"
        description: "the id of the phase in bronze.public_phases"
      - name: prev_apella_case_id
        data_type: "string"
        description: "Apella's unique identifier of the case that came before the turnover phase"
      - name: next_apella_case_id
        data_type: "string"
        description: "Apella's unique identifier of the case that came after the turnover phase. Guaranteed to not be null."
      - name: org_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: phase_type_id
        data_type: "string"
        description: "the name of the phase within the turnover"
      - name: start_event_id
        data_type: "string"
      - name: end_event_id
        data_type: "string"
      - name: phase_start_timestamp_utc
        data_type: "timestamp"
      - name: phase_end_timestamp_utc
        data_type: "timestamp"
      - name: phase_start_datetime_local
        data_type: "datetime"
        description: "the local datetime the phase started"
      - name: phase_end_datetime_local
        data_type: "datetime"
        description: "the local datetime the phase ended"
      - name: phase_duration_minutes
        data_type: "bigint"
        description: "the duration of the phase in minutes"
      - name: api_created_timestamp_utc
        data_type: datetime
        description: the created timestamp of the case, according to when the API database received the initial write
      - name: api_updated_timestamp_utc
        data_type: datetime
        description: the latest update timestamp of the case in `public.cases`, according to when the API database received the update

  - name: core_case_staff
    description: >
      This table contains staff for all cases that have been matched to Apella 'CASE' phases.
    columns:
      - name: ds
        data_type: "timestamp"
      - name: api_created_timestamp_utc
        data_type: "timestamp"
        description: The created timestamp of the staff, according to when the API database received the initial write
      - name: api_updated_timestamp_utc
        data_type: "timestamp"
        description: The latest update timestamp of the staff in `public_case_staff`, according to when the API database received the update
      - name: apella_staff_id
        data_type: "string"
        description: Apella's unique identifier for this staff member
      - name: customer_staff_id
        data_type: "string"
        description: The customer's identifier for this staff member
      - name: apella_case_id
        data_type: "string"
        description: Apella's unique ID of the case this staff member was involved in
      - name: role
        data_type: "string"
        description: The staff member's role for this case
      - name: first_name
        data_type: "string"
      - name: last_name
        data_type: "string"
      - name: org_id
        data_type: "string"
      - name: is_candidate_primary_surgeon
        data_type: "boolean"
        description: A convenience column based on the staff member's role. Note that there can be more than one candidate primary surgeon.


  - name: core_case_procedures
    description: >
      This table contains procedure information for all cases that have been matched to Apella 'CASE' phases.
    columns:
      - name: ds
        data_type: "timestamp"
      - name: api_created_timestamp_utc
        data_type: "timestamp"
        description: The created timestamp of the procedure, according to when the API database received the initial write
      - name: api_updated_timestamp_utc
        data_type: "timestamp"
        description: The latest update timestamp of the procedure in `public_case_procedures`, according to when the API database received the update
      - name: apella_case_id
        data_type: "string"
        description: Apella's unique ID of the case during which this procedure took place
      - name: org_id
        data_type: "string"
      - name: procedure_id
        data_type: "string"
        description: Apella's unique ID for this procedure type
      - name: procedure_name
        data_type: "string"
        description: Human readable name of the procedure
      - name: is_candidate_primary_procedure
        data_type: "boolean"
        description: A convenience column based on the procedure's hierarchy. Note that there can be more than one candidate primary procedure. This is only be available for ~50% of the cases, and will have a values of true|false|null where null indicates that we have no hierarchy information for the case across all its procedures.
      - name: anesthesia_id
        data_type: "string"
        description: Apella's unique ID for the type of anesthesia used during this procedure
      - name: anesthesia_type_name
        data_type: "string"
        description: Human readable name of the anesthesia used

  - name: core_flip_room_cases
    description: >
      This table defines as flip rooms any set of rooms that
        1. have at least a case where the acting surgeon is the same staff_id
      and
        1. those cases overlap in time (as scheduled)
      and
        1. there is more than one room in this set (in contrast to two cases happening in a single
      room with some overlap in time)

      There could be more than two rooms being flipped at the same time by the same surgeon
      Note 1: implementation relies on table `staff_intersection_over_union` (a table that
      already computed when the same surgeon is in two cases at the same time)
      Note 2: in this scenario
        Surgery S1 is done by surgeons A and B in room R1
        Surgery S2 is done by surgeons A and B in room R2 (overlapping in time with surgery S1)
        Surgery S3 is done by surgeon A in room R3 (overlapping in time with surgery S2)
      then we'll have 5 rows
      We'll have surgeries S1 and S2 showing up under surgeon B
      We'll have surgeiies S1, S2 and S3 showing up under surgeon A

    columns:
      - name: ds
        data_type: 'timestamp'
      - name: apella_case_id
        data_type: 'string'
      - name: site_id
        data_type: 'string'
      - name: room_id
        data_type: 'string'
      - name: staff_id
        data_type: 'string'
      - name: case_date
        data_type: "date"
      - name: scheduled_start_datetime_local
        data_type: 'datetime'
      - name: scheduled_end_datetime_local
        data_type: 'datetime'
      - name: actual_start_datetime_local
        data_type: 'datetime'
      - name: actual_end_datetime_local
        data_type: 'datetime'
      - name: case_order_in_site
        data_type: "int"
        description: counter that starts at 1 and increases for each surgery the surgeon has on this date and site_id
          Note that the counter is specific to the surgeon and the site_id
          (case_order_in_site >= case_order_in_room)
        tests:
          - not_null
          - accepted_range:
              min_value: 0
      - name: case_order_in_room
        data_type: "int"
        description: counter that starts at 1 and increases for each surgery the surgeon has in this date and room_id.
            Note that the counter is specific to the surgeon and the room_id
        tests:
          - not_null
          - accepted_range:
              min_value: 0

  - name: core_case_event_tall
    description: Table with events within a case. We only consider events that are matched to
      a case through EHR data.
      We use both apella generated and OBX events (when available)
    columns:
      - name: ds
        data_type: "timestamp"
      - name: event_id
        data_type: "string"
      - name: version
        data_type: "int"
      - name: source_type
        data_type: "string"
        description: one of "OBX" or apella's "human_gt" or "prediction"
      - name: source
        data_type: "string"
        description:
          When source_type is "human_gt" will be the id of the labeler
          When source_type is "prediction" will be the model_version
          When source_type is "OBX", will be "OBX"
      - name: event_type_id
        data_type: "string"
      - name: event_time
        data_type: "timestamp"
      - name: apella_case_id
        data_type: "string"

  - name: core_case_first_event_wide
    description: >
      Table with events within a case in wide format. In this version of the table,
      we only allow one event of each type per case (we arbitrarily take the 1st event).
      We prepend the name of the event_type with either 'OBX' or 'Apella' to highlight the original
      source_type.
      This is a VERY wide table with many columns, many of these columns will be mostly null values.
      The most populated columns of each type are (in decreasing frequency)
        For OBX events
          OBSERVED_OUT_OF_ROOM
          OBSERVED_IN_ROOM
          OBSERVED_CASE_START
          OBSERVED_CASE_FINISH
          OBSERVED_ANESTHESIA_FINISH
          OBSERVED_ANESTHESIA_START
          OBSERVED_CASE_CLOSING
          OBSERVED_IN_FACILITY
          OBSERVED_IN_PACU
          OBSERVED_PROCEDURAL_CARE_COMPLETE
          OBSERVED_OUT_OF_PACU
          OBSERVED_IN_PRE_PROCEDURE
          OBSERVED_PRE_PROCEDURE_COMPLETE
          OBSERVED_ANESTHESIA_READY
        For Apella case related events
          patient_wheels_out
          patient_wheels_in
          patient_undraped
          patient_draped
          patient_xfer_to_bed
          patient_xfer_to_or_table
          intubation
          patient_not_intubated
          anesthesia_draping
          anesthesia_undraping
          trash_out
          patient_entered_intubated
          endo_pack_open
          back_table_open
          back_table_cleared
        For Apella turnover events
          back_table_open
          terminal_clean_start
          terminal_clean_end
          mop_out_turnover
          trash_out
          endo_pack_open
          back_table_cleared
          no_mop
          wall_mopping
          mop_out
          cleaning_start
          setup_start
          cleaning_end
          setup_end
      columns:
      - name: ds
        data_type: "timestamp"
      - name: apella_case_id
        data_type: "string"
        tests:
          - not_null
      - name: Apella_anesthesia_cart_workstation_cleaning_start_prev
        data_type: "timestamp"
      - name: Apella_anesthesia_machine_cleaning_start_prev
        data_type: "timestamp"
      - name: Apella_anesthesia_medication_station_cleaning_start_prev
        data_type: "timestamp"
      - name: Apella_back_table_cleaning_start_prev
        data_type: "timestamp"
      - name: Apella_back_table_cleared_next
        data_type: "timestamp"
      - name: Apella_back_table_open_prev
        data_type: "timestamp"
      - name: Apella_boom_mounted_monitors_cleaning_start_prev
        data_type: "timestamp"
      - name: Apella_boom_mounted_overhead_light_cleaning_start_prev
        data_type: "timestamp"
      - name: Apella_case_cart_in_prev
        data_type: "timestamp"
      - name: Apella_case_cart_out_prev
        data_type: "timestamp"
      - name: Apella_ceiling_cleaning_start_prev
        data_type: "timestamp"
      - name: Apella_chair_cleaning_start_prev
        data_type: "timestamp"
      - name: Apella_cleaning_crew_in_prev
        data_type: "timestamp"
      - name: Apella_cleaning_crew_out_prev
        data_type: "timestamp"
      - name: Apella_cleaning_end_prev
        data_type: "timestamp"
      - name: Apella_cleaning_start_prev
        data_type: "timestamp"
      - name: Apella_da_vinci_instrument_opening_prev
        data_type: "timestamp"
      - name: Apella_da_vinci_instrument_use_prev
        data_type: "timestamp"
      - name: Apella_da_vinci_robot_cleaning_start_prev
        data_type: "timestamp"
      - name: Apella_da_vinci_surgeon_console_cleaning_start_prev
        data_type: "timestamp"
      - name: Apella_da_vinci_tray_opening_prev
        data_type: "timestamp"
      - name: Apella_door_to_the_core_cleaning_start_prev
        data_type: "timestamp"
      - name: Apella_endo_pack_open_prev
        data_type: "timestamp"
      - name: Apella_equipment_cleaning_start_prev
        data_type: "timestamp"
      - name: Apella_floor_cleaning_start_prev
        data_type: "timestamp"
      - name: Apella_floor_mopping_prev
        data_type: "timestamp"
      - name: Apella_furniture_cleaning_start_prev
        data_type: "timestamp"
      - name: Apella_instrument_tray_opening_prev
        data_type: "timestamp"
      - name: Apella_lights_off_prev
        data_type: "timestamp"
      - name: Apella_lights_on_prev
        data_type: "timestamp"
      - name: Apella_mayo_stand_cleaning_start_prev
        data_type: "timestamp"
      - name: Apella_mop_in_prev
        data_type: "timestamp"
      - name: Apella_mop_out_prev
        data_type: "timestamp"
      - name: Apella_mop_out_turnover_prev
        data_type: "timestamp"
      - name: Apella_mopping_floor_start_prev
        data_type: "timestamp"
      - name: Apella_no_mop_prev
        data_type: "timestamp"
      - name: Apella_nursing_fixed_work_station_cleaning_start_prev
        data_type: "timestamp"
      - name: Apella_nursing_mobile_workstation_cleaning_start_prev
        data_type: "timestamp"
      - name: Apella_nursing_station_cleaning_start_prev
        data_type: "timestamp"
      - name: Apella_or_lights_and_booms_cleaning_start_prev
        data_type: "timestamp"
      - name: Apella_or_table_cleaning_start_prev
        data_type: "timestamp"
      - name: Apella_or_table_ready_prev
        data_type: "timestamp"
      - name: Apella_post_operative_prev
        data_type: "timestamp"
      - name: Apella_pre_operative_prev
        data_type: "timestamp"
      - name: Apella_prep_table_cleaning_start_prev
        data_type: "timestamp"
      - name: Apella_robot_drape_end_prev
        data_type: "timestamp"
      - name: Apella_robot_drape_start_prev
        data_type: "timestamp"
      - name: Apella_room_ready_prev
        data_type: "timestamp"
      - name: Apella_setup_end_prev
        data_type: "timestamp"
      - name: Apella_setup_start_prev
        data_type: "timestamp"
      - name: Apella_sterile_pack_on_back_table_prev
        data_type: "timestamp"
      - name: Apella_sweeping_floor_start_start_prev
        data_type: "timestamp"
      - name: Apella_terminal_clean_end_prev
        data_type: "timestamp"
      - name: Apella_terminal_clean_start_prev
        data_type: "timestamp"
      - name: Apella_trash_out_prev
        data_type: "timestamp"
      - name: Apella_turn_over_clean_prev
        data_type: "timestamp"
      - name: Apella_turn_over_idle_prev
        data_type: "timestamp"
      - name: Apella_turn_over_open_prev
        data_type: "timestamp"
      - name: Apella_under_OR_equipment_cleaning_start_prev
        data_type: "timestamp"
      - name: Apella_under_OR_table_pads_cleaning_start_prev
        data_type: "timestamp"
      - name: Apella_wall_mopping_prev
        data_type: "timestamp"
      - name: Apella_waste_can_cleaning_start_prev
        data_type: "timestamp"
      - name: Apella_anesthesia_cart_workstation_cleaning_start_next
        data_type: "timestamp"
      - name: Apella_anesthesia_machine_cleaning_start_next
        data_type: "timestamp"
      - name: Apella_anesthesia_medication_station_cleaning_start_next
        data_type: "timestamp"
      - name: Apella_back_table_cleaning_start_next
        data_type: "timestamp"
      - name: Apella_back_table_cleared_next
        data_type: "timestamp"
      - name: Apella_back_table_open_next
        data_type: "timestamp"
      - name: Apella_boom_mounted_monitors_cleaning_start_next
        data_type: "timestamp"
      - name: Apella_boom_mounted_overhead_light_cleaning_start_next
        data_type: "timestamp"
      - name: Apella_case_cart_in_next
        data_type: "timestamp"
      - name: Apella_case_cart_out_next
        data_type: "timestamp"
      - name: Apella_ceiling_cleaning_start_next
        data_type: "timestamp"
      - name: Apella_chair_cleaning_start_next
        data_type: "timestamp"
      - name: Apella_cleaning_crew_in_next
        data_type: "timestamp"
      - name: Apella_cleaning_crew_out_next
        data_type: "timestamp"
      - name: Apella_cleaning_end_next
        data_type: "timestamp"
      - name: Apella_cleaning_start_next
        data_type: "timestamp"
      - name: Apella_da_vinci_instrument_opening_next
        data_type: "timestamp"
      - name: Apella_da_vinci_instrument_use_next
        data_type: "timestamp"
      - name: Apella_da_vinci_robot_cleaning_start_next
        data_type: "timestamp"
      - name: Apella_da_vinci_surgeon_console_cleaning_start_next
        data_type: "timestamp"
      - name: Apella_da_vinci_tray_opening_next
        data_type: "timestamp"
      - name: Apella_door_to_the_core_cleaning_start_next
        data_type: "timestamp"
      - name: Apella_endo_pack_open_next
        data_type: "timestamp"
      - name: Apella_equipment_cleaning_start_next
        data_type: "timestamp"
      - name: Apella_floor_cleaning_start_next
        data_type: "timestamp"
      - name: Apella_floor_mopping_next
        data_type: "timestamp"
      - name: Apella_furniture_cleaning_start_next
        data_type: "timestamp"
      - name: Apella_instrument_tray_opening_next
        data_type: "timestamp"
      - name: Apella_lights_off_next
        data_type: "timestamp"
      - name: Apella_lights_on_next
        data_type: "timestamp"
      - name: Apella_mayo_stand_cleaning_start_next
        data_type: "timestamp"
      - name: Apella_mop_in_next
        data_type: "timestamp"
      - name: Apella_mop_out_next
        data_type: "timestamp"
      - name: Apella_mop_out_turnover_next
        data_type: "timestamp"
      - name: Apella_mopping_floor_start_next
        data_type: "timestamp"
      - name: Apella_no_mop_next
        data_type: "timestamp"
      - name: Apella_null_event_next
        data_type: "timestamp"
      - name: Apella_nursing_fixed_work_station_cleaning_start_next
        data_type: "timestamp"
      - name: Apella_nursing_mobile_workstation_cleaning_start_next
        data_type: "timestamp"
      - name: Apella_nursing_station_cleaning_start_next
        data_type: "timestamp"
      - name: Apella_or_lights_and_booms_cleaning_start_next
        data_type: "timestamp"
      - name: Apella_or_table_cleaning_start_next
        data_type: "timestamp"
      - name: Apella_or_table_ready_next
        data_type: "timestamp"
      - name: Apella_post_operative_next
        data_type: "timestamp"
      - name: Apella_pre_operative_next
        data_type: "timestamp"
      - name: Apella_prep_table_cleaning_start_next
        data_type: "timestamp"
      - name: Apella_robot_drape_end_next
        data_type: "timestamp"
      - name: Apella_robot_drape_start_next
        data_type: "timestamp"
      - name: Apella_room_ready_next
        data_type: "timestamp"
      - name: Apella_setup_end_next
        data_type: "timestamp"
      - name: Apella_setup_start_next
        data_type: "timestamp"
      - name: Apella_sterile_pack_on_back_table_next
        data_type: "timestamp"
      - name: Apella_sweeping_floor_start_start_next
        data_type: "timestamp"
      - name: Apella_terminal_clean_end_next
        data_type: "timestamp"
      - name: Apella_terminal_clean_start_next
        data_type: "timestamp"
      - name: Apella_trash_out_next
        data_type: "timestamp"
      - name: Apella_turn_over_clean_next
        data_type: "timestamp"
      - name: Apella_turn_over_idle_next
        data_type: "timestamp"
      - name: Apella_turn_over_open_next
        data_type: "timestamp"
      - name: Apella_under_OR_equipment_cleaning_start_next
        data_type: "timestamp"
      - name: Apella_under_OR_table_pads_cleaning_start_next
        data_type: "timestamp"
      - name: Apella_wall_mopping_next
        data_type: "timestamp"
      - name: Apella_waste_can_cleaning_start_next
        data_type: "timestamp"
      - name: Apella_anesthesia_draping
        data_type: "timestamp"
      - name: Apella_anesthesia_ready
        data_type: "timestamp"
      - name: Apella_anesthesia_undraping
        data_type: "timestamp"
      - name: Apella_back_table_cleared
        data_type: "timestamp"
      - name: Apella_back_table_open
        data_type: "timestamp"
      - name: Apella_case_cart_in
        data_type: "timestamp"
      - name: Apella_case_cart_out
        data_type: "timestamp"
      - name: Apella_endo_pack_open
        data_type: "timestamp"
      - name: Apella_intubation
        data_type: "timestamp"
      - name: Apella_patient_not_intubated
        data_type: "timestamp"
      - name: Apella_lights_off
        data_type: "timestamp"
      - name: Apella_lights_on
        data_type: "timestamp"
      - name: Apella_mop_in
        data_type: "timestamp"
      - name: Apella_mop_out
        data_type: "timestamp"
      - name: Apella_no_case_cart
        data_type: "timestamp"
      - name: Apella_no_patient
        data_type: "timestamp"
      - name: Apella_patient_draped
        data_type: "timestamp"
      - name: Apella_patient_on_hospital_bed
        data_type: "timestamp"
      - name: Apella_patient_on_or_table
        data_type: "timestamp"
      - name: Apella_patient_undraped
        data_type: "timestamp"
      - name: Apella_patient_wheels_in
        data_type: "timestamp"
      - name: Apella_patient_wheels_out
        data_type: "timestamp"
      - name: Apella_patient_xfer_to_bed
        data_type: "timestamp"
      - name: Apella_patient_xfer_to_or_table
        data_type: "timestamp"
      - name: Apella_post_operative
        data_type: "timestamp"
      - name: Apella_pre_operative
        data_type: "timestamp"
      - name: Apella_sterile_pack_on_back_table
        data_type: "timestamp"
      - name: Apella_surgery
        data_type: "timestamp"
      - name: Apella_terminal_clean_end
        data_type: "timestamp"
      - name: Apella_terminal_clean_start
        data_type: "timestamp"
      - name: Apella_turn_over_clean
        data_type: "timestamp"
      - name: Apella_turn_over_idle
        data_type: "string"
      - name: Apella_turn_over_open
        data_type: "timestamp"
      - name: OBX_OBSERVED_ANESTHESIA_AVAILABLE
        data_type: "timestamp"
      - name: OBX_OBSERVED_ANESTHESIA_FINISH
        data_type: "timestamp"
      - name: OBX_OBSERVED_ANESTHESIA_READY
        data_type: "timestamp"
      - name: OBX_OBSERVED_ANESTHESIA_START
        data_type: "timestamp"
      - name: OBX_OBSERVED_AT_CECUM
        data_type: "timestamp"
      - name: OBX_OBSERVED_CASE_CLOSING
        data_type: "timestamp"
      - name: OBX_OBSERVED_CASE_FINISH
        data_type: "timestamp"
      - name: OBX_OBSERVED_CASE_START
        data_type: "timestamp"
      - name: OBX_OBSERVED_CATHLAB_TO_OR
        data_type: "timestamp"
      - name: OBX_OBSERVED_CLEANUP_COMPLETE
        data_type: "timestamp"
      - name: OBX_OBSERVED_CLEANUP_START
        data_type: "timestamp"
      - name: OBX_OBSERVED_DECISION_TIME
        data_type: "timestamp"
      - name: OBX_OBSERVED_DISCHARGE_CRITERIA_MET
        data_type: "timestamp"
      - name: OBX_OBSERVED_EPIDURAL_TO_C_SECTION
        data_type: "timestamp"
      - name: OBX_OBSERVED_HYSTEROTOMY
        data_type: "timestamp"
      - name: OBX_OBSERVED_INDUCTION
        data_type: "timestamp"
      - name: OBX_OBSERVED_IN_EXTENDED_RECOVERY
        data_type: "timestamp"
      - name: OBX_OBSERVED_IN_FACILITY
        data_type: "timestamp"
      - name: OBX_OBSERVED_IN_HOLDING_AREA
        data_type: "timestamp"
      - name: OBX_OBSERVED_IN_PACU
        data_type: "timestamp"
      - name: OBX_OBSERVED_IN_PHASE_II
        data_type: "timestamp"
      - name: OBX_OBSERVED_IN_PRE_PROCEDURE
        data_type: "timestamp"
      - name: OBX_OBSERVED_IN_PROCEDURAL_RECOVERY
        data_type: "timestamp"
      - name: OBX_OBSERVED_IN_ROOM
        data_type: "timestamp"
      - name: OBX_OBSERVED_OPNOTE_VERIFIED
        data_type: "timestamp"
      - name: OBX_OBSERVED_OUT_OF_EXTENDED_RECOVERY
        data_type: "timestamp"
      - name: OBX_OBSERVED_OUT_OF_HOLDING_AREA
        data_type: "timestamp"
      - name: OBX_OBSERVED_OUT_OF_PACU
        data_type: "timestamp"
      - name: OBX_OBSERVED_OUT_OF_PACU_2ND_TIME
        data_type: "timestamp"
      - name: OBX_OBSERVED_OUT_OF_PACU_3RD_TIME
        data_type: "timestamp"
      - name: OBX_OBSERVED_OUT_OF_PHASE_II
        data_type: "timestamp"
      - name: OBX_OBSERVED_OUT_OF_PHASE_II_2ND_TIME
        data_type: "timestamp"
      - name: OBX_OBSERVED_OUT_OF_PRE_PROCEDURE
        data_type: "timestamp"
      - name: OBX_OBSERVED_OUT_OF_PROCEDURAL_RECOVERY
        data_type: "timestamp"
      - name: OBX_OBSERVED_OUT_OF_ROOM
        data_type: "timestamp"
      - name: OBX_OBSERVED_PATIENT_MOVED_TO_ANOTHER_OR_ROOM
        data_type: "timestamp"
      - name: OBX_OBSERVED_PATIENT_SENT_FOR
        data_type: "timestamp"
      - name: OBX_OBSERVED_PATIENT_TRANSFER_TO_HOSPITAL_ROOM
        data_type: "timestamp"
      - name: OBX_OBSERVED_PHASE_II_CARE_COMPLETE
        data_type: "timestamp"
      - name: OBX_OBSERVED_PHYSICIAN_AVAILABLE
        data_type: "timestamp"
      - name: OBX_OBSERVED_PREP_COMPLETE
        data_type: "timestamp"
      - name: OBX_OBSERVED_PREP_START
        data_type: "timestamp"
      - name: OBX_OBSERVED_PRE_PROCEDURE_COMPLETE
        data_type: "timestamp"
      - name: OBX_OBSERVED_PROCEDURAL_CARE_COMPLETE
        data_type: "timestamp"
      - name: OBX_OBSERVED_PROCEDURE_FINISH
        data_type: "timestamp"
      - name: OBX_OBSERVED_RETURN_TO_OR
        data_type: "timestamp"
      - name: OBX_OBSERVED_RETURN_TO_PACU
        data_type: "timestamp"
      - name: OBX_OBSERVED_RETURN_TO_PACU_3RD_TIME
        data_type: "timestamp"
      - name: OBX_OBSERVED_RETURN_TO_PHASE_II
        data_type: "timestamp"
      - name: OBX_OBSERVED_SEDATION_END
        data_type: "timestamp"
      - name: OBX_OBSERVED_SEDATION_START
        data_type: "timestamp"
      - name: OBX_OBSERVED_SETUP_COMPLETE
        data_type: "timestamp"
      - name: OBX_OBSERVED_SETUP_START
        data_type: "timestamp"
      - name: OBX_OBSERVED_TIMEOUT_ANESTHESIA
        data_type: "timestamp"
      - name: OBX_OBSERVED_TIMEOUT_DEBRIEF
        data_type: "timestamp"
      - name: OBX_OBSERVED_TIMEOUT_FIRE_SAFETY
        data_type: "timestamp"
      - name: OBX_OBSERVED_TIMEOUT_PREINCISION
        data_type: "timestamp"
      - name: OBX_OBSERVED_TIMEOUT_PREPROCEDURE
        data_type: "timestamp"
      - name: OBX_OBSERVED_TO_PHASE_II
        data_type: "timestamp"
  - name: core_case_events
    description: >
      This table contains a mapping between a case and all events that occurred between that case's actual start and end time.
      Events are pulled from both Apella events (public.events) and EHR events (public.observations).
    columns:
      - name: ds
        data_type: "timestamp"
      - name: event_id
        data_type: "string"
        description: The event ID
      - name: event_time
        data_type: "timestamp"
        description: The time that this event occurred. It is either `start_time` from public.events or `observation_time` from public.observations
      - name: org_id
        data_type: "string"
        description: The org ID
      - name: site_id
        data_type: "string"
        description: The site ID
      - name: room_id
        data_type: "string"
        description: The room ID
      - name: case_id
        data_type: "string"
        description: The case ID (the one assigned in public.cases)
      - name: source_type
        data_type: "string"
        description: >
          The source type of the event. This can be one of the following:
            - ehr (for observations)
            - human_gt (for Apella events)
            - prediction (for Apella events)
      - name: source
        data_type: "string"
        description: The source of the event. This can be a user or a system (e.g., `event-predictor`)
      - name: event_type_id
        data_type: "string"
        description: The event type ID - comes straight from the upstream tables, unprocessed
      - name: event_created_time
        data_type: "timestamp"
        description: The time the event was created in the upstream table
      - name: event_updated_time
        data_type: "timestamp"
        description: The time the event was updated in the upstream table