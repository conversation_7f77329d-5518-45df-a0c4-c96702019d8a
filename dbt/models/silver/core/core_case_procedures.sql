{{ daily_config(produce_latest_snapshot=True) }}

select
    {{ ds() }} as ds,
    case_procedures.created_time as api_created_timestamp_utc,
    case_procedures.updated_time as api_updated_timestamp_utc,
    case_procedures.case_id as apella_case_id,
    procedures.org_id as org_id,
    case_procedures.procedure_id,
    procedures.name as procedure_name,
    case_procedures.hierarchy = 1 as is_candidate_primary_procedure,
    case_procedures.anesthesia_id,
    anesthesias.name as anesthesia_type_name
from {{ api_table_snapshot("bronze", "public_case_procedures", "case_procedures") }}
left outer join {{ api_table_snapshot("bronze", "public_procedures", "procedures") }}
    on case_procedures.procedure_id = procedures.id
left outer join {{ api_table_snapshot("bronze", "public_anesthesias", "anesthesias") }}
    on case_procedures.anesthesia_id = anesthesias.id
where
    case_procedures.archived_time is null
    and case_procedures.case_id is not null
    and procedures.org_id is not null
