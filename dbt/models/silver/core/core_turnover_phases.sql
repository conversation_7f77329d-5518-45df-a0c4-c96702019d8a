{{ daily_config(produce_latest_snapshot=True) }}

with all_phases as (
    select
        phases.id as phase_id,
        phases.case_id as apella_case_id,
        phases.org_id,
        phases.room_id as room_id,
        sites.id as site_id,
        phases.type_id as phase_type_id,
        start_events.id as start_event_id,
        end_events.id as end_event_id,
        start_events.start_time as phase_start_timestamp_utc,
        end_events.start_time as phase_end_timestamp_utc,
        phases.created_time as api_created_timestamp_utc,
        phases.updated_time as api_updated_timestamp_utc,
        extract(date from datetime(start_events.start_time, sites.timezone))
            as phase_date_local,
        datetime(start_events.start_time, sites.timezone)
            as phase_start_datetime_local,
        datetime(end_events.start_time, sites.timezone)
            as phase_end_datetime_local,
        timestamp_diff(
            end_events.start_time,
            start_events.start_time,
            minute
        ) as phase_duration_minutes
    from {{ api_table_snapshot("bronze", "public_phases", alias="phases") }}
    inner join
        {{ api_table_snapshot("bronze", "public_events", alias="start_events") }}
        on phases.start_event_id = start_events.id
    inner join {{ api_table_snapshot("bronze", "public_events", alias="end_events") }}
        on phases.end_event_id = end_events.id
    inner join {{ api_table_snapshot("bronze", "public_sites", alias="sites") }}
        on phases.site_id = sites.id
    where
        phases.source_type = 'unified'
        and start_events.deleted_at is null
        and end_events.deleted_at is null
        and phases.status = 'VALID'
        and phases.room_id is not null
),

parent_turnover_phases as (
    select
        prev_case_phases.apella_case_id as prev_apella_case_id,
        next_case_phases.apella_case_id as next_apella_case_id,
        all_phases.phase_id,
        all_phases.org_id,
        all_phases.room_id,
        all_phases.site_id,
        all_phases.phase_type_id,
        all_phases.start_event_id,
        all_phases.end_event_id,
        all_phases.phase_start_timestamp_utc,
        all_phases.phase_end_timestamp_utc,
        all_phases.phase_date_local,
        all_phases.phase_start_datetime_local,
        all_phases.phase_end_datetime_local,
        all_phases.phase_duration_minutes,
        all_phases.api_created_timestamp_utc,
        all_phases.api_updated_timestamp_utc
    from all_phases
    inner join all_phases as prev_case_phases
        on all_phases.start_event_id = prev_case_phases.end_event_id
    inner join all_phases as next_case_phases
        on all_phases.end_event_id = next_case_phases.start_event_id
    where
        all_phases.phase_type_id = 'TURNOVER'
        and next_case_phases.phase_type_id = 'CASE'
        and next_case_phases.apella_case_id is not null
        and prev_case_phases.phase_type_id = 'CASE'
        and prev_case_phases.apella_case_id is not null
),

child_turnover_phases as (
    select
        parent_turnover_phases.prev_apella_case_id,
        parent_turnover_phases.next_apella_case_id,
        all_phases.phase_id,
        all_phases.org_id,
        all_phases.room_id,
        all_phases.site_id,
        all_phases.phase_type_id,
        all_phases.start_event_id,
        all_phases.end_event_id,
        all_phases.phase_start_timestamp_utc,
        all_phases.phase_end_timestamp_utc,
        all_phases.phase_date_local,
        all_phases.phase_start_datetime_local,
        all_phases.phase_end_datetime_local,
        all_phases.phase_duration_minutes,
        all_phases.api_created_timestamp_utc,
        all_phases.api_updated_timestamp_utc
    from all_phases
    inner join {{ api_table_snapshot("bronze", "public_phase_relationships", "phase_relationships") }}
        on all_phases.phase_id = phase_relationships.child_phase_id
    inner join parent_turnover_phases
        on phase_relationships.parent_phase_id = parent_turnover_phases.phase_id
),

-- First cases of the day have TURNOVER_OPEN phases but no TURNOVER parent phase.
first_case_of_day_turnover_open as (
    select
        next_case_phases.apella_case_id as next_apella_case_id,
        all_phases.phase_id,
        all_phases.org_id,
        all_phases.room_id,
        all_phases.site_id,
        all_phases.phase_type_id,
        all_phases.start_event_id,
        all_phases.end_event_id,
        all_phases.phase_start_timestamp_utc,
        all_phases.phase_end_timestamp_utc,
        all_phases.phase_date_local,
        all_phases.phase_start_datetime_local,
        all_phases.phase_end_datetime_local,
        all_phases.phase_duration_minutes,
        all_phases.api_created_timestamp_utc,
        all_phases.api_updated_timestamp_utc
    from all_phases
    inner join all_phases as next_case_phases
        on all_phases.end_event_id = next_case_phases.start_event_id
    -- Filter out any opens already captured as children of turnovers.
    left outer join child_turnover_phases
        on
            child_turnover_phases.phase_type_id = 'TURNOVER_OPEN'
            and next_case_phases.apella_case_id = child_turnover_phases.next_apella_case_id
    where
        all_phases.phase_type_id = 'TURNOVER_OPEN'
        and next_case_phases.phase_type_id = 'CASE'
        and next_case_phases.apella_case_id is not null
        and child_turnover_phases.next_apella_case_id is null
)

select
    {{ ds() }} as ds,
    *
from (
    select
        phase_id,
        org_id,
        room_id,
        site_id,
        phase_type_id,
        start_event_id,
        end_event_id,
        phase_start_timestamp_utc,
        phase_end_timestamp_utc,
        phase_date_local,
        phase_start_datetime_local,
        phase_end_datetime_local,
        phase_duration_minutes,
        prev_apella_case_id,
        next_apella_case_id,
        api_created_timestamp_utc,
        api_updated_timestamp_utc
    from parent_turnover_phases

    union all

    select
        phase_id,
        org_id,
        room_id,
        site_id,
        phase_type_id,
        start_event_id,
        end_event_id,
        phase_start_timestamp_utc,
        phase_end_timestamp_utc,
        phase_date_local,
        phase_start_datetime_local,
        phase_end_datetime_local,
        phase_duration_minutes,
        prev_apella_case_id,
        next_apella_case_id,
        api_created_timestamp_utc,
        api_updated_timestamp_utc
    from child_turnover_phases

    union all

    select
        phase_id,
        org_id,
        room_id,
        site_id,
        phase_type_id,
        start_event_id,
        end_event_id,
        phase_start_timestamp_utc,
        phase_end_timestamp_utc,
        phase_date_local,
        phase_start_datetime_local,
        phase_end_datetime_local,
        phase_duration_minutes,
        null as prev_apella_case_id,
        next_apella_case_id,
        api_created_timestamp_utc,
        api_updated_timestamp_utc
    from first_case_of_day_turnover_open
) as a
