{{ daily_config() }}

with apella_events as (
    select
        e.id as event_id,
        e.start_time as event_time,
        e.org_id,
        e.site_id,
        e.room_id,
        p.case_id as apella_case_id,
        e.source_type,
        e.event_type_id,
        e.version,
        -- (next line needs `noqa` comment or raises Keywords should not be used as identifiers)
        if(e.source_type = "human_gt", e.source, e.model_version) as source -- noqa
    from {{ api_table_snapshot("bronze", "public_phases", "p") }}
    inner join {{ api_table_snapshot("bronze", "public_events", "start_e") }}
        on p.start_event_id = start_e.id
    inner join {{ api_table_snapshot("bronze", "public_events", "end_e") }}
        on p.end_event_id = end_e.id
    inner join {{ api_table_snapshot("bronze", "public_events", "e") }}
        on
            e.room_id = p.room_id
            and e.start_time between start_e.start_time and end_e.start_time
    where
        p.type_id = "CASE"
        and p.status = "VALID"
        and p.case_id is not null
        and p.source_type in ("human_gt", "prediction", "unified")
        and e.source_type in ("prediction", "human_gt")
),

obx_events as (
    select
        po.id as event_id,
        po.observation_time as event_time,
        po.org_id,
        cc.site_id,
        cc.room_id,
        po.case_id as apella_case_id,
        "OBX" as source_type,
        po.type_id as event_type_id
    from {{ api_table_snapshot("bronze", "public_observations", "po") }}
    inner join {{ ref("core_cases") }} as cc
        on po.case_id = cc.apella_case_id
    inner join {{ api_table_snapshot("bronze", "public_phases", "p") }}
        on p.case_id = po.case_id
    where
        cc.ds = {{ ds() }}
        and cc.is_case_matched
        -- I'm not sure why we need to join with phases, seems like a bug to me but we have
        -- apella_case_id = 'a222b455-f66d-480b-b93b-d8b620fe2a08' that matches no CASE phase
        -- select *
        -- FROM "public"."phases" as p
        -- where case_id = 'a222b455-f66d-480b-b93b-d8b620fe2a08'
        and p.type_id = "CASE"
        and p.status = "VALID"
        and p.source_type in ("human_gt", "prediction", "unified")
    -- if multiple observations of the same type for the case_id, keep the last one
    qualify row_number() over (partition by po.case_id, po.type_id order by po.recorded_time desc) = 1
)

select
    {{ ds() }} as ds,
    event_id,
    event_time,
    org_id,
    site_id,
    room_id,
    apella_case_id,
    source_type,
    source,
    event_type_id,
    version
from apella_events
union all
select
    {{ ds() }} as ds,
    event_id,
    event_time,
    org_id,
    site_id,
    room_id,
    apella_case_id,
    source_type,
    -- (next line needs `noqa` comment or raises Keywords should not be used as identifiers)
    null as source,  -- noqa
    event_type_id,
    -- (next line needs `noqa` comment or raises Keywords should not be used as identifiers)
    null as version  -- noqa
from obx_events
