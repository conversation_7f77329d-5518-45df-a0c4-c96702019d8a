{{ daily_config() }}
-- In this model we have several different ways of assigning cases to advertised slots.
-- We'll compute each one separately and join them at the end
-- Note that we are assigning cases to both advertised and not advertised slots

with day_before_slot_timestamps as (
    -- 1st assignment type:
    -- We generate a list of room_id, window_end_time to match cases.
    -- The end_time here will be hardcoded for the time being to 6:00pm the day
    -- before the advertised slot. We can change the 6:00pm in the future and/or make it
    -- a configuration parameter.
    select
        slot.id as slot_id,
        slot.slot_site_id,
        slot.slot_room_id as room_id,
        slot.email_id,
        slot.is_advertised,
        slot.slot_start_timestamp,
        slot.slot_end_timestamp,
        slot.timezone,
        date(slot.slot_start_timestamp, slot.timezone) as slot_date,
        -- next line is a mouth full
        -- we have slot_end_timestamp, we are:
        --  converting it to local datetime to get the date
        --  subtracting 1 day
        --  adding time to make it a datetime at 5:00pm
        --  converting it back to timestamp
        timestamp(
            datetime(date_sub(date(slot.slot_start_timestamp, slot.timezone), interval 1 day), "17:00:00"),
            slot.timezone
        )
            as window_end_timestamp
    from {{ ref("advertised_time__slots") }} as slot
    where slot.ds = {{ ds() }}
),

cases_the_day_before_slot_timestamp as (
    -- now we have the room_id and the end_timestamp, we get the last version of the case
    -- that was scheduled in the room at end_timestamp
    select
        dbst.email_id,
        dbst.slot_id,
        dbst.room_id,
        dbst.is_advertised,
        ch.case_id,
        "day_before" as assignment_type,
        dbst.slot_start_timestamp,
        dbst.slot_end_timestamp,
        max(ch.version) as case_version
    from day_before_slot_timestamps as dbst
    inner join {{ api_table_snapshot("bronze", "public_cases_history", "ch") }}
        on
            dbst.room_id = ch.room_id
            -- and has to be updated before the 6:00pm cut off
            and dbst.window_end_timestamp > ch.updated_time
            and ch.status = "scheduled"
    group by 1, 2, 3, 4, 5, 6, 7, 8
),

assignment_the_day_before as (
    -- now that we have the room_id and the end_timestamp, we match cases and the last version from
    -- public_cases_history available at end_timestamp
    select
        ctdbst.email_id,
        ctdbst.slot_id,
        ctdbst.is_advertised,
        ch.case_id,
        ch.version as case_version,
        "day_before" as assignment_type
    from cases_the_day_before_slot_timestamp as ctdbst
    inner join {{ api_table_snapshot("bronze", "public_cases_history", "ch") }}
        on
            ctdbst.room_id = ch.room_id
            and ctdbst.case_version = ch.version
            and ctdbst.case_id = ch.case_id
            -- for a case to be included, it has to overlap with the slot
            and ctdbst.slot_start_timestamp < ch.scheduled_end_time
            and ctdbst.slot_end_timestamp > ch.scheduled_start_time
),

----------------------------------------------------------------------------------------
first_since_email as (
    -- 2nd assignment type: First version per case_id ever added after the email is sent
    select
        email.id as email_id,
        slot.id as slot_id,
        slot.slot_room_id as room_id,
        slot.is_advertised,
        slot.slot_start_timestamp,
        slot.slot_end_timestamp,
        email.email_timestamp,
        slot.timezone,
        date(slot.slot_start_timestamp, slot.timezone) as slot_date
    from {{ ref("advertised_time__emails") }} as email
    inner join {{ ref("advertised_time__slots") }} as slot
    -- currently this is producing no matches, because slot.email_id is null everywhere
    -- remove this comment once OBS-2150 is done
        -- https://linear.app/apella/issue/OBS-2150/add-email-id-to-send-time-selection-email-in-amplitude
        on email.id = slot.email_id
    where
        email.ds = {{ ds() }}
        and slot.ds = {{ ds() }}
),

assignment_first_since_email as (
    select
        fse.email_id,
        fse.slot_id,
        fse.is_advertised,
        ch.case_id,
        ch.version as case_version,
        "first_since_email" as assignment_type
    from first_since_email as fse
    inner join {{ api_table_snapshot("bronze", "public_cases_history", "ch") }}
        on
            fse.room_id = ch.room_id
            -- for a case to be included, it has to overlap with the slot
            and fse.slot_start_timestamp < ch.scheduled_end_time
            and ch.scheduled_start_time < fse.slot_end_timestamp
            -- and updated after the email is sent
            and fse.email_timestamp < ch.updated_time
            and ch.status = "scheduled"
    qualify row_number() over (partition by ch.room_id, fse.slot_date, ch.case_id order by ch.updated_time) = 1
)


----------------------------------------------------------------------------------------
-- now we join the different assignment types (for the time being there are only 2 assignment types,
-- but we can add more in the future)

select
    {{ ds() }} as ds,
    email_id,
    slot_id,
    is_advertised,
    case_id,
    case_version,
    assignment_type
from assignment_the_day_before
union all
select
    {{ ds() }} as ds,
    email_id,
    slot_id,
    is_advertised,
    case_id,
    case_version,
    assignment_type
from assignment_first_since_email
