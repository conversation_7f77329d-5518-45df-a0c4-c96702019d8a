{{ daily_config() }}

select
    {{ ds() }} as ds,
    email.user_id,
    assignments.email_id,
    assignments.slot_id,
    assignments.is_advertised,
    assignments.case_id,
    assignments.case_version,
    assignments.assignment_type,
    email.email_timestamp,
    date(c_version.scheduled_start_time, slot.timezone) as case_date,
    c_version.scheduled_start_time,
    c_version.scheduled_end_time,
    c_version.updated_time as case_version_updated_timestamp,
    slot.slot_start_timestamp,
    slot.slot_end_timestamp,
    datetime_diff(c_version.scheduled_end_time, c_version.scheduled_start_time, minute)
        as case_scheduled_duration_minutes,
    datetime_diff(slot.slot_end_timestamp, slot.slot_start_timestamp, minute) as slot_duration_minutes,
    datetime(c_v_1.created_time, s.timezone) as case_version_1_created_datetime,
    timestamp_diff(c_v_1.updated_time, email.email_timestamp, minute) as email_to_case_version_1_minutes,
    timestamp_diff(c_version.updated_time, email.email_timestamp, minute) as email_to_current_version_minutes,
    timestamp_diff(c_version.scheduled_start_time, c_version.updated_time, minute)
        as current_version_to_surgery_minutes,
    c_version.is_add_on,
    c_version.version = 1 as is_new_case,
    c_version.scheduled_start_time < c_v_1.scheduled_start_time as is_scheduled_earlier
from {{ ref("advertised_time__slots") }} as slot
inner join {{ ref("advertised_time__emails") }} as email
    on slot.email_id = email.id
inner join {{ ref("advertised_time__assignment") }} as assignments
    on
        slot.id = assignments.slot_id
        and email.id = assignments.email_id
inner join {{ api_table_snapshot("bronze", "public_cases_history", "c_version") }}
    on
        assignments.case_id = c_version.case_id
        and assignments.case_version = c_version.version
inner join {{ api_table_snapshot("bronze","public_cases_history", "c_v_1") }}
    on
        assignments.case_id = c_v_1.case_id
        and c_v_1.version = 1
inner join {{ api_table_snapshot("bronze", "public_sites", "s") }}
    on slot.slot_site_id = s.id
-- remember that advertised_time__emails and advertised_time__slots are incremental models,
-- we shouldn't filter by ds
where
    slot.ds = {{ ds() }}
    and email.ds = {{ ds() }}
    and assignments.ds = {{ ds() }}
