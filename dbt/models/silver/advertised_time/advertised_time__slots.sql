{{ daily_config() }}
select
    {{ ds() }} as ds,
    events.id,
    json_extract_scalar(events.event_properties, "$.emailId") as email_id,
    json_extract_scalar(events.event_properties, "$.slotSiteId") as slot_site_id,
    json_extract_scalar(events.event_properties, "$.slotRoomId") as slot_room_id,
    -- unfortunately no easy to way to convert strings directly to ints, so we need to cast twice
    cast(
        cast(
            json_extract_scalar(events.event_properties, "$.slotMaxAvailableDurationMinutes")
            as float64
        )
        as int
    ) as slot_duration_minutes,
    timestamp(json_extract_scalar(events.event_properties, "$.slotStartTime")) as slot_start_timestamp,
    timestamp(json_extract_scalar(events.event_properties, "$.slotEndTime")) as slot_end_timestamp,
    json_extract_string_array(events.event_properties, "$.slotBlockIds") as slot_block_ids,
    if(events.event_name = "send time slot", true, false) as is_advertised,
    site.timezone
from {{ ref("amplitude_events_cleaned") }} as events
inner join {{ api_table_snapshot("bronze", "public_sites", "site") }}
    on json_extract_scalar(events.event_properties, "$.slotSiteId") = site.id
where
    events.event_name in ("send time slot", "evaluate time slot")
    and events.ds = {{ ds() }}
