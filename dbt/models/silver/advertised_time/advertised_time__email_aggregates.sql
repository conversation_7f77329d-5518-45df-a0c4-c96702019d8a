{{ daily_config() }}

select
    {{ ds() }} as ds,
    emails.id as email_id,
    emails.number_of_slots_suggested,
    emails.number_of_slots_selected,
    slots_and_cases.assignment_type,
    slots_and_cases.is_advertised,
    count(*) as number_of_cases,
    sum(if(slots_and_cases.is_add_on, 1, 0)) as number_of_add_on_cases,
    sum(if(slots_and_cases.is_scheduled_earlier, 1, 0)) as number_of_cases_moved_forward_in_time,
    sum(if(slots_and_cases.is_new_case, 1, 0)) as number_of_new_cases,
    count(*) - sum(if(slots_and_cases.is_new_case, 1, 0)) as number_of_cases_moved_to_slot,
    --  stats about what slots are selected (durations)
    min(slots_and_cases.slot_duration_minutes) as min_slot_duration_minutes,
    approx_quantiles(slots_and_cases.slot_duration_minutes, 100)[offset(50)] as median_slot_duration_minutes,
    max(slots_and_cases.slot_duration_minutes) as max_slot_duration_minutes,
    --  stats about when cases are added relative to email
    min(slots_and_cases.email_to_current_version_minutes) as min_email_to_current_version_minutes,
    approx_quantiles(slots_and_cases.email_to_current_version_minutes, 100)[offset(50)]
        as median_email_to_current_version_minutes,
    max(slots_and_cases.email_to_current_version_minutes) as max_email_to_current_version_minutes,
    sum(if(slots_and_cases.is_advertised, 1, 0)) as number_of_advertised_slots,
    sum(if(slots_and_cases.is_advertised, 0, 1)) as number_of_unadvertised_slots
from {{ ref("advertised_time__emails") }} as emails
inner join {{ ref("advertised_time__slots") }} as slots
    on emails.id = slots.email_id
inner join {{ ref("advertised_time__slots_and_cases") }} as slots_and_cases
    on emails.id = slots_and_cases.email_id
where
    emails.ds = {{ ds() }}
    and slots.ds = {{ ds() }}
    and slots_and_cases.ds = {{ ds() }}
group by 1, 2, 3, 4, 5, 6
