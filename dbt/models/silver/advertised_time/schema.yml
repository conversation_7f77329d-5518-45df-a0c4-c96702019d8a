version: 1

models:
  - name: advertised_time__emails
    columns:
      - name: ds
        data_type: "timestamp"
      - name: id
        data_type: "string"
        description: "A unique identifier for the email. Each slot that gets sent on the same email
        has the same amplitude_id"
      - name: user_id
        data_type: "string"
      - name: is_apella_employee
        data_type: "boolean"
      - name: selected_site_ids
        data_type: "array<string>"
      - name: selected_date_range_start
        data_type: "date"
        description: "The start of the date range selected by the user"
      - name: selected_date_range_end
        data_type: "date"
        description: "The end of the date range selected by the user"
      - name: selected_days_of_week
        data_type: "array<string>"
        description: "The days of the week selected by the user"
      - name: selected_minimum_duration_minutes
        data_type: "int"
        description: "The minimum duration selected by the user"
      - name: selected_room_ids
        data_type: "array<string>"
        description: "The room ids selected by the user"
      - name: selected_block_ids
        data_type: "array<string>"
        description: "The block ids selected by the user"
      - name: number_of_slots_suggested
        data_type: "int"
        description: "The number of slots suggested"
      - name: number_of_slots_selected
        data_type: "int"
        description: "The number of slots selected"
      - name: email_timestamp
        data_type: "timestamp"
        description: "The timestamp when the email was sent"

  - name: advertised_time__slots
    columns:
      - name: ds
        data_type: "timestamp"
      - name: id
        data_type: "string"
        description: "A unique identifier for the slot"
      - name: email_id
        data_type: "string"
        description: "The primary index into advertised_time__emails"
      - name: slot_site_id
        data_type: "string"
      - name: slot_room_id
        data_type: "string"
      - name: slot_duration_minutes
        data_type: "int"
      - name: slot_start_timestamp
        data_type: "timestamp"
      - name: slot_end_timestamp
        data_type: "timestamp"
      - name: slot_block_ids
        data_type: "array<string>"
        description: "The block ids that this slot is associated with"
      - name: is_advertised
        data_type: "boolean"
        description: "Is this slot selected by the user"
      - name: timezone
        data_type: "string"
        description: "The timezone of the corresponding site"

  - name: advertised_time__assignment
    columns:
      - name: ds
        data_type: "timestamp"
      - name: email_id
        data_type: "string"
        description: "The primary index into advertised_time__emails"
      - name: slot_id
        data_type: "string"
        description: "A unique identifier for the slot"
      - name: is_advertised
        data_type: "boolean"
        description: "Is this slot selected by the user and sent on the email?"
      - name: case_id
        data_type: "string"
      - name: case_version
        data_type: "int"
      - name: assignment_type
        data_type: "string"

  - name: advertised_time__slots_and_cases
    columns:
      - name: ds
        data_type: "timestamp"
      - name: user_id
        data_type: "string"
        description: "Not that this is not the same as staff_id, which unfortunately is not available in amplitude"
      - name: email_id
        data_type: "string"
        description: "The primary index into advertised_time__emails"
      - name: slot_id
        data_type: "string"
        description: "A unique identifier for the slot"
      - name: is_advertised
        data_type: "boolean"
        description: "Is this slot selected by the user and sent on the email?"
      - name: case_id
        data_type: "string"
        description: "A unique identifier for the case"
      - name: case_version
        data_type: "int"
      - name: assignment_type
        data_type: "string"
      - name: email_timestamp
        data_type: "timestamp"
        description: "The timestamp when the email was sent"
      - name: case_date
        data_type: "date"
        description: "The date of the case, which is the same as the slot_date"
      - name: scheduled_start_time
        data_type: "timestamp"
      - name: scheduled_end_time
        data_type: "timestamp"
      - name: case_version_updated_timestamp,
        data_type: "timestamp"
      - name: assignment.slot_start_timestamp
        data_type: "timestamp"
      - name: assignment.slot_end_timestamp
        data_type: "timestamp"
      - name: case_scheduled_duration_minutes
        data_type: "int"
      - name: slot_duration_minutes
        data_type: "int"
      - name: case_version_1_created_datetime
        data_type: "datetime"
        description: "when was case_version=1 created"
      - name: email_to_case_version_1_minutes
        data_type: "int"
        description: "Minutes that elapsed since the email to version 1 of the case created. If this number is negative it
          means that the first version was created before the email was sent"
      - name: email_to_current_version_minutes
        data_type: "int"
        description: "Minutes that elapsed since the email to the case first created. If this number is negative it
          means that the first version was created before the email was sent"
      - name: current_version_to_surgery_minutes
        data_type: "int"
        description: "the number of minutes since the case was scheduled till surgery"
      - name: is_add_on
        data_type: "boolean"
        description: "is this an add on case"
      - name: is_new_case
        data_type: "boolean"
        description: "is this a new case"
      - name: is_scheduled_earlier
        data_type: "boolean"
        description: is the case being moved to an earlier time?

  - name: advertised_time__email_aggregates
    columns:
      - name: ds
        data_type: "timestamp"
      - name: email_id
        data_type: "string"
        description: "The primary index into advertised_time__emails"
      - name: number_of_slots_suggested
        data_type: "int"
        description: "The number of slots suggested"
      - name: number_of_slots_selected
        data_type: "int"
        description: "The number of slots selected"
      - name: assignment_type
        data_type: "string"
      - name: number_of_cases
        data_type: "int"
      - name: number_of_add_on_cases
        data_type: "int"
      - name: number_of_cases_moved_forward_in_time
        data_type: "int"
      - name: number_of_new_new_case
        data_type: "int"
      - name: number_of_cases_moved_to_slot
        data_type: "int"
      - name: min_slot_duration_minutes
        data_type: "int"
      - name: median_slot_duration_minutes
        data_type: "int"
      - name: max_slot_duration_minutes
        data_type: "int"
      - name: min_email_to_current_version_minutes
        data_type: "int"
      - name: median_email_current_version_minutes
        data_type: "int"
      - name: max_email_current_version_minutes
        data_type: "int"
