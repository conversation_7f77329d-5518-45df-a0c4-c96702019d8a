{{ daily_config() }}
select
    {{ ds() }} as ds,
    json_extract_scalar(event_properties, "$.emailId") as id,
    --  would be much better if we were using staff_id here instead of user_id. Will be complex to join with
    --  other tables.
    --  Unfortunately, we don't have staff_id in the event_properties
    user_id,
    is_apella_employee,
    json_extract_string_array(event_properties, "$.selectedSiteIds") as selected_site_ids,
    date(json_extract_scalar(event_properties, "$.selectedDateRangeStart")) as selected_date_range_start,
    date(json_extract_scalar(event_properties, "$.selectedDateRangeEnd")) as selected_date_range_end,
    json_extract_string_array(event_properties, "$.selectedDaysOfWeek") as selected_days_of_week,
    cast(json_extract_scalar(event_properties, "$.selectedDuration") as int) as selected_minimum_duration_minutes,
    json_extract_string_array(event_properties, "$.selectedRoomIds") as selected_room_ids,
    json_extract_string_array(event_properties, "$.selectedBlockIds") as selected_block_ids,
    -- TODO replace null below when OBS-2140 is done
    --    https://linear.app/apella/issue/OBS-2140/add-total-times-and-all-times-viewed-event -> discarded
    --    https://linear.app/apella/issue/OBS-2197/add-new-event-properties-to-send-time-email-event-in-amplitude
    null as number_of_slots_suggested,
    cast(json_extract_scalar(event_properties, "$.numSelected") as int) as number_of_slots_selected,
    event_timestamp as email_timestamp
from {{ ref("amplitude_events_cleaned") }}
where
    event_name = "send time email"
    and ds = {{ ds() }}
