{{ daily_config(produce_latest_snapshot=True) }}

with auth0_users as (
    select
        {{ ds() }} as ds,
        any_value(users.user_id) as user_id,
        any_value(users.name) as user_name,
        users.email as user_email,
        any_value(array_to_string(users.groups, ", ")) as user_groups,
        any_value(users.created_at_utc) as created_at_utc,
        any_value(users.last_login_utc) as last_login_utc,
        array_to_string(array_agg(orgs.org_name), ", ") as org_name,
        array_to_string(array_agg(array_to_string(user_roles.roles, " | ")), ", ") as org_roles
    from {{ source("bronze", "auth0_users") }} as users
    left outer join
        {{ source("bronze", "auth0_org_to_user_roles") }} as user_roles
        on users.user_id = user_roles.user_id and user_roles.ds = {{ ds() }}
    left outer join {{ source("bronze", "auth0_orgs") }} as orgs
        on user_roles.org_id = orgs.org_id and orgs.ds = {{ ds() }}
    where
        users.ds = {{ ds() }} and users.email not like "%iam.gserviceaccount.com"
    group by users.email
)

select
    {{ ds() }} as ds,
    auth0_users.user_id as user_id,
    auth0_users.user_name as user_name,
    auth0_users.user_email as user_email,
    auth0_users.user_groups as user_groups,
    auth0_users.created_at_utc as created_at_utc,
    auth0_users.last_login_utc as last_login_utc,
    auth0_users.org_name as org_name,
    auth0_users.org_roles as org_roles,
    sheets_users.job_title as job_title
from auth0_users
left outer join
    {{ source("bronze", "cs_google_sheets_users_to_org_titles") }} as sheets_users
    on lower(auth0_users.user_email) = lower(sheets_users.email) and sheets_users.ds = {{ ds() }}
