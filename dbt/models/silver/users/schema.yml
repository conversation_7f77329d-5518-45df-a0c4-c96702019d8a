version: 1

models:
  - name: user_permissions
    columns:
      - name: user_id
        description: Auth0's unique user ID.
        data_type: "string"
      - name: user_name
        description: The user's full name as it appears in Auth0.
        data_type: "string"
      - name: user_email
        description: The user's email address.
        data_type: "string"
      - name: user_groups
        description: Comma separated list of IdP groups the user is a part of.
        data_type: "string"
      - name: created_at_utc
        description: Timestamp indicating when the user profile was first created.
        data_type: "timestamp"
      - name: last_login_utc
        description: Timestamp indicating when the user last logged in.
        data_type: "timestamp"
      - name: org_name
        description: The comma separated list of Auth0 orgs that a user is part of.
        data_type: "string"
      - name: org_roles
        description: A user's Auth0 Assigned Roles for the organizations the user is part of. Comma separated order mirrors `org_name` above. If a user has multiple roles for a given organization, they are separated by a '|' delimiter.
        data_type: "string"
      - name: job_title
        description: The user's job title as tracked by CS in Google sheets.
        data_type: "string"
