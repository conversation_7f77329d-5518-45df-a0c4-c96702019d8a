version: 3

models:
  - name: amplitude_events_cleaned
    columns:
      - name: ds
        data_type: "timestamp"
      - name: id
        data_type: "string"
        description: "A unique identifier for the event"
      - name: amplitude_id
        data_type: "string"
      - name: event_id
        data_type: "string"
      - name: session_id
        data_type: "string"
      - name: event_name
        data_type: "string"
      - name: event_date
        data_type: "date"
      - name: event_timestamp
        data_type: "timestamp"
      - name: user_id 
        data_type: "string"
      - name: email_domain
        data_type: "string"
      - name: user_email
        data_type: "string"
      - name: user_name
        data_type: "string"
      - name: is_apella_employee
        data_type: "boolean"
      - name: roles
        data_type: "array<string>"
        description: "An array of the role(s) of the user that fired the event e.g. [HMH West Main Site,HM Charge Nurse]"
      - name: apella_org_id
        data_type: "string"
        description: "The Apella Customer organization ID in human readable format e.g. houston_methodist"
      - name: org_id
        data_type: "string"
        description: "The Apella Customer oganization ID in auth0 e.g. org_Xj37YbZTAOh54edr"
      - name: room_id
        data_type: "string"
      - name: room_id_array
        data_type: "array<string>"
      - name: page_path
        data_type: "string"
      - name: site_id
        data_type: "string"
        description: "There is one site_id for each event, but because some events can occur for multiple site_id's at once 
          (for example a page view of a specific product) there may be rows with identical data except for the site_id"
      - name: event_properties
        data_type: "string"
        description: "A JSON string with the event properties. The type of data encoded here depends on the event_type 
          and might change over time"
