{{ daily_config(expiration_days = 5) }}


select
    {{ ds() }} as ds,
    uuid as id,
    amplitude_id,
    event_id,
    session_id,
    event_type as event_name,
    date(event_time) as event_date,
    event_time as event_timestamp,
    user_id,
    json_extract_scalar(user_properties, '$.emailDomain') as email_domain,
    json_extract_scalar(user_properties, '$.email') as user_email,
    json_extract_scalar(user_properties, '$.name') as user_name,
    cast(json_extract(user_properties, '$.isApellaEmployee') as boolean) as is_apella_employee,
    json_extract(user_properties, '$.roles') as roles,
    json_extract_scalar(user_properties, '$.apellaOrgId') as apella_org_id,
    json_extract_scalar(user_properties, '$.orgId') as org_id,
    json_extract_scalar(event_properties, '$.roomId') as room_id,
    coalesce(
        json_extract_array(event_properties, '$.roomIds'),
        json_extract_array(event_properties, '$.selectedRoomIds')
    ) as room_id_array,
    coalesce(
        json_extract_scalar(replace(event_properties, '[Amplitude] ', ''), '$.Page Path'),
        json_extract_scalar(event_properties, '$.path')
    ) as page_path,
    coalesce(
        json_extract_scalar(event_properties, '$.primarySiteId'),
        json_extract_scalar(event_properties, '$.siteId'),
        json_extract_scalar(site_id)
    ) as site_id,
    event_properties
from {{ api_table_snapshot("bronze", "amplitude_events_export_daily") }},
    unnest(
        if(
            coalesce(
                json_extract_array(event_properties, '$.siteIds'),
                json_extract_array(event_properties, '$.selectedSiteIds')
            ) is null,
            ['None'],
            coalesce(
                json_extract_array(event_properties, '$.siteIds'),
                json_extract_array(event_properties, '$.selectedSiteIds')
            )
        )
    ) as site_id
group by 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21
