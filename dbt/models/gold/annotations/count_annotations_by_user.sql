{{ daily_config_latest_partition() }}


with user_annotation_timestamps as (
    select
        completed_annotations.annotation_finish_time as annotation_finish_time,
        annotation_tasks.annotator_user_id as annotator_user_id
    from {{ api_table_snapshot("bronze", "public_annotation_tasks", "annotation_tasks") }}
    inner join {{ ref("completed_annotations") }} as completed_annotations
        on (annotation_tasks.id = completed_annotations.annotation_task_id)
    where completed_annotations.ds = {{ ds() }}
)

select
    {{ ds() }} as ds,
    extract(date from user_annotation_timestamps.annotation_finish_time at time zone 'America/Los_Angeles')
        as annotation_date_pt,
    coalesce(auth0_users.name, 'UNKNOWN') as user_name,
    count(*) as count
from user_annotation_timestamps
left outer join {{ source("bronze", "auth0_users") }} as auth0_users
    on user_annotation_timestamps.annotator_user_id = auth0_users.user_id and auth0_users.ds = {{ ds() }}
where
    user_annotation_timestamps.annotation_finish_time < timestamp_add({{ ds(as_date=False) }}, interval 1 day)
    and user_annotation_timestamps.annotation_finish_time >= timestamp_sub({{ ds(as_date=False) }}, interval 30 day)
group by annotation_date_pt, user_name
