{{ daily_config_latest_partition() }}


with user_review_timestamps as (
    select
        completed_reviews.review_finish_time as review_finish_time,
        annotation_tasks.reviewer_user_id as reviewer_user_id
    from {{ api_table_snapshot("bronze", "public_annotation_tasks", "annotation_tasks") }}
    inner join {{ ref("completed_reviews") }} as completed_reviews
        on (annotation_tasks.id = completed_reviews.annotation_task_id)
    where completed_reviews.ds = {{ ds() }}
)

select
    {{ ds() }} as ds,
    extract(date from user_review_timestamps.review_finish_time at time zone 'America/Los_Angeles') as review_date_pt,
    coalesce(auth0_users.name, 'UNKNOWN') as user_name,
    count(*) as count
from user_review_timestamps
left outer join {{ source("bronze", "auth0_users") }} as auth0_users
    on user_review_timestamps.reviewer_user_id = auth0_users.user_id and auth0_users.ds = {{ ds() }}
where
    user_review_timestamps.review_finish_time < timestamp_add({{ ds(as_date=False) }}, interval 1 day)
    and user_review_timestamps.review_finish_time >= timestamp_sub({{ ds(as_date=False) }}, interval 30 day)
group by review_date_pt, user_name
