{{ daily_config_latest_partition() }}

with recent_cases as (
    select
        phases.room_id,
        phases.org_id,
        phases.site_id,
        start_events.start_time as start_time,
        end_events.start_time as end_time
    from {{ api_table_snapshot("bronze", "public_phases", alias="phases") }}
    inner join
        {{ api_table_snapshot("bronze", "public_events", alias="start_events") }}
        on phases.start_event_id = start_events.id
    inner join {{ api_table_snapshot("bronze", "public_events", alias="end_events") }}
        on phases.end_event_id = end_events.id
    where
        start_events.start_time > date_sub(
            timestamp({{ ds(as_date=False) }}),
            interval 28 day
        ) and phases.source_type = 'unified' and start_events.deleted_at is null
        and end_events.deleted_at is null
        and phases.status = 'VALID'
        and phases.room_id is not null
        and phases.type_id = 'CASE'
),

-- TODO(DATA-1656): Use annotation task versions.
annotation_tasks_containing_intubations as (
    select id
    from {{ api_table_snapshot("bronze", "public_annotation_task_types", "annotation_task_types") }}
    where exists (
        select * from unnest(json_value_array(event_types)) as e
        where e in unnest({{ get_intubation_events() }})
    )
),

cases_with_annotation_task_status_of_interest as (
    select
        cases.start_time,
        cases.end_time,
        cases.room_id,
        cases.org_id,
        cases.site_id,
        annotation_tasks.status as annotation_status
    from recent_cases as cases
    inner join
        {{ api_table_snapshot("bronze", "public_annotation_tasks", "annotation_tasks") }}
        on
            cases.room_id = annotation_tasks.room_id
            and cases.start_time >= annotation_tasks.start_time
            and cases.end_time <= annotation_tasks.end_time
    inner join annotation_tasks_containing_intubations as intubation_tasks
        on annotation_tasks.type_id = intubation_tasks.id
    where
        annotation_tasks.status in ('CANCELLED', 'DONE')
),

grouped_events_for_cases as (
    select
        cases.start_time,
        events.room_id,
        any_value(events.org_id) as org_id,
        any_value(events.site_id) as site_id,
        array_agg(events.event_type_id) as event_types,
        any_value(cases.annotation_status) as annotation_status
    from {{ api_table_snapshot("bronze", "public_events", "events") }}
    inner join cases_with_annotation_task_status_of_interest as cases
        on (
            events.room_id = cases.room_id
            and events.start_time >= cases.start_time
            and events.start_time < cases.end_time
        )
    group by
        cases.start_time,
        events.room_id
)

select
    {{ ds() }} as ds,
    start_time as case_start_time,
    concat(
        'https://internal.apella.io/',
        org_id, '/', site_id, '/videos/',
        format_timestamp(
            '%Y-%m-%dT%X',
            start_time
        ), '/', room_id,
        '?time=', unix_seconds(start_time)
    ) as link_to_video,
    room_id,
    annotation_status
from grouped_events_for_cases
where not exists (
    select * from unnest(event_types) as e
    where e in unnest({{ get_intubation_events() }})
)
