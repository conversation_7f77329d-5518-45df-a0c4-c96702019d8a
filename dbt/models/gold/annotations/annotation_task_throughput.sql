{{ daily_config_latest_partition() }}

with pivoted_table as (
    select
        annotation_task_id,
        duration_not_started,
        duration_in_progress,
        duration_ready_for_review,
        duration_in_review,
        first_start_time_not_started,
        first_start_time_in_progress,
        first_start_time_ready_for_review,
        first_start_time_in_review,
        first_start_time_done
    from (
        select
            annotation_task_id,
            status,
            duration_seconds,
            first_start_time
        from {{ ref("annotation_task_status_durations") }}
        where ds = {{ ds() }}
    )
    pivot (
        max(duration_seconds) duration,
        min(first_start_time) as first_start_time for status in (
            'NOT_STARTED',
            'IN_PROGRESS',
            'READY_FOR_REVIEW',
            'IN_REVIEW',
            'DONE'
        )
    )
),

latest_completed_annotations as (
    select *
    from {{ ref("completed_annotations") }}
    where ds = {{ ds() }}
),

latest_completed_reviews as (
    select *
    from {{ ref("completed_reviews") }}
    where ds = {{ ds() }}
)

select
    {{ ds() }} as ds,
    annotation_tasks.id as annotation_task_id,
    annotation_tasks.type_id,
    annotation_tasks.org_id,
    annotation_tasks.site_id,
    annotation_tasks.room_id,
    annotation_tasks.start_time,
    annotation_tasks.end_time,
    pivoted_table.first_start_time_not_started as time_not_started,
    pivoted_table.first_start_time_in_progress as time_in_progress,
    pivoted_table.first_start_time_ready_for_review as time_ready_for_review,
    pivoted_table.first_start_time_in_review as time_in_review,
    pivoted_table.first_start_time_done as time_done,
    pivoted_table.duration_not_started as time_to_pick_up_task,
    pivoted_table.duration_in_progress as annotation_duration,
    pivoted_table.duration_ready_for_review as time_to_pick_up_review,
    pivoted_table.duration_in_review as review_duration,
    latest_completed_annotations.annotation_finish_time as annotation_finish_time,
    latest_completed_reviews.review_finish_time as review_finish_time,
    annotation_task_event_counts.count_human_gt as number_of_gt_events,
    annotation_task_event_counts.count_prediction
        as number_of_prediction_events,

    annotation_tasks.annotator_user_id,
    annotation_tasks.reviewer_user_id
from {{ api_table_snapshot("bronze", "public_annotation_tasks", "annotation_tasks") }}
inner join
    pivoted_table
    on annotation_tasks.id = pivoted_table.annotation_task_id
inner join
    {{ ref("annotation_task_event_counts") }} as annotation_task_event_counts
    on
        annotation_tasks.id = annotation_task_event_counts.annotation_task_id
        and annotation_task_event_counts.ds = {{ ds() }}
left outer join
    latest_completed_annotations
    on annotation_tasks.id = latest_completed_annotations.annotation_task_id
left outer join
    latest_completed_reviews
    on annotation_tasks.id = latest_completed_reviews.annotation_task_id
where
    annotation_tasks.status in ('DONE', 'CANCELLED')
