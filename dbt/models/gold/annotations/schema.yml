version: 2

models:
  - name: annotation_task_throughput
    columns:
      - name: annotation_task_id
        data_type: "string"
      - name: type_id
        data_type: "string"
      - name: org_id
        data_type: "string"
      - name: site_id
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: start_time
        data_type: "timestamp"
      - name: end_time
        data_type: "timestamp"
      - name: time_not_started
        data_type: "timestamp"
        description: "The first time this task entered the status 'Not Started'"
      - name: time_in_progress
        data_type: "timestamp"
        description: "The first time this task entered the status 'In Progress'"
      - name: time_ready_for_review
        data_type: "timestamp"
        description: "The first time this task entered the status 'Ready for Review'"
      - name: time_in_review
        data_type: "timestamp"
        description: "The first time this task entered the status 'In Review'"
      - name: time_done
        data_type: "timestamp"
        description: "The first time this task entered the status 'Done'"
      - name: time_to_pick_up_task
        data_type: "int"
        description: "The total amount of time this task spent in the status 'Not Started'"
      - name: annotation_duration
        data_type: "int"
        description: "The total amount of time this task spent in the status 'In Progress'"
      - name: time_to_pick_up_review
        data_type: "int"
        description: "The total amount of time this task spent in the status 'Ready for Review'"
      - name: review_duration
        data_type: "int"
        description: "The total amount of time this task spent in the status 'In Review'"
      - name: number_of_gt_events
        data_type: "int"
      - name: number_of_prediction_events
        data_type: "int"
      - name: annotator_user_id
        data_type: "string"
      - name: reviewer_user_id
        data_type: "string"
      - name: annotation_finish_time
        data_type: "timestamp"
        description: The time this task finished annotation, as defined by silver.completed_annotations.
      - name: review_finish_time
        data_type: "timestamp"
        description: The time this task finished review, as defined by silver.completed_reviews.
  - name: annotation_tasks_missing_intubations
    description: As part of https://linear.app/apella/issue/OBS-1348/missing-intubation-dashboard annotators would like to determine when intubations are missed
    columns:
      - name: link_to_video
        data_type: "string"
      - name: annotation_status
        data_type: "string"
      - name: room_id
        data_type: "string"
      - name: case_start_time
        data_type: "timestamp"
  - name: count_reviews_by_user
    description: Counts the number of tasks reviewed by each user, bucketed by date
    columns:
      - name: review_date_pt
        data_type: "date"
      - name: user_name
        data_type: "string"
      - name: count
        data_type: "int"
  - name: count_annotations_by_user
    description: Counts the number of tasks annotated by each user, bucketed by date
    columns:
      - name: annotation_date_pt
        data_type: "date"
      - name: user_name
        data_type: "string"
      - name: count
        data_type: "int"
