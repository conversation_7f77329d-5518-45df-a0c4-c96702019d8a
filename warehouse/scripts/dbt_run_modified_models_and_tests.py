import argparse
import json
import logging
import os
import subprocess
from dataclasses import dataclass
from datetime import datetime, timedelta

import pytz
from git import Repo

from warehouse.configs.warehouse_config import DBT_PROJECT_DIR
from warehouse.utils.dbt.helpers import get_dbt_manifest

logging.basicConfig(format="%(asctime)s - %(levelname)s - %(message)s", level=logging.INFO)


@dataclass
class ModifiedModels:
    directly_without_macro_change: set[str]
    directly_with_macro_change: set[str]
    only_due_to_macro_change: set[str]


class dbtRepoModelParser(object):
    _MODEL_PATH = "dbt/models"
    _MACROS_PATH = "dbt/macros"

    _SQL_EXTENSION = ".sql"
    _MODIFIED_TYPES = "ARM"

    # temporary for historical_cases until we implement better plumbing to run these models alongside daily models
    # also includes computationally expensive tables
    _BLACKLIST_MODELS = {"historical_cases", "object_model_results_aggregated"}

    def __init__(self, repo_fpath: str):
        repo = Repo(repo_fpath)
        commits = list(repo.iter_commits())

        assert len(commits) > 0, "Current branch has zero commits. Something is off."

        self.main_commit = repo.remotes.origin.refs.main.commit  # type: ignore
        self.latest_commit = commits[0]

        logging.info(
            f"Looking changes between main commit {self.main_commit} and current commit {self.latest_commit}"
        )

    def _is_sql_file_in_dir(self, dir: str, path: str) -> bool:
        return path.startswith(dir) and path.endswith(self._SQL_EXTENSION)

    def _get_modified_names(self, dir: str) -> set[str]:
        return {
            os.path.basename(file_diff.b_path).split(self._SQL_EXTENSION)[0]
            for file_diff in self.main_commit.diff(self.latest_commit)
            if file_diff.change_type in self._MODIFIED_TYPES
            and self._is_sql_file_in_dir(dir, file_diff.b_path)
        }

    def _get_models_dependent_on_macros(self, macros: set[str]) -> set[str]:
        manifest = get_dbt_manifest()
        macro_ids = set(
            [m["unique_id"] for m in manifest["macros"].values() if m["name"] in macros]
        )
        downstream_models = set()
        for node in manifest["nodes"].values():
            macro_deps = set(node["depends_on"]["macros"])
            if node["resource_type"] == "model" and len(macro_deps & macro_ids) > 0:
                downstream_models.add(node["name"])
        return downstream_models

    def get_modified_models(self) -> ModifiedModels:
        directly_modified_models = self._get_modified_names(self._MODEL_PATH).difference(
            self._BLACKLIST_MODELS
        )
        modified_macros = self._get_modified_names(self._MACROS_PATH)
        models_with_modified_macros = self._get_models_dependent_on_macros(
            modified_macros
        ).difference(self._BLACKLIST_MODELS)

        return ModifiedModels(
            directly_without_macro_change=(directly_modified_models - models_with_modified_macros),
            directly_with_macro_change=(directly_modified_models & models_with_modified_macros),
            only_due_to_macro_change=(models_with_modified_macros - directly_modified_models),
        )


class dbtDailyModelRunner(object):
    _RUN_COMMAND = "dbt-run-prod-for-ci"
    _TEST_COMMAND = "dbt-test-prod-for-ci"

    def __init__(self, ds: str, custom_model_prefix: str, models: set[str]):
        self.models = models
        self.ds = ds
        self.custom_model_prefix = custom_model_prefix

    def _run_command(
        self, run_command: str, model_select: str, sandbox_models_for_test: list[str] = []
    ):
        command = ["make", run_command, f"select={model_select}", f"ds={self.ds}"]

        if self.custom_model_prefix:
            command.append(f"custom_model_prefix={self.custom_model_prefix}")

        if run_command == self._TEST_COMMAND:
            command.append(f"sandbox_models_for_test={','.join(sandbox_models_for_test)}")
        elif run_command == self._RUN_COMMAND:
            command.append("threads=4")

        subprocess.run(command, check=True)

    def _capture_generated_models(self) -> list[str]:
        results = json.loads(open(f"{DBT_PROJECT_DIR}/target/run_results.json").read())

        generated_models = [r["unique_id"].split(".")[-1] for r in results["results"]]
        assert len(generated_models) > 0, "Did not find any generated models from the CI run"

        return generated_models

    def _get_model_select(self, models: set[str]) -> str:
        union = " ".join(f"{model}+" for model in models)
        return f"{union},tag:daily"

    def run(self) -> None:
        model_select = self._get_model_select(self.models)

        logging.info("Running added or modified models")
        self._run_command(self._RUN_COMMAND, model_select)

        logging.info("Running the tests of added or modified models")
        self._run_command(self._TEST_COMMAND, model_select, self._capture_generated_models())


def _get_script_default_ds() -> str:
    ds_date = datetime.now() - timedelta(days=1)
    return pytz.timezone("UTC").localize(ds_date).strftime("%Y-%m-%d 00:00:00")


def main(args):
    dbt_repo_model_parser = dbtRepoModelParser(args.repo_abs_filepath)
    modified_models = dbt_repo_model_parser.get_modified_models()

    if len(modified_models.only_due_to_macro_change) > 2:
        logging.info(
            f"Limiting models affected by macro changes from {len(modified_models.only_due_to_macro_change)} to 2"
        )
        modified_models.only_due_to_macro_change = set(
            list(modified_models.only_due_to_macro_change)[:2]
        )
    all_models = (
        modified_models.directly_without_macro_change
        | modified_models.directly_with_macro_change
        | modified_models.only_due_to_macro_change
    )

    if len(all_models) > 0:
        logging.info(
            f"Running models and tests for {len(all_models)} models.\nModels directly modified without macro changes: {modified_models.directly_without_macro_change}.\nModels directly modified and with upstream macro changes: {modified_models.directly_with_macro_change}.\nModels with only upstream macro changes: {modified_models.only_due_to_macro_change}."
        )
        dbtDailyModelRunner(
            args.ds,
            f"PR_{args.pull_request_number}" if args.pull_request_number else None,
            all_models,
        ).run()
    else:
        logging.info("Commit has no changes detected for models, tests, or macros")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Executes `dbt run` and `dbt test` for all models added or modified in the current commit, including their downstream"
    )
    parser.add_argument(
        "--repo-abs-filepath",
        help="absolute filepath of the data-warehouse repo",
        default=os.getcwd(),
    )
    parser.add_argument("--ds", help="which `ds` to run on", default=_get_script_default_ds())
    parser.add_argument("--pull-request-number", default=None)

    args = parser.parse_args()
    main(args)
