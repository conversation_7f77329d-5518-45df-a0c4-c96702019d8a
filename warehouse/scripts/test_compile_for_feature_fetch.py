import logging
from datetime import datetime, timedelta, timezone

from google.cloud.bigquery import Client

from warehouse.configs.warehouse_config import DBT_CONFIG, GCP_PROJECT_ID, WAREHOUSE_ENVIRONMENT

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


if __name__ == "__main__":
    logger.info(
        f"Testing the compiled inference query against the {WAREHOUSE_ENVIRONMENT} data warehouse"
    )
    bigquery_client = Client(project=GCP_PROJECT_ID)
    fpath = f"{DBT_CONFIG['project_dir']}/target/compiled/data_warehouse/models/silver/forecasting/features/forecasting_case_features_combined.sql"

    case_scheduled_date = (datetime.now(timezone.utc) - timedelta(days=2)).date().isoformat()
    ds = datetime.strftime(datetime.now(tz=timezone.utc), "%Y-%m-%d %H:%M:%S")

    query_str = (
        open(fpath, "r")
        .read()
        .replace("{case_scheduled_date}", case_scheduled_date)
        .replace("{ds}", ds)
    )

    logger.info("Running query (this might take a while...)")
    results = bigquery_client.query_and_wait(query_str)
    row_counts = results.total_rows

    logger.info(f"Query returned {row_counts} rows")
    assert row_counts > 0, "No rows returned from query"
