import argparse
import contextlib
import io
import json
import logging
import sys
from typing import List, Union

from google.cloud import bigquery
from google.cloud.exceptions import NotFound

from warehouse.configs.warehouse_config import GCP_PROJECT_ID
from warehouse.utils.dbt.helpers import get_dbt_cli

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s]: %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)

logger = logging.getLogger()

_NUM_MILLISECONDS_PER_DAY = 1000 * 60 * 60 * 24
_REQUIRE_PARTITION_OPTION_NAME = "require_partition_filter"
_PARTITION_EXPIRATION_DAYS_OPTION_NAME = "partition_expiration_days"


@contextlib.contextmanager
def _suppress_stdout():
    """
    This is a nice-to-have when reading the output of the script, since the
    DBT CLI `stream_raw_events` function echoes everything from the command
    to stdout. This makes it hard to parse the script output and follow the
    core logic of the script.
    """
    new_target = io.StringIO()
    old_target, sys.stdout = sys.stdout, new_target
    try:
        yield new_target
    finally:
        sys.stdout = old_target


def _get_sql(table: str, value: Union[int, bool, None], option_name: str) -> str:
    return f"""
    alter table `{table}`
    set options ({option_name} = {'null' if value is None else value});
    """


def _get_dbt_tables() -> List[tuple[str, dict]]:
    cli = get_dbt_cli()
    invocation = cli.cli(
        args=["ls", "--output", "json", "--vars", '{ds: "____-__-__ __:__:__"}'],
        raise_on_error=True,
    )
    tables = []
    with _suppress_stdout():
        events = list(invocation.stream_raw_events())
    for event in events:
        eventstr = str(event)
        try:
            tables.append(json.loads(eventstr))
        except json.decoder.JSONDecodeError:
            logging.warning(f"Non-table `ls` output detected: {eventstr[:25]}...")
    return [
        (
            f'{GCP_PROJECT_ID}.{table["config"]["schema"]}.{table["name"]}',
            {
                _PARTITION_EXPIRATION_DAYS_OPTION_NAME: table["config"].get(
                    "partition_expiration_days"
                ),
                _REQUIRE_PARTITION_OPTION_NAME: table["config"].get("require_partition_filter"),
            },
        )
        for table in tables
        if table["resource_type"] == "model"
    ]


def _table_exists(client: bigquery.Client, table_id: str) -> bool:
    try:
        client.get_table(table_id)
        return True
    except NotFound:
        return False


def _get_current_table_option(
    client: bigquery.Client, table_id: str, option_name: str
) -> Union[int, bool, None]:
    """Get the current value of a table option."""
    table_info = client.get_table(table_id)
    if option_name == _PARTITION_EXPIRATION_DAYS_OPTION_NAME:
        if not table_info.partition_expiration:
            return None
        if table_info.partitioning_type == "DAY":
            return table_info.partition_expiration / _NUM_MILLISECONDS_PER_DAY
        raise ValueError(
            f"Unexpected partition value, {table_info.partition_expiration}, with partitioning type {table_info.partitioning_type} for {table_id}."
        )
    return getattr(table_info, option_name, None)


def _confirm_run(prompt: str) -> bool:
    user_input = input(prompt).strip().lower()
    if user_input == "y":
        return True
    logger.warning("Not applying change.")
    return False


def update_table_option(option_name: str):
    """Update a specific table option based on dbt configuration."""
    tables = _get_dbt_tables()
    client = bigquery.Client(project=GCP_PROJECT_ID)

    for table_id, options in tables:
        if _table_exists(client, table_id):
            config_value = options.get(option_name)
            curr_value = _get_current_table_option(client, table_id, option_name)

            if curr_value == config_value:
                logger.info(f"{table_id} already has {option_name}={curr_value}. No update needed.")
                continue

            prompt = f"{table_id} will be set to {option_name}={config_value}. Currently: {curr_value}. Apply change? [y/n] "
            if _confirm_run(prompt):
                client.query(_get_sql(table_id, config_value, option_name)).result()
                logger.info(
                    f"Changed {option_name} for {table_id} from {curr_value} to {config_value}."
                )
        else:
            logger.warning(f"Table not found: {table_id}")
    logger.info("Done.")


def main():
    parser = argparse.ArgumentParser(
        description="Manage BigQuery table partition options for dbt models."
    )
    subparsers = parser.add_subparsers(dest="command", required=True)
    subparsers.add_parser(
        "update-retention", help="Update the partition retention based on dbt configuration."
    )
    subparsers.add_parser(
        "update-require-filter",
        help="Set or unset require_partition_filter based on dbt configuration.",
    )
    args = parser.parse_args()
    if args.command == "update-retention":
        update_table_option(_PARTITION_EXPIRATION_DAYS_OPTION_NAME)
    elif args.command == "update-require-filter":
        update_table_option(_REQUIRE_PARTITION_OPTION_NAME)


if __name__ == "__main__":
    main()
