import logging
from collections import defaultdict

import fire
from google.cloud import bigquery

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s]: %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)

logger = logging.getLogger()


_ENV_TO_PROJECTS = {
    "dev": "dev-data-platform-439b4c",
    "prod": "prod-data-platform-027529",
}


class Migrator:
    MIGRATED_SUFFIX = "__migrated"
    DEPRECATED_SUFFIX = "__deprecated"

    def __init__(self, project_id, allowlist, denylist):
        self.client = bigquery.Client(project_id)
        self.allowlist = allowlist
        self.denylist = denylist
        self.tables = self.get_tables()

    def get_ds_col(self, obj):
        return [f for f in obj.schema if f.name == "ds"][0]

    def is_original_table(self, obj):
        return (
            obj.time_partitioning
            and obj.time_partitioning.field == "ds"
            and obj.time_partitioning.type_ == "DAY"
            and self.get_ds_col(obj).field_type.lower() == "timestamp"
        )

    def is_migrated_table(self, obj):
        return (
            obj.time_partitioning
            and obj.time_partitioning.field == "ds"
            and obj.time_partitioning.type_ == "DAY"
            and self.get_ds_col(obj).field_type.lower() == "date"
        )

    def get_canonical_name(self, name):
        return name.split(self.MIGRATED_SUFFIX)[0].split(self.DEPRECATED_SUFFIX)[0]

    def get_tables_for_dataset(self, dataset_id):
        dataset_ref = self.client.dataset(dataset_id)
        tables = self.client.list_tables(dataset_ref)

        tables_lu = defaultdict(dict)

        for table in tables:
            ref = dataset_ref.table(table.table_id)
            obj = self.client.get_table(ref)

            check = f"{obj.dataset_id}.{obj.table_id}"

            if (self.allowlist and check not in self.allowlist) or (
                self.denylist and check in self.denylist
            ):
                logger.info(f"Skipping {check} due to allowlist or denylist")
                continue

            if "dbt_tmp" in check:
                logger.info(f"Skipping {check} due to it being a dbt_tmp table")
                continue

            canonical_name = self.get_canonical_name(obj.table_id)

            if self.is_original_table(obj):
                tables_lu[canonical_name]["original"] = obj
            elif self.is_migrated_table(obj):
                tables_lu[canonical_name]["migrated"] = obj
            else:
                logger.info(
                    f"Skipping migration for unrecognized partition table: {obj.full_table_id}"
                )

        return tables_lu

    def get_tables(self):
        tables = defaultdict(dict)
        for dataset in ("bronze", "silver", "gold"):
            tables.update(self.get_tables_for_dataset(dataset))
        return tables

    def create_migrated_tables(self):
        for canonical_name, tables in self.tables.items():
            original, migrated = tables.get("original"), tables.get("migrated")
            assert canonical_name == original.table_id, "Something weird going on"

            if migrated is not None:
                assert migrated.table_id.endswith(self.MIGRATED_SUFFIX)
                logger.info(
                    f"Migrated table already found: {self.tables[canonical_name]['migrated']}"
                )
            else:
                logger.info(f"Cloning {original.full_table_id} with schema: {original.schema}")
                migrated_table_name = canonical_name + self.MIGRATED_SUFFIX

                migrated = bigquery.Table(
                    self.client.dataset(original.dataset_id).table(migrated_table_name),
                    schema=[
                        bigquery.SchemaField(
                            "ds", "DATE", field.mode, description=field.description
                        )
                        if field.name == "ds"
                        else field
                        for field in original.schema
                    ],
                )

                migrated.time_partitioning = bigquery.TimePartitioning(
                    type_=bigquery.TimePartitioningType.DAY,  # granularity
                    field="ds",  # column name
                    expiration_ms=original.time_partitioning.expiration_ms,  # expiration
                )

                migrated = self.client.create_table(migrated)
                logger.info(f"Cloned {migrated.full_table_id} with new schema: {migrated.schema}")

    def populate_migrated_tables(self):
        for canonical_name, tables in self.tables.items():
            original, migrated = tables.get("original"), tables.get("migrated")
            assert (
                original.table_id == canonical_name
                and migrated.table_id == f"{canonical_name}{self.MIGRATED_SUFFIX}"
            )

            assert [s for s in original.schema if s.name != "ds"] == [
                s for s in migrated.schema if s.name != "ds"
            ]

            columns = ",\n".join(
                "cast(ds as date)" if field.name == "ds" else f"`{field.name}`"
                for field in original.schema
            )
            migrated_table_name = f"{migrated.dataset_id}.{migrated.table_id}"

            # Construct the SQL query to copy data
            query = f"""
            truncate table `{migrated_table_name}`;
            insert into `{migrated_table_name}`
            select
                {columns}
            from `{original.dataset_id}.{original.table_id}`
            """

            logger.info(f"Executing {query}")

            job = self.client.query(query)
            job.result()

            migrated = self.client.get_table(migrated_table_name)

            assert migrated.num_rows == original.num_rows, "Copy failed"

            logger.info(
                f"Copied data from {original.table_id} to {migrated.table_id} with transformed partition column."
            )

    def rename(self, from_, to_):
        job = self.client.query(f"alter table `{from_}` rename to `{to_}`")
        job.result()

    def _do_rename(
        self,
        canonical_name,
        live_table,
        live_table_new_suffix,
        staging_table,
        expected_staging_table_suffix,
    ):
        assert live_table.table_id == canonical_name
        assert staging_table.table_id.endswith(expected_staging_table_suffix)

        # handle live table
        original_row_count = live_table.num_rows
        new_live_table_id = f"{canonical_name}{live_table_new_suffix}"
        self.rename(f"{live_table.dataset_id}.{live_table.table_id}", new_live_table_id)

        assert (
            original_row_count
            == self.client.get_table(f"{live_table.dataset_id}.{new_live_table_id}").num_rows
        )

        logger.info(f"Renamed {live_table.full_table_id} to {new_live_table_id}")

        new_staging_table_id = f"{staging_table.dataset_id}.{canonical_name}"
        self.rename(f"{staging_table.dataset_id}.{staging_table.table_id}", canonical_name)

        assert original_row_count == self.client.get_table(new_staging_table_id).num_rows

        logger.info(f"Renamed {staging_table.full_table_id} to {new_staging_table_id}")

    def migrate_forward_rename(self):
        for canonical_name, tables in self.tables.items():
            original, migrated = tables.get("original"), tables.get("migrated")

            if (
                original.table_id.endswith(self.DEPRECATED_SUFFIX)
                and migrated.table_id == canonical_name
            ):
                logger.info(f"Skipping {canonical_name} as it has already been migrated forward")
                continue

            self._do_rename(
                canonical_name,
                original,
                self.DEPRECATED_SUFFIX,
                migrated,
                self.MIGRATED_SUFFIX,
            )

    def migrate_backwards_rename(self):
        for canonical_name, tables in self.tables.items():
            original, migrated = tables.get("original"), tables.get("migrated")

            if (
                migrated.table_id.endswith(self.MIGRATED_SUFFIX)
                and original.table_id == canonical_name
            ):
                logger.info(f"Skipping {canonical_name} as it has already been migrated backwards")
                continue

            self._do_rename(
                canonical_name,
                migrated,
                self.MIGRATED_SUFFIX,
                original,
                self.DEPRECATED_SUFFIX,
            )

    def check_table_equality(self, original, migrated):
        original_table_id = f"{original.dataset_id}.{original.table_id}"
        migrated_table_id = f"{migrated.dataset_id}.{migrated.table_id}"

        coalesce_statements = []

        for field in original.schema:
            if (
                field.name == "ds"
                or field.mode == "REPEATED"
                or field.field_type in ("RECORD", "JSON")
            ):
                continue

            column_name = field.name
            column_type = field.field_type

            default_value = {
                "STRING": "'__null__'",
                "INT64": "0",
                "FLOAT64": "0.0",
                "TIMESTAMP": "timestamp('1970-01-01 00:00:00')",
                "DATE": "date('1970-01-01')",
                "BOOLEAN": "true",
                "BYTES": "b'__null__'",
                "INTEGER": "0",
                "BIGNUMERIC": "0",
                "DATETIME": "datetime('1970-01-01 00:00:00')",
                "NUMERIC": "0",
                "TIME": "extract(time from timestamp('1970-01-01 00:00:00'))",
                "FLOAT": "0",
            }[column_type]

            coalesce_statements.append(
                f"coalesce(`{column_name}`, {default_value}) as `{column_name}`"
            )
            # Join the COALESCE statements with commas and create the final query

        columns = ",\n".join(coalesce_statements)

        sql = f"""
        with migrated as (
          select
            {columns}
          from `{migrated_table_id}`
        ),
        original as (
          select
            {columns}
          from `{original_table_id}`
        ),
        migrated_minus_original as (
          select * from migrated
          except distinct
          select * from original
        ),
        original_minus_migrated as (
          select * from original
          except distinct
          select * from migrated
        )

        select
          case
            when exists (select 1 from migrated_minus_original) then 'tables are not equal: rows in migrated not in original'
            when exists (select 1 from original_minus_migrated) then 'tables are not equal: rows in original not in migrated'
            else 'tables are equal'
          end as result
        ;
        """

        job = self.client.query(sql)
        logger.info(
            f"""
            Checking equality between {original_table_id} and {migrated_table_id}:
            {list(job.result())}
            """.strip()
        )

    def run_check_table_equality(self):
        for canonical_name, tables in self.tables.items():
            original, migrated = tables.get("original"), tables.get("migrated")
            self.check_table_equality(original, migrated)


def migrate(
    env,
    create_migrated_tables=False,
    copy_data_into=False,
    migrate_forward_rename=False,
    migrate_backwards_rename=False,
    allowlist=[],
    denylist=[],
    run_check_table_equality=False,
):
    m = Migrator(_ENV_TO_PROJECTS[env], allowlist, denylist)

    if create_migrated_tables:
        logger.info("create_migrated_tables")
        m.create_migrated_tables()
    elif copy_data_into:
        logger.info("copy_data_into")
        m.populate_migrated_tables()
    elif migrate_forward_rename:
        logger.info("migrate_forward_rename")
        m.migrate_forward_rename()
    elif migrate_backwards_rename:
        logger.info("migrated_backwards_rename")
        m.migrate_backwards_rename()
    elif run_check_table_equality:
        logger.info("run_check_table_equality")
        m.run_check_table_equality()


if __name__ == "__main__":
    fire.Fire(migrate)
