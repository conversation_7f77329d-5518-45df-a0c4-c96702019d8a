from contextlib import contextmanager
from time import perf_counter
from typing import Iterator

from dagster._utils.log import create_console_logger


@contextmanager
def timer(description: str) -> Iterator:
    start = perf_counter()

    # wrapped code block executes
    yield

    ellapsed_time = perf_counter() - start

    logger = create_console_logger("timer", "INFO")
    logger.info(f"{description} took {ellapsed_time:.3f}s")
