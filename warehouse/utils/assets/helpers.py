from datetime import date, datetime

from dagster import DailyPartitionsDefinition

from warehouse.configs.warehouse_config import DEFAULT_PARTITION_TIMEZONE, DS_FORMAT


def get_date_from_ds_str(ds_str: str) -> date:
    return datetime.strptime(ds_str, DS_FORMAT).date()


def _get_daily_partitions_def_with_offset(
    end_offset: int,
    timezone: str | None,
) -> DailyPartitionsDefinition:
    if timezone is None:
        timezone = DEFAULT_PARTITION_TIMEZONE

    return DailyPartitionsDefinition(
        start_date=datetime(2023, 6, 1, 0, 0, 0),
        fmt=DS_FORMAT,
        end_offset=end_offset,
        timezone=timezone,
    )


# The default dagster behavior. Each partition corresponds to a completed time window.
# For example, the schedule will fill in the 2024-01-01 00:00:00 UTC partition on
# 2024-01-02 00:00:00 UTC.
def get_previous_day_partitions_def(
    timezone=None,
) -> DailyPartitionsDefinition:
    return _get_daily_partitions_def_with_offset(0, timezone)


# Each partition corresponds to the day the job was run.
# For example, the schedule will fill in the 2024-01-01 00:00:00 UTC partition on
# 2024-01-01 00:00:00 UTC.
def get_day_of_partitions_def(timezone=None) -> DailyPartitionsDefinition:
    return _get_daily_partitions_def_with_offset(1, timezone)
