from typing import Sequence, cast

from dagster import Output<PERSON>ontext
from dagster._core.storage.db_io_manager import (
    TablePartitionDimension,
    TableSlice,
    TimeWindow,
)
from dagster_gcp.bigquery.io_manager import BigQueryClient
from google.cloud import bigquery
from google.cloud.exceptions import NotFound


class DatePartitionedBigQueryClient(BigQueryClient):
    BIGQUERY_DATE_FORMAT = "%Y-%m-%d"

    @staticmethod
    def ensure_schema_exists(
        context: OutputContext,
        table_slice: TableSlice,
        connection: bigquery.Client,
    ) -> None:
        """
        The parent method creates a provided schema if it doesn't exist.

        We want to have tight control over which schemas are created in the
        warehouse, so we override this method to simply check schema existence.
        """
        try:
            connection.get_dataset(table_slice.schema)
            return None
        except NotFound:
            context.log.error(
                "Creating BigQuery datasets in Dagster is not allowed. All"
                " datasets and their entitlements are managed via"
                " Terraform, within the repo `tf-data-platform`"
            )
            raise

    @staticmethod
    def delete_table_slice(context: Output<PERSON>ontext, table_slice: TableSlice, connection) -> None:
        """
        Overwrite this method to do nothing. Context:

        On data load, the Dagster BigQueryClient will either:

            - delete a table partition if the corresponding Dagster asset is
              partitioned
            - truncate the entire table if the corresponding Dagster asset is
              not partitioned

        This is not ideal, because:

            - table write behavior is shared between the internal Dagster logic
              and the actual google.cloud LoadJobConfig that's passed into the
              native google.cloud bigquery client
            - Dagster performs these separate statements over multiple
              transactions: e.g., a `DELETE FROM` and then a `INSERT`
            - this logic is totally implicit and hidden in the Dagster source

        The native google.cloud BigQuery client supports insert-overwrite
        partition logic, performed in one swoop on table load, so we should
        be using that instead. It's also better to define how data is written
        to the final table *in one place* using native APIs, instead of custom
        and implicit logic sharded across libraries.
        """
        return

    @staticmethod
    def get_select_statement(table_slice: TableSlice) -> str:
        """
        We need to overwrite this simply to call a modified version of `_time_window_where_clause`,
        which in `dagster-gcp` is a module-level method.

        https://github.com/dagster-io/dagster/blob/****************************************/python_modules/libraries/dagster-gcp/dagster_gcp/bigquery/io_manager.py#L390-L395

        The `dagster-gcp` version of this method is problematic because it assumes every time partition column
        can be compared to their global variable BIGQUERY_DATE_FORMAT

        This breaks when the data type of a partition column is `date`. For now we are changing this
        to only support `date` partition types. If folks need another partition type they can use the
        standard Dagster I/O managers and clients.

        All methods below are copy/pasted from Dagster except `_time_window_where_clause`
        """
        col_str = ", ".join(table_slice.columns) if table_slice.columns else "*"

        table_id = f"{table_slice.database}.{table_slice.schema}.{table_slice.table}"

        if table_slice.partition_dimensions and len(table_slice.partition_dimensions) > 0:
            query = f"SELECT {col_str} FROM" f" `{table_id}` WHERE\n"
            return query + DatePartitionedBigQueryClient._partition_where_clause(
                table_slice.partition_dimensions
            )
        else:
            return f"""SELECT {col_str} FROM `{table_id}`"""

    @staticmethod
    def _partition_where_clause(partition_dimensions: Sequence[TablePartitionDimension]) -> str:
        """
        Copy/pasted from Dagster
        """
        return " AND\n".join(
            (
                DatePartitionedBigQueryClient._time_window_where_clause(partition_dimension)
                if isinstance(partition_dimension.partitions, TimeWindow)
                else DatePartitionedBigQueryClient._static_where_clause(partition_dimension)
            )
            for partition_dimension in partition_dimensions
        )

    @staticmethod
    def _time_window_where_clause(table_partition: TablePartitionDimension) -> str:
        """
        Copy/pasted from Dagster except the return statement (see `get_select_statement` docstring)
        """
        partition = cast(TimeWindow, table_partition.partitions)
        start_dt, _ = partition

        start_dt_str = start_dt.strftime(DatePartitionedBigQueryClient.BIGQUERY_DATE_FORMAT)

        return f"{table_partition.partition_expr} = '{start_dt_str}'"

    @staticmethod
    def _static_where_clause(table_partition: TablePartitionDimension) -> str:
        """
        Copy/pasted from Dagster
        """
        partitions = ", ".join(f"'{partition}'" for partition in table_partition.partitions)
        return f"""{table_partition.partition_expr} in ({partitions})"""
