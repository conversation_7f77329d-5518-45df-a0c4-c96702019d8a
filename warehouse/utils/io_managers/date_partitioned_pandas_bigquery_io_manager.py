from typing import Generator, Optional, Sequence, Type

import pandas as pd
from dagster import OutputContext
from dagster._core.storage.db_io_manager import (
    DbIOManager,
    DbTypeHandler,
)
from dagster_gcp.bigquery.io_manager import BigQueryIOManager
from dagster_gcp.bigquery.utils import setup_gcp_creds

from warehouse.utils.io_managers.date_partitioned_bigquery_client import (
    DatePartitionedBigQueryClient,
)
from warehouse.utils.io_managers.date_partitioned_bigquery_pandas_type_handler import (  # noqa: E501
    DatePartitionedBigQueryPandasTypeHandler,
)


class DatePartitionedPandasBigQueryIOManager(BigQueryIOManager):
    def create_io_manager(self, _: OutputContext) -> Generator:
        mgr = DbIOManager(
            db_client=DatePartitionedBigQueryClient(),
            io_manager_name="DatePartitionedPandasBigQueryIOManager",
            database=self.project,
            schema=self.dataset,
            type_handlers=self.type_handlers(),
            default_load_type=self.default_load_type(),
        )
        if self.gcp_credentials:
            with setup_gcp_creds(self.gcp_credentials):
                yield mgr
        else:
            yield mgr

    @staticmethod
    def type_handlers() -> Sequence[DbTypeHandler]:
        return [DatePartitionedBigQueryPandasTypeHandler()]

    @staticmethod
    def default_load_type() -> Optional[Type]:
        return pd.DataFrame
