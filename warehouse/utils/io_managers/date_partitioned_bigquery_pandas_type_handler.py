from datetime import date
from typing import Any, List, Optional, <PERSON><PERSON>

import pandas as pd
from dagster import (
    InputContext,
    MetadataValue,
    OutputContext,
    TableColumn,
    TableSchema,
    get_dagster_logger,
)
from dagster._core.storage.db_io_manager import <PERSON><PERSON><PERSON>
from dagster_gcp_pandas import BigQueryPandasTypeHandler
from google.cloud import bigquery
from google.cloud.bigquery import <PERSON>adJobConfig, <PERSON>hemaField
from google.cloud.bigquery.table import TimePartitioning, TimePartitioningType

from warehouse.utils.io_managers.date_partitioned_bigquery_client import (
    DatePartitionedBigQueryClient,
)

_BQ_PARTITION_FORMATS = {
    TimePartitioningType.DAY: "%Y%m%d",
}


class DatePartitionedBigQueryPandasTypeHandler(BigQueryPandasTypeHandler):
    """
    This type handler performs an insert-overwrite for provided partitions on
    the final table load. If the provided partition does not exist, it is created
    alongside existing partitions. Otherwise, it is replaced.
    """

    def _get_write_partition_for_bq(
        self, df: pd.DataFrame, partition_column: str, partition_cadence: str
    ) -> <PERSON><PERSON>[str, str]:
        assert partition_column in df.columns, (
            f"Expected `{partition_column}` as a partition"
            " column in your dataframe being written to BigQuery"
        )
        assert (
            len(df[partition_column].drop_duplicates()) == 1
        ), "You must load one partition at a time with this utility"

        partition_cadence = partition_cadence.upper()

        assert partition_cadence in _BQ_PARTITION_FORMATS, (
            f"Unsupported BigQuery partition cadence {partition_cadence}. See"
            " GCP docs for `TimePartitioningType`"
        )

        partition_value = df[partition_column].iloc[0]

        assert type(partition_value) is date, (
            f"`{partition_column}` partition column must be a `datetime.date`. Got"
            f" {type(partition_value)}"
        )

        return (
            partition_cadence,
            partition_value.strftime(_BQ_PARTITION_FORMATS[partition_cadence]),
        )

    def _get_schema_field(self, key: str, value: Any) -> SchemaField:  # type: ignore[return]
        if isinstance(value, dict):
            return SchemaField(
                name=key,
                field_type="struct",
                fields=[
                    self._get_schema_field(value_key, value_type)
                    for value_key, value_type in value.items()
                ],
            )
        elif isinstance(value, list):
            if isinstance(value[0], dict):
                return SchemaField(
                    name=key,
                    field_type="struct",
                    mode="REPEATED",
                    fields=[
                        self._get_schema_field(value_key, value_type)
                        for value_key, value_type in value[0].items()
                    ],
                )
            else:
                return SchemaField(
                    name=key,
                    field_type=value[0],
                    mode="REPEATED",
                )
        elif isinstance(value, str):
            return SchemaField(name=key, field_type=value)
        assert ValueError(f"Unsupported datatype {value}")

    def _get_schema(
        self,
        df: pd.DataFrame,
        provided_schema: Optional[dict[str, Any]],
        partition_column: str,
    ) -> List[Optional[SchemaField]]:
        if provided_schema is not None:
            return [self._get_schema_field(k, v) for k, v in provided_schema.items()]
        else:
            return [
                SchemaField(
                    name=df_col,
                    field_type=("date" if df_col == partition_column else "string"),
                )
                for df_col in df.columns
            ]

    def load_input(
        self, context: InputContext, table_slice: TableSlice, connection: bigquery.Client
    ) -> pd.DataFrame:
        """
        This method is copy-pasted from the standard Pandas BQ handler, except for using our
        DatePartitionedBigQueryClient to generate the select statement
        """
        if table_slice.partition_dimensions and len(context.asset_partition_keys) == 0:
            return pd.DataFrame()
        result = connection.query(
            query=DatePartitionedBigQueryClient.get_select_statement(table_slice),
            project=table_slice.database,
            location=context.resource_config.get("location") if context.resource_config else None,
            timeout=context.resource_config.get("timeout") if context.resource_config else None,
        ).to_dataframe()

        result.columns = map(str.lower, result.columns)
        return result

    def handle_output(
        self,
        context: OutputContext,
        table_slice: TableSlice,
        df: pd.DataFrame,
        connection: bigquery.Client,
    ):
        if len(df) == 0:
            get_dagster_logger().warn("No data to write to BigQuery. Skipping write.")
            return

        assert (
            context.metadata is not None
            and "partition_expr" in context.metadata
            and "partition_cadence" in context.metadata
        ), (
            "Assets using this I/O manager must provide metadata with keys"
            " `partition_expr`, `partition_cadence`"
        )

        partition_column = context.metadata["partition_expr"]
        partition_cadence = context.metadata["partition_cadence"]

        partition_cadence, write_partition = self._get_write_partition_for_bq(
            df, partition_column, partition_cadence
        )

        df = df[[column for column in df.columns]]

        assert (
            table_slice.schema is not None and table_slice.database is not None
        ), f"Input table_slice {table_slice} must not have null fields"
        job = connection.load_table_from_dataframe(
            dataframe=df,
            destination=(f"{table_slice.schema}.{table_slice.table}${write_partition}"),
            project=table_slice.database,
            location=(
                context.resource_config.get("location")  # type: ignore[arg-type]
                if context.resource_config
                else None  # ignore [arg-type]
            ),
            timeout=(
                context.resource_config.get("timeout")  # type: ignore[arg-type]
                if context.resource_config
                else None
            ),
            job_config=LoadJobConfig(
                time_partitioning=TimePartitioning(
                    type_=partition_cadence,
                    field=partition_column,
                ),
                write_disposition="WRITE_TRUNCATE",
                schema=self._get_schema(df, context.metadata.get("table_schema"), partition_column),
                schema_update_options=[bigquery.SchemaUpdateOption.ALLOW_FIELD_ADDITION],
            ),
        )

        job.result()

        context.add_output_metadata(
            {
                "row_count": df.shape[0],
                "dataframe_columns": MetadataValue.table_schema(
                    TableSchema(
                        columns=[
                            TableColumn(name=str(name), type=str(dtype))
                            for name, dtype in df.dtypes.items()
                        ]
                    )
                ),
            }
        )
