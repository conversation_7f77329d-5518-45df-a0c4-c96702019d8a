import json
import os
from functools import lru_cache
from typing import Any

from dagster import (
    AssetKey,
    AssetsDefinition,
    OpExecutionContext,
    PartitionsDefinition,
    RetryRequested,
)
from dagster._annotations import public
from dagster_dbt import (
    DagsterDbtTranslator,
    DagsterDbtTranslatorSettings,
    DbtCliResource,
    dbt_assets,
)
from pyparsing import Mapping

from warehouse.configs.warehouse_config import (
    DBT_CONFIG,
    DBT_PROJECT_DIR,
    WarehouseDatasets,
)


def get_dbt_cli() -> DbtCliResource:
    return DbtCliResource(
        project_dir=DBT_CONFIG["project_dir"],
        profiles_dir=DBT_CONFIG["profiles_dir"],
        target=DBT_CONFIG["target"],
    )


@lru_cache()
def get_dbt_manifest():
    return json.load(open(f"{DBT_PROJECT_DIR}/target/manifest.json"))


class DBTScheduleCadences:
    DAILY = "daily"


class DBTTranslator(DagsterDbtTranslator):
    def __init__(self):
        super().__init__(settings=DagsterDbtTranslatorSettings(enable_asset_checks=False))

    @classmethod
    @public
    def get_asset_key(cls, dbt_resource_props: Mapping[str, Any]) -> AssetKey:
        if dbt_resource_props["resource_type"] == "test":
            return AssetKey([dbt_resource_props["name"]])
        return AssetKey(
            [
                dbt_resource_props["schema"],
                dbt_resource_props["name"],
            ]
        )

    @classmethod
    @public
    def get_group_name(cls, dbt_resource_props: Mapping[str, Any]) -> str:
        path = dbt_resource_props["path"]
        path_components = path.split(os.path.sep)

        dataset = path_components.pop(0)
        dw_datasets = [v.value for v in WarehouseDatasets.__members__.values()]

        assert dataset in dw_datasets, (
            f"Unexpected model path from DBT: dbt/models/{path}. "
            "Models must be placed dbt/models/<target_dataset>/<project_name>, "
            f"where the target dataset is one of {dw_datasets}. Got: {dataset}."
        )

        return path_components.pop(0)


def dbt_asset_factory(asset_name: str, partitions_def: PartitionsDefinition) -> AssetsDefinition:
    def compute(context: OpExecutionContext, dbt: DbtCliResource):
        # TODO: https://linear.app/apella/issue/DATA-1827/unified-cli-for-generating-dbtpython-assets-in-sandbox
        if os.environ.get("WAREHOUSE_RUNTIME_ENVIRONMENT") == "local":
            raise RuntimeError(
                "dbt assets must be materialized using the Makefile commands 'dbt-run-dev' or 'dbt-run-prod' when running locally"
            )

        try:
            yield from dbt.cli(
                args=[
                    "--debug",
                    "run",
                    "--vars",
                    json.dumps(
                        {
                            "ds": context.partition_key,
                            # only sync dbt docs to bq upon successful asset run through Dagster
                            "enable_persist_docs_to_bq": True,
                        }
                    ),
                ],
                context=context,
            ).stream()
        except Exception as e:
            raise RetryRequested(max_retries=3, seconds_to_wait=3) from e

    compute.__name__ = asset_name

    my_dbt_asset = dbt_assets(
        manifest=get_dbt_manifest(),
        select=asset_name,
        dagster_dbt_translator=DBTTranslator(),
        partitions_def=partitions_def,
    )(compute)

    return my_dbt_asset
