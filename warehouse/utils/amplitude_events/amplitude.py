import gzip
import json
import zipfile
from dataclasses import dataclass
from datetime import datetime, timedelta
from decimal import Decimal
from io import BytesIO
from uuid import UUID

import requests
from dagster import ConfigurableResource, InitResourceContext
from dagster._utils.log import create_console_logger
from dataclasses_json import DataClassJsonMixin
from google.cloud.secretmanager import SecretManagerServiceClient
from pydantic import PrivateAttr


# Per https://amplitude.com/docs/apis/analytics/export#response-schema
@dataclass
class AmplitudeEvent(DataClassJsonMixin):
    server_received_time: datetime
    app: int
    device_carrier: str
    city: str
    user_id: str
    uuid: UUID
    event_time: datetime
    platform: str
    os_version: str
    amplitude_id: Decimal
    processed_time: datetime
    version_name: str
    ip_address: str
    paying: bool
    dma: str
    group_properties: str
    user_properties: str
    client_upload_time: datetime
    insert_id: str
    event_type: str
    library: str
    amplitude_attribution_ids: str
    device_type: str
    start_version: str
    location_lng: float
    server_upload_time: datetime
    event_id: int
    location_lat: float
    os_name: str
    groups: str
    event_properties: str
    data: str
    device_id: str
    language: str
    country: str
    region: str
    session_id: Decimal
    device_family: str
    sample_rate: Decimal | None
    client_event_time: datetime

    def __init__(self, raw_event: dict) -> None:
        for key, value in raw_event.items():
            if key in ["$insert_key", "$schema"]:
                continue
            elif key in ["amplitude_id", "session_id", "sample_rate"] and value is not None:
                value = Decimal(value)
            elif key in [
                "group_properties",
                "user_properties",
                "groups",
                "event_properties",
                "data",
            ]:
                value = json.dumps(value)
            elif key in [
                "server_received_time",
                "event_time",
                "processed_time",
                "client_upload_time",
                "server_upload_time",
                "client_event_time",
            ]:
                value = datetime.strptime(value, "%Y-%m-%d %H:%M:%S.%f")
            elif key == "$insert_id":
                key = "insert_id"

            setattr(self, key, value)


AMPLITUDE_DATE_TIME_FORMAT = "%Y%m%dT%H"


def parse_newline_delimited_json(js: str):
    return json.loads("[" + js.replace("\n", ",")[:-1] + "]")


class AmplitudeExport:
    URL = "https://amplitude.com/api/2/export"
    logger = create_console_logger("AmplitudeExport", "INFO")

    def __init__(self, api_key, secret_key) -> None:
        self.__auth = (api_key, secret_key)

    def _response_handler(self, response) -> list[dict]:
        response.raise_for_status()
        content = zipfile.ZipFile(BytesIO(response.content))
        data = list()
        for name in content.namelist():
            with content.open(name) as gz:
                raw_js = gzip.decompress(gz.read())
                js = raw_js.decode("utf-8")
                data.extend(parse_newline_delimited_json(js))
        return data

    def export(self, start, end) -> list[dict]:
        params = {"start": start, "end": end}
        self.logger.info(f"Fetching Amplitude data from {start} to {end}")
        response = requests.get(self.URL, params=params, auth=self.get_auth(), stream=True)
        return self._response_handler(response)

    def get_auth(self):
        return self.__auth

    @staticmethod
    def get_intraday_range(event_date: str) -> tuple[str, str]:
        event_datetime = datetime.strptime(event_date, "%Y-%m-%d %H:%M:%S")
        start = event_datetime.replace(hour=0).strftime(AMPLITUDE_DATE_TIME_FORMAT)
        # end is next day with hour 0
        end = (
            (event_datetime + timedelta(days=1))
            .replace(hour=0)
            .strftime(AMPLITUDE_DATE_TIME_FORMAT)
        )
        return start, end


class AmplitudeResource(ConfigurableResource):  # type: ignore[misc]
    amplitude_api_key: str
    amplitude_secret_key: str

    _amplitude_export_client: AmplitudeExport = PrivateAttr()

    def _get_api_key(self) -> str:
        client = SecretManagerServiceClient()
        response = client.access_secret_version(request={"name": self.amplitude_api_key})
        return response.payload.data.decode()

    def _get_secret_key(self) -> str:
        client = SecretManagerServiceClient()
        response = client.access_secret_version(request={"name": self.amplitude_secret_key})
        return response.payload.data.decode()

    def setup_for_execution(self, context: InitResourceContext) -> None:
        self._amplitude_export_client = AmplitudeExport(
            api_key=self._get_api_key(), secret_key=self._get_secret_key()
        )

    def fetch_events(self, event_date: str) -> list[AmplitudeEvent]:
        # uses the run `ds` to fetch notifications for that day. recall that Dagster runs T at T+1
        start, end = AmplitudeExport.get_intraday_range(event_date)
        event_list = self._amplitude_export_client.export(start, end)

        return [AmplitudeEvent(event) for event in event_list]
