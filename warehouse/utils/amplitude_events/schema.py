amplitude_schema = {
    "server_received_time": "TIMESTAMP",
    "app": "INTEGER",
    "device_carrier": "STRING",
    "city": "STRING",
    "user_id": "STRING",
    "uuid": "STRING",
    "event_time": "TIMESTAMP",
    "platform": "STRING",
    "os_version": "STRING",
    "amplitude_id": "BIGNUMERIC",
    "processed_time": "TIMESTAMP",
    "version_name": "STRING",
    "ip_address": "STRING",
    "paying": "BOOLEAN",
    "dma": "STRING",
    "group_properties": "STRING",
    "user_properties": "STRING",
    "client_upload_time": "TIMESTAMP",
    "insert_id": "STRING",
    "event_type": "STRING",
    "library": "STRING",
    "amplitude_attribution_ids": "STRING",
    "device_type": "STRING",
    "start_version": "STRING",
    "location_lng": "FLOAT",
    "server_upload_time": "TIMESTAMP",
    "event_id": "INTEGER",
    "location_lat": "FLOAT",
    "os_name": "STRING",
    "groups": "STRING",
    "event_properties": "STRING",
    "data": "STRING",
    "device_id": "STRING",
    "language": "STRING",
    "country": "STRING",
    "region": "STRING",
    "session_id": "BIGNUMERIC",
    "device_family": "STRING",
    "sample_rate": "BIGNUMERIC",
    "client_event_time": "TIMESTAMP",
}
