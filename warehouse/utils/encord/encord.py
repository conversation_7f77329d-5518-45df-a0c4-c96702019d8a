from dataclasses import dataclass
from typing import Iterable, Mapping

from dagster import ConfigurableResource, InitResourceContext
from encord import EncordUserClient
from encord.objects import LabelRowV2, Option
from google.cloud.secretmanager import SecretManagerServiceClient
from pydantic import PrivateAttr

BUNDLE_SIZE = 100


@dataclass
class EncapsulatedLabel:
    label_row: LabelRowV2
    classifications: Mapping[str, str | list[str]]
    project_hash: str
    project_name: str


class EncordResource(ConfigurableResource):  # type: ignore[misc]
    encord_api_key_name: str
    encord_project_hashes: list[str]

    _encord_user_client: EncordUserClient = PrivateAttr()

    def _get_api_token(self) -> str:
        client = SecretManagerServiceClient()
        response = client.access_secret_version(request={"name": self.encord_api_key_name})
        return response.payload.data.decode()

    def setup_for_execution(self, context: InitResourceContext) -> None:
        encord_api_token = self._get_api_token()
        self._encord_user_client = EncordUserClient.create_with_ssh_private_key(
            ssh_private_key=encord_api_token,
            domain="https://api.us.encord.com",
        )

    def _get_classification_instances(self, label_row: LabelRowV2) -> Mapping[str, str | list[str]]:
        classification_instances: dict[str, str | list[str]] = {}
        for classification_instance in label_row.get_classification_instances():
            answers = classification_instance.get_answer()
            # Check if the answers are a list of answers or a single answer
            if isinstance(answers, str):
                classification_instances[classification_instance.classification_name] = answers
            elif isinstance(answers, Iterable):
                classification_instances[classification_instance.classification_name] = [
                    answer.label for answer in answers if isinstance(answer, Option)
                ]
            elif isinstance(answers, Option):
                classification_instances[classification_instance.classification_name] = (
                    answers.label
                )
        return classification_instances

    def get_labels(self) -> list[EncapsulatedLabel]:
        all_label_rows = []
        for project_hash in self.encord_project_hashes:
            project = self._encord_user_client.get_project(project_hash)
            bundle_init = project.create_bundle(bundle_size=BUNDLE_SIZE)
            label_rows = project.list_label_rows_v2(
                include_client_metadata=True,
                workflow_graph_node_title_eq="Complete",  # Only export reviewed labels
            )
            for label_row in label_rows:
                label_row.initialise_labels(bundle=bundle_init)
            bundle_init.execute()
            all_label_rows.extend(
                [
                    EncapsulatedLabel(
                        label_row,
                        self._get_classification_instances(label_row),
                        project_hash,
                        project.title,
                    )
                    for label_row in label_rows
                ]
            )
        return all_label_rows
