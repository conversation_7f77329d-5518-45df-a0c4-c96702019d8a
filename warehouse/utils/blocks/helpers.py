from datetime import datetime


def get_org_date_pairs_query_str(start_time: datetime, end_time: datetime) -> str:
    """
    Generate the query string to fetch org date pairs based on pending block times and block releases.
    This query is used to determine which org-date pairs need to be processed.

    Args:
        start_time: The start time to filter out the rows that haven't been processed.
        end_time: The end time to filter out the rows that have already been processed.

    Returns:
        The query string
    """
    # formatting the times with seconds precision for proper BigQuery TIMESTAMP comparison
    formatted_start_time = start_time.isoformat(timespec="seconds")
    formatted_end_time = end_time.isoformat(timespec="seconds")

    query_str = f"""
        SELECT distinct org_id, DATE(created_time) AS date
        FROM `bronze.public_block_schedule_file_rows`
        WHERE created_time >= TIMESTAMP('{formatted_start_time}') AND created_time < TIMESTAMP('{formatted_end_time}')
        INTERSECT DISTINCT
        SELECT distinct org_id, DATE(created_time) AS date
        FROM `bronze.public_block_release_file_rows`
        WHERE created_time >= TIMESTAMP('{formatted_start_time}') AND created_time < TIMESTAMP('{formatted_end_time}')
    """
    return query_str
