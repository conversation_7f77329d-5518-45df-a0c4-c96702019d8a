import gzip
import json

import polling2
import requests
from auth0.authentication import GetToken
from auth0.management import Auth0
from dagster import ConfigurableResource, InitResourceContext
from google.cloud.secretmanager import SecretManagerServiceClient
from pydantic import PrivateAttr


class Auth0Resource(ConfigurableResource):  # type: ignore[misc]
    _COMPLETE_JOB_STATUS = "completed"
    _FAILED_JOB_STATUS = "failed"
    _PENDING_JOB_STATUS = "pending"
    _TIMEOUT_SECONDS = 60
    _RETRY_INTERVAL_SECONDS = 10
    _NUM_ENTRIES_TO_RETRIEVE = 50
    _EXPORT_JOB_DATA_PARAMS = {
        # CSV export doesn't export objects like groups so must be JSON.
        "format": "json",
        "fields": [
            {"name": "user_id"},
            {"name": "groups"},
            {"name": "name"},
            {"name": "email"},
            {"name": "created_at", "export_as": "created_at_utc"},
            {"name": "last_login", "export_as": "last_login_utc"},
        ],
    }

    auth0_client_id_name: str
    auth0_client_secret_name: str
    auth0_domain: str
    _auth0_client: Auth0 = PrivateAttr()

    def _get_auth0_client_secret(self) -> str:
        client = SecretManagerServiceClient()
        response = client.access_secret_version(request={"name": self.auth0_client_secret_name})
        return response.payload.data.decode()

    def _get_auth0_client_id(self) -> str:
        client = SecretManagerServiceClient()
        response = client.access_secret_version(request={"name": self.auth0_client_id_name})
        return response.payload.data.decode()

    def setup_for_execution(self, context: InitResourceContext) -> None:
        get_token = GetToken(
            self.auth0_domain,
            self._get_auth0_client_id(),
            client_secret=self._get_auth0_client_secret(),
        )
        token = get_token.client_credentials(
            "https://{}/api/v2/".format(self.auth0_domain),
        )
        self._auth0_client = Auth0(self.auth0_domain, token["access_token"])

    def get_orgs(self) -> list[dict]:
        all_org_resp = self._auth0_client.organizations.all_organizations(
            take=self._NUM_ENTRIES_TO_RETRIEVE
        )
        organizations = all_org_resp["organizations"]
        while "next" in all_org_resp:
            all_org_resp = self._auth0_client.organizations.all_organizations(
                from_param=all_org_resp["next"]
            )
            organizations.extend(all_org_resp["organizations"])
        rows = []
        for org in organizations:
            rows.append({"org_name": org["display_name"], "org_id": org["id"]})
        return rows

    def get_user_roles(self, org_id: str) -> list[dict]:
        users_in_org_resp = self._auth0_client.organizations.all_organization_members(
            org_id,
            fields=["user_id", "roles"],
            take=self._NUM_ENTRIES_TO_RETRIEVE,
        )
        users = users_in_org_resp["members"]
        while "next" in users_in_org_resp:
            users_in_org_resp = self._auth0_client.organizations.all_organization_members(
                org_id,
                fields=["user_id", "roles"],
                from_param=users_in_org_resp["next"],
            )
            users.extend(users_in_org_resp["members"])
        rows = []
        for user in users:
            rows.append(
                {
                    "user_id": user["user_id"],
                    "roles": [r["name"] for r in user["roles"]],
                    "org_id": org_id,
                }
            )
        return rows

    def get_user_info(self) -> list[dict]:
        response = self._auth0_client.jobs.export_users(body=self._EXPORT_JOB_DATA_PARAMS)
        job_id = response["id"]
        try:
            polling2.poll(
                lambda: self._auth0_client.jobs.get(job_id)["status"] != self._PENDING_JOB_STATUS,
                step=self._RETRY_INTERVAL_SECONDS,
                timeout=self._TIMEOUT_SECONDS,
            )
        except polling2.TimeoutException:
            raise TimeoutError(
                "Job {} didn't finish exporting in expected time frame.".format(job_id)
            )
        job_info = self._auth0_client.jobs.get(job_id)
        if job_info["status"] == self._FAILED_JOB_STATUS:
            raise ValueError("Failed to get export job for job ID: {}.".format(job_id))
        r = requests.get(job_info["location"])
        data = gzip.decompress(r.content).decode("utf-8")
        return [json.loads(d) for d in data.rstrip().split("\n")]
