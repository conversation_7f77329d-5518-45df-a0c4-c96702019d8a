from datetime import datetime, <PERSON><PERSON><PERSON>
from datetime import timezone as tz

import pandas as pd
import pandera as pa
from apella_cloud_api import Client as ApellaCloudApiClient
from apella_cloud_api.new_api_server_schema import ApellaSchema as ApellaCloudApiSchema
from apella_cloud_api.new_input_schema import GQLAvailableTimeSlotQueryInput
from zoneinfo import ZoneInfo

pandera_input_schema = pa.DataFrameSchema(
    {
        "site_id": pa.Column(pa.String),
        "room_id": pa.Column(pa.String),
        "start_time": pa.Column(pd.DatetimeTZDtype(tz="UTC")),
        "end_time": pa.Column(pd.DatetimeTZDtype(tz="UTC")),
        "max_available_duration": pa.Column(pd.Timedelta),
        "block_time_ids": pa.Column(
            pa.Object,
            checks=pa.Check(lambda x: isinstance(x, list), element_wise=True),
        ),
    },
    coerce=True,
    strict=True,
)

pandera_output_schema = pandera_input_schema.remove_columns(["max_available_duration"]).add_columns(
    {
        "email_date": pa.Column(pa.Date),
        "surgery_date": pa.Column(pa.Date),
        "max_available_duration_minutes": pa.Column(pa.Int),
    }
)


def get_available_time_slots_df(
    apella_client: ApellaCloudApiClient,
    apella_schema: ApellaCloudApiSchema,
    site_id: str,
    timezone: str,
    num_days: int,
    min_available_duration: int,
) -> pd.DataFrame:
    today = datetime.now(tz=ZoneInfo(timezone)).date()
    available_time_slots_query = apella_schema.Query.available_time_slots.args(
        query=GQLAvailableTimeSlotQueryInput(
            min_available_duration=timedelta(minutes=min_available_duration),
            site_ids=[site_id],
            start_date=today,
            end_date=today + timedelta(days=num_days),
        ),
    ).select(
        apella_schema.AvailableTimeSlot.room_id,
        apella_schema.AvailableTimeSlot.start_time,
        apella_schema.AvailableTimeSlot.end_time,
        apella_schema.AvailableTimeSlot.max_available_duration,
        apella_schema.AvailableTimeSlot.block_time_ids,
    )
    results = apella_client.query_graphql_from_schema(available_time_slots_query)
    df = pd.DataFrame(
        [
            [
                site_id,
                time_slot.room_id,
                time_slot.start_time.astimezone(tz.utc),
                time_slot.end_time.astimezone(tz.utc),
                time_slot.max_available_duration,
                time_slot.block_time_ids,
            ]
            for time_slot in results.available_time_slots
        ],
        columns=[
            "site_id",
            "room_id",
            "start_time",
            "end_time",
            "max_available_duration",
            "block_time_ids",
        ],
    )

    # if df is empty for whatever reason, we still need columns to be the correct type
    df = pandera_input_schema.validate(df)

    # bigquery schema can't have intervals. We need to convert to something else. Changing it
    # to integer number of minutes
    df["max_available_duration_minutes"] = (
        df["max_available_duration"].dt.total_seconds() / 60
    ).astype(int)
    df["email_date"] = today
    df["surgery_date"] = df["start_time"].dt.tz_convert(timezone).dt.date
    df.drop(columns=["max_available_duration"], inplace=True)
    df = pandera_output_schema.validate(df)
    return df
