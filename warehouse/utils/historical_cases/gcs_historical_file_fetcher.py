import re
from datetime import datetime
from io import String<PERSON>
from typing import List

import pandas as pd
from google.cloud.storage.client import Client

from warehouse.configs.historical_cases_config import HistoricalCasesConfig
from warehouse.configs.warehouse_config import GCS_HISTORICAL_FILE_BUCKET


class GCSHistoricalFileFetcher(object):
    _HISTORICAL_CASES_FOLDER = "historical_cases"
    _COLUMN_NAME_REGEX = re.compile(r"^[a-zA-Z_][a-zA-Z0-9_]*$")
    _COLUMN_REPLACE_TOKENS = ((" ", "_"), ("-", "_"), ("#", "number"))

    def __init__(
        self,
        gcs_client: Client,
        config: HistoricalCasesConfig,
        load_date: datetime,
    ):
        self.gcs_client = gcs_client
        self.config = config
        self.load_date = load_date.date()
        self.gcs_filepath = "/".join(
            [
                GCSHistoricalFileFetcher._HISTORICAL_CASES_FOLDER,
                config.org_id,
                config.file_name,
            ]
        )

    def _validate_and_transform_columns(self, columns: List[str]) -> List[str]:
        transformed = []
        for column in columns:
            tcolumn = column.lower().strip()

            for (
                from_token,
                to_token,
            ) in GCSHistoricalFileFetcher._COLUMN_REPLACE_TOKENS:
                tcolumn = tcolumn.replace(from_token, to_token)

            assert (
                re.match(GCSHistoricalFileFetcher._COLUMN_NAME_REGEX, tcolumn) is not None
            ), f"Got unexpected column name. Original: {column}. Transformed: {tcolumn}"
            transformed.append(tcolumn)

        return transformed

    def _get_file_contents(self) -> str:
        bucket = self.gcs_client.bucket(GCS_HISTORICAL_FILE_BUCKET)
        blob = bucket.get_blob(self.gcs_filepath)

        assert blob is not None, f"Could not find GCS blob named {self.gcs_filepath}"

        return blob.download_as_text(encoding=self.config.file_encoding)

    def get_dataframe(self) -> pd.DataFrame:
        df = pd.read_csv(
            StringIO(self._get_file_contents()),
            dtype=str,
            sep=self.config.file_delimiter,
        )

        df.columns = self._validate_and_transform_columns(df.columns)  # type: ignore[assignment]

        df[df.columns] = df.apply(lambda s: s.str.strip())

        df["source_file_date_utc"] = str(self.config.file_date)
        df["org_id"] = self.config.org_id
        df["gcs_file_path"] = f"{GCS_HISTORICAL_FILE_BUCKET}/{self.gcs_filepath}"
        df["ds"] = self.load_date

        return df
