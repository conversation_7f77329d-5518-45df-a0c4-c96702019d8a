from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum
from functools import lru_cache
from typing import Any, List, Optional, Union

import pandas as pd
from dagster import DagsterLogManager
from nameparser import HumanName


class CaseClass(Enum):
    URGENT = "urgent"
    ELECTIVE = "elective"
    EMERGENCY = "emergency"
    ADD_ON = "add_on"


class PatientClass(Enum):
    EMERGENCY = "emergency"
    OUTPATIENT = "outpatient"
    INPATIENT = "inpatient"
    SURGERY_ADMIT = "surgery_admit"
    PRE_ADMIT = "pre_admit"
    OBSERVATION = "observation"


class BaseSchemaMerger(ABC):
    _BQ_MERGE_SCHEMA = {
        "org_id": "string",
        "file_case_id": "string",
        "room": "string",
        "site": "string",
        "scheduled_start_datetime_local": "datetime",
        "scheduled_end_datetime_local": "datetime",
        "actual_start_datetime_local": "datetime",
        "actual_end_datetime_local": "datetime",
        "case_class": "string",
        "patient_class": "string",
        "is_add_on": "bool",
        "primary_surgeon": {
            "first_name": "string",
            "middle_name": "string",
            "last_name": "string",
            "suffix": "string",
        },
        "primary_procedure_name": "string",
        "full_procedure_name": "string",
        "all_procedure_names": ["string"],
        "procedure_count": "integer",
        "source_file_date_utc": "date",
        "service_line": "string",
        "source_file_gcs_path": "string",
        "cancellation_reason": "string",
        "primary_surgeon_staff_id": "string",
        "ds": "date",
    }

    def __init__(
        self,
        table_name: str,
        file_dataframe: pd.DataFrame,
        load_date: datetime,
        logger: DagsterLogManager,
    ):
        self.logger = logger
        self.file_df = file_dataframe
        self.num_rows_prefilter = len(file_dataframe)
        self.filter_file_dataframe_premerge()
        assert not self.file_df.empty, f"Got empty pre-merge dataframe for {table_name}"
        self.load_date = load_date.date()
        self.gcs_file_path = self.file_df["gcs_file_path"].iloc[0]

    @staticmethod
    def get_schema() -> dict[str, Any]:
        return BaseSchemaMerger._BQ_MERGE_SCHEMA

    # save compute, sure, but also reduce log noise. this should reserve max ~10mb
    @lru_cache(maxsize=pow(2, 16))
    def _primary_surgeon_struct(
        self, ps_str: Union[None, str]
    ) -> Optional[dict[str, Optional[str]]]:
        if ps_str is None:
            return None

        surgeons = ps_str.lower().split("\n")
        ps_str = surgeons[0].lower()
        if len(surgeons) > 1:
            self.logger.warn(f"Got more than one surgeon: {surgeons}")
            self.logger.warn(f"Selecting {ps_str} as the primary surgeon")

        try:
            human_name = HumanName(ps_str)
        except Exception as e:
            self.logger.warn(f"Caught an exception: {e}", exc_info=True)
            self.logger.warn(f'Could not parse surgeon name. Got "{ps_str}"')
            return None

        if not human_name.last:
            self.logger.warn(f'Cannot extract last name for surgeon "{ps_str}"')
            return None

        # convert empty strings (did not find name component) to nulls
        return {
            k: v if v else None
            for k, v in {
                "first_name": human_name.first,
                "middle_name": human_name.middle,
                "last_name": human_name.last,
                "suffix": human_name.suffix,
            }.items()
        }

    def _combine_columns(self, columns: List[str], func, extra_args=[]) -> pd.Series:
        return self.file_df[columns].apply(
            lambda row: func(*([getattr(row, column) for column in columns] + extra_args)),
            axis=1,
        )

    # save compute, sure, but also reduce log noise. this should reserve max ~1mb
    @lru_cache(maxsize=pow(2, 16))
    def _parse_datetime_str(
        self, date_str: str, date_format: str, time_format: str
    ) -> Optional[datetime]:
        format = f"{date_format} {time_format}"
        try:
            return datetime.strptime(date_str, format)
        # unexpected datetime format
        except ValueError:
            self.logger.warn(f"Could not parse date using format: {format}. Got {date_str}")
            return None
        # NaN -> empty cell in the CSV
        except TypeError:
            return None

    def _get_clean_procedure_name(self, procedure_name: str) -> str:
        tokens = procedure_name.lower().strip().split()
        return " ".join([t for t in tokens if t not in ("left", "right")])

    def _log_statistics(self, df: pd.DataFrame, column: str) -> None:
        pct_null = (df[column].isnull().sum() / len(df[column])) * 100
        self.logger.info(f"Got {pct_null:0.2f}% null values for column {column}")

    def get_unified_dataframe(self) -> pd.DataFrame:
        df = pd.DataFrame()
        for column in BaseSchemaMerger.get_schema().keys():
            df[column] = getattr(self, f"get_{column}")()
            self._log_statistics(df, column)

        df_ret = df[
            df.scheduled_start_datetime_local.notnull()
            & df.scheduled_end_datetime_local.notnull()
            & (df.scheduled_end_datetime_local > df.scheduled_start_datetime_local)
            & df.actual_start_datetime_local.notnull()
            & df.actual_end_datetime_local.notnull()
            & (df.actual_end_datetime_local > df.actual_start_datetime_local)
        ]

        self.logger.info(
            f"Filtered {(100 * (1 - len(df_ret) / self.num_rows_prefilter)):0.2f}% of rows due to erroneous data"
        )

        return df_ret

    @abstractmethod
    def filter_file_dataframe_premerge(self):
        raise NotImplementedError()

    @abstractmethod
    def get_file_case_id(self) -> pd.Series:
        raise NotImplementedError()

    @abstractmethod
    def get_room(self) -> pd.Series:
        raise NotImplementedError()

    @abstractmethod
    def get_scheduled_start_datetime_local(self) -> pd.Series:
        raise NotImplementedError()

    @abstractmethod
    def get_scheduled_end_datetime_local(self) -> pd.Series:
        raise NotImplementedError()

    @abstractmethod
    def get_actual_start_datetime_local(self) -> pd.Series:
        raise NotImplementedError()

    @abstractmethod
    def get_actual_end_datetime_local(self) -> pd.Series:
        raise NotImplementedError()

    @abstractmethod
    def get_case_class(self) -> Optional[pd.Series]:
        raise NotImplementedError()

    @abstractmethod
    def get_patient_class(self) -> pd.Series:
        raise NotImplementedError()

    @abstractmethod
    def get_is_add_on(self) -> Optional[pd.Series]:
        raise NotImplementedError()

    @abstractmethod
    def get_primary_surgeon(self) -> pd.Series:
        raise NotImplementedError()

    @abstractmethod
    def get_primary_procedure_name(self) -> pd.Series:
        raise NotImplementedError()

    @abstractmethod
    def get_all_procedure_names(self) -> pd.Series:
        raise NotImplementedError()

    @abstractmethod
    def get_procedure_count(self) -> Optional[pd.Series]:
        raise NotImplementedError()

    @abstractmethod
    def get_service_line(self) -> Optional[pd.Series]:
        raise NotImplementedError()

    @abstractmethod
    def get_full_procedure_name(self) -> pd.Series:
        raise NotImplementedError()

    @abstractmethod
    def get_site(self) -> pd.Series:
        raise NotImplementedError()

    @abstractmethod
    def get_cancellation_reason(self) -> pd.Series:
        raise NotImplementedError()

    @abstractmethod
    def get_primary_surgeon_staff_id(self) -> pd.Series:
        raise NotImplementedError()

    def get_source_file_date_utc(self) -> pd.Series:
        return self.file_df["source_file_date_utc"].apply(
            lambda date_str: datetime.strptime(date_str, "%Y-%m-%d")
        )

    def get_org_id(self) -> pd.Series:
        return self.file_df["org_id"]

    def get_ds(self) -> pd.Series:
        return pd.Series([self.load_date] * self.file_df.size)

    def get_source_file_gcs_path(self) -> pd.Series:
        return self.file_df["gcs_file_path"]
