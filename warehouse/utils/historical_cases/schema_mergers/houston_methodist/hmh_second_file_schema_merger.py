import re
from datetime import datetime, timedelta
from typing import Optional, Union

import pandas as pd

from warehouse.utils.historical_cases.schema_mergers.base_schema_merger import (
    CaseClass,
)
from warehouse.utils.historical_cases.schema_mergers.houston_methodist.hmh_base_schema_merger import (
    HMHBaseSchemaMerger,
)


class HMHSecondFileSchemaMerger(HMHBaseSchemaMerger):
    _TIME_FORMAT_24H = "%H:%M"
    _DATE_FORMAT = "%m/%d/%y"
    _TIME_FORMAT_STD = "%I:%M:%S %p"

    def filter_file_dataframe_premerge(self) -> pd.DataFrame:
        def duration_filter(n):
            return n is None or int(n) > 0

        self.file_df = self.file_df[
            self.file_df.sch_in_rm_to_sch_out_of_rm.apply(duration_filter)
            & self.file_df.room_duration.apply(duration_filter)
        ]
        super().filter_file_dataframe_premerge()

    def _create_datetime_from_parts(
        self, date_str: str, time_str: str, time_format: str
    ) -> Union[None, datetime]:
        return self._parse_datetime_str(f"{date_str} {time_str}", self._DATE_FORMAT, time_format)

    def _get_case_id_column(self) -> str:
        if "log_number" in self.file_df.columns:
            return "log_number"
        elif "case_number" in self.file_df.columns:
            return "case_number"
        else:
            raise Exception(
                "This schema merger requires `[log|case]_number` as a column corresponding to the case ID"
            )

    def _case_class(self, cc: str) -> Optional[str]:
        if cc == "elective":
            return CaseClass.ELECTIVE.value
        elif cc == "emergent":
            return CaseClass.EMERGENCY.value
        elif cc == "urgent":
            return CaseClass.URGENT.value
        elif cc in (
            "partially cosmetic",
            "unplanned returned to surgery - same day",
            "planned return to surgery - same day",
        ):
            return None
        else:
            raise ValueError(f"Unexpected case class {cc}")

    def _compute_end_time(
        self,
        date_str: str,
        time_str: str,
        duration_minutes_str: str,
        time_format: str,
    ) -> Union[datetime, None]:
        start_dt = self._create_datetime_from_parts(date_str, time_str, time_format)
        if start_dt is None or duration_minutes_str is None:
            return None
        return start_dt + timedelta(minutes=int(duration_minutes_str))

    def _get_first_procedure(self, procedures: str) -> str:
        return procedures.splitlines()[0].lower()

    def _remove_bracketed_id(self, procedure: str):
        return re.sub(r" \[\d+\]", "", procedure)

    def get_cancellation_reason(self) -> pd.Series:
        return self.file_df.reason.apply(lambda s: s.strip().lower() if s is not None else None)

    def get_file_case_id(self) -> pd.Series:
        return self.file_df[self._get_case_id_column()].str.lower()

    def get_room(self) -> pd.Series:
        return self.file_df.room

    def get_scheduled_start_datetime_local(self) -> pd.Series:
        return self._combine_columns(
            ["date", "time"],
            self._create_datetime_from_parts,
            extra_args=[self._TIME_FORMAT_STD],
        )

    def get_scheduled_end_datetime_local(self) -> pd.Series:
        return self._combine_columns(
            ["date", "time", "sch_in_rm_to_sch_out_of_rm"],
            self._compute_end_time,
            extra_args=[self._TIME_FORMAT_STD],
        )

    def get_actual_start_datetime_local(self) -> pd.Series:
        return self._combine_columns(
            ["date", "in_room"],
            self._create_datetime_from_parts,
            extra_args=[self._TIME_FORMAT_24H],
        )

    def get_actual_end_datetime_local(self) -> pd.Series:
        return self._combine_columns(
            ["date", "in_room", "room_duration"],
            self._compute_end_time,
            extra_args=[self._TIME_FORMAT_24H],
        )

    def get_case_class(self) -> Optional[pd.Series]:
        return self.file_df.case_classification.str.lower().apply(
            lambda cc: self._case_class(cc) if cc is not None else None
        )

    def get_primary_surgeon(self) -> pd.Series:
        res = self.file_df[["surgeon"]].apply(
            lambda row: self._primary_surgeon_struct(row.surgeon),
            axis=1,
        )
        return res

    def get_procedure_count(self) -> Optional[pd.Series]:
        return self.file_df.procedures.apply(
            lambda np: len(np.splitlines()) if np is not None else None
        )

    def get_service_line(self) -> Optional[pd.Series]:
        return self.file_df.service

    def get_site(self) -> pd.Series:
        return self.file_df.location

    def get_primary_procedure_name(self) -> pd.Series:
        return self.file_df.procedures.apply(
            lambda pc: self._get_clean_procedure_name(
                self._remove_bracketed_id(self._get_first_procedure(pc))
            )
            if pc is not None
            else None
        )

    def get_full_procedure_name(self) -> pd.Series:
        return self.file_df.procedures.apply(
            lambda pc: self._get_first_procedure(pc) if pc is not None else None
        )

    def get_all_procedure_names(self) -> pd.Series:
        return self.file_df.procedures.apply(
            (
                lambda ap: [
                    self._get_clean_procedure_name(self._remove_bracketed_id(p.lower()))
                    for p in ap.split("\n")
                ]
                if ap is not None
                else None
            )
        )
