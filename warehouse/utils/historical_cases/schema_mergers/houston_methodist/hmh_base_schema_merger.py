from typing import Optional

import pandas as pd

from warehouse.utils.historical_cases.schema_mergers.base_schema_merger import (
    BaseSchemaMerger,
    PatientClass,
)


class HMHBaseSchemaMerger(BaseSchemaMerger):
    def _get_patient_class(self, pc: str) -> str:
        if pc == "inpatient":
            return PatientClass.INPATIENT.value
        elif pc == "hospital outpatient surgery":
            return PatientClass.OUTPATIENT.value
        elif pc == "surgery admit":
            return PatientClass.SURGERY_ADMIT.value
        elif pc == "emergency":
            return PatientClass.EMERGENCY.value
        else:
            raise ValueError(f"Unexpected patient class {pc}")

    def filter_file_dataframe_premerge(self) -> pd.DataFrame:
        prefilter_df = self.file_df[
            (self.get_log_status() == "posted")
            & self.get_room().notnull()
            & self.get_scheduled_start_datetime_local().notnull()
        ]

        pct_filtered = 100 * (1 - len(prefilter_df) / len(self.file_df))

        self.logger.info(f"Pre-filtered {pct_filtered:0.2f}% of initial rows")

        self.file_df = prefilter_df

    def get_log_status(self) -> pd.Series:
        return self.file_df.log_status.str.lower()

    def get_patient_class(self) -> pd.Series:
        return self.file_df.patient_class.apply(
            lambda pc: self._get_patient_class(pc.lower()) if pc is not None else None
        )

    def get_is_add_on(self) -> Optional[pd.Series]:
        return None

    def get_primary_surgeon_staff_id(self) -> pd.Series:
        return None
