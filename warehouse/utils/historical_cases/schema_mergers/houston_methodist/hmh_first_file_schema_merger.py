from typing import Optional

import pandas as pd

from warehouse.utils.historical_cases.schema_mergers.houston_methodist.hmh_base_schema_merger import (
    HMHBaseSchemaMerger,
)


class HMHFirstFileSchemaMerger(HMHBaseSchemaMerger):
    _DATE_FORMAT = "%m/%d/%Y"
    _TIME_FORMAT = "%I:%M:%S %p"

    def _get_datetime_column(self, time_column: str) -> pd.Series:
        return getattr(self.file_df, time_column).apply(
            lambda t_str: self._parse_datetime_str(t_str, self._DATE_FORMAT, self._TIME_FORMAT)
        )

    def filter_file_dataframe_premerge(self) -> pd.DataFrame:
        super().filter_file_dataframe_premerge()
        self.file_df = self.file_df[self.file_df.log_service_line.str.lower() != "oncology"]

    def get_cancellation_reason(self) -> pd.Series:
        return self.file_df.procedure_not_performed_reason.apply(
            lambda s: s.strip().lower() if s is not None else None
        )

    def get_file_case_id(self) -> pd.Series:
        return self.file_df["log_id"].str.lower()

    def get_room(self) -> pd.Series:
        return self.file_df.room

    def get_scheduled_start_datetime_local(self) -> pd.Series:
        return self._get_datetime_column("scheduled_in_room")

    def get_scheduled_end_datetime_local(self) -> pd.Series:
        return self._get_datetime_column("scheduled_out_of_room")

    def get_actual_start_datetime_local(self) -> pd.Series:
        return self._get_datetime_column("patient_in_room")

    def get_actual_end_datetime_local(self) -> pd.Series:
        return self._get_datetime_column("patient_out_of_room")

    def get_case_class(self) -> Optional[pd.Series]:
        return None

    def get_primary_surgeon(self) -> pd.Series:
        return self.file_df[["primary_surgeon_name"]].apply(
            lambda row: self._primary_surgeon_struct(row.primary_surgeon_name),
            axis=1,
        )

    def get_primary_procedure_name(self) -> pd.Series:
        return self.file_df["primary_procedure_name"].apply(self._get_clean_procedure_name)

    def get_full_procedure_name(self) -> pd.Series:
        return self.file_df["primary_procedure_name"].str.lower()

    # Only received free text that cannot be normalized.
    def get_all_procedure_names(self) -> pd.Series:
        return None

    def get_procedure_count(self) -> Optional[pd.Series]:
        return self.file_df["number_of_procedures"].apply(int)

    def get_service_line(self) -> pd.Series:
        return self.file_df["hmh_primary_physician_service_line"].str.lower()

    def get_site(self) -> pd.Series:
        return self.file_df.location
