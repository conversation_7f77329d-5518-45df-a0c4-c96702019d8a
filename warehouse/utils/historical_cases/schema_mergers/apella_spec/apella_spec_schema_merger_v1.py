import re
from abc import abstractmethod
from typing import Optional, Union

import pandas as pd

from warehouse.utils.historical_cases.schema_mergers.base_schema_merger import (
    BaseSchemaMerger,
    CaseClass,
    PatientClass,
)


class ApellaSpecSchemaMergerV1(BaseSchemaMerger):
    """
    The spec is defined here:
        https://apella.notion.site/Historic-Data-Requirements-92c3b46880cd4305993eda496281f0c1
    """

    def __init__(self, *args, **kwargs):
        self._patient_class_lu = self._flip_mapping(self._patient_class_mapping)
        self._case_class_lu = self._flip_mapping(self._case_class_mapping)

        super().__init__(*args, **kwargs)

    def _flip_mapping(
        self, mapping: dict[Union[PatientClass, CaseClass], set[str]]
    ) -> dict[str, Union[PatientClass, CaseClass]]:
        return {
            data_class: apella_class
            for apella_class, data_classes in mapping.items()
            for data_class in data_classes
        }

    @property
    @abstractmethod
    def _case_class_mapping(self) -> dict[CaseClass, set[str]]:
        raise NotImplementedError()

    @property
    @abstractmethod
    def _patient_class_mapping(self) -> dict[PatientClass, set[str]]:
        raise NotImplementedError()

    @property
    @abstractmethod
    def _get_date_and_time_format(self) -> tuple[str, str]:
        raise NotImplementedError()

    def _get_normalized_lu(
        self,
        value: Union[str, None],
        mapping: dict[str, Union[PatientClass, CaseClass]],
    ) -> Union[str, None]:
        if value is None:
            return None

        value = value.lower().strip()

        assert (
            value in mapping
        ), f"'{value}' is not mapped for class {list(mapping.values())[0].__class__}"

        return mapping[value].value

    def _parse_case_class(self, case_class: Union[str, None]) -> Union[str, None]:
        return self._get_normalized_lu(case_class, self._case_class_lu)

    def _parse_patient_class(self, patient_class: Union[str, None]) -> Union[str, None]:
        return self._get_normalized_lu(patient_class, self._patient_class_lu)

    def _parse_is_add_on(self, is_add_on: Union[str, None]) -> Union[bool, None]:
        if is_add_on is None:
            return None
        return is_add_on.lower().strip() == "y"

    def _get_datetime_column(self, time_column: str) -> pd.Series:
        return getattr(self.file_df, time_column).apply(
            lambda t_str: self._parse_datetime_str(
                t_str, self._get_date_and_time_format[0], self._get_date_and_time_format[1]
            )
        )

    def _remove_bracketed_id(self, procedure: str):
        return re.sub(r"\[\d+\]", "", procedure)

    def _replace_escaped_chars(self, procedure: str):
        return procedure.replace("&gt;", ">").replace("&amp;", "&").replace("&lt;", "<")

    def filter_file_dataframe_premerge(self):
        return

    def get_file_case_id(self) -> pd.Series:
        return self.file_df.case_identifier

    def get_room(self) -> pd.Series:
        return self.file_df.room_identifier

    def get_scheduled_start_datetime_local(self) -> pd.Series:
        return self._get_datetime_column("scheduled_start_time")

    def get_scheduled_end_datetime_local(self) -> pd.Series:
        return self._get_datetime_column("scheduled_end_time")

    def get_actual_start_datetime_local(self) -> pd.Series:
        return self._get_datetime_column("case_start_time")

    def get_actual_end_datetime_local(self) -> pd.Series:
        return self._get_datetime_column("case_end_time")

    def get_case_class(self) -> Optional[pd.Series]:
        return self.file_df.case_classification.apply(self._parse_case_class)

    def get_patient_class(self) -> pd.Series:
        return self.file_df.patient_classification.apply(self._parse_patient_class)

    def get_is_add_on(self) -> Optional[pd.Series]:
        return self.file_df.add_on_indicator.apply(self._parse_is_add_on)

    def get_primary_procedure_name(self) -> pd.Series:
        # applying the cleanup function to keep with the convention re. other files
        return self.file_df.planned_primary_procedure.apply(
            lambda pc: self._get_clean_procedure_name(pc.lower()) if pc is not None else None
        )

    def get_all_procedure_names(self) -> pd.Series:
        return self.file_df.all_planned_procedures.apply(
            (
                lambda ap: [
                    self._get_clean_procedure_name(
                        self._replace_escaped_chars(self._remove_bracketed_id(p.lower()))
                    )
                    for p in ap.split("^")
                ]
                if ap is not None
                else None
            )
        )

    def get_procedure_count(self) -> Optional[pd.Series]:
        return self.file_df.count_of_total_procedures.apply(
            lambda x: int(x) if x is not None else None
        )

    def get_service_line(self) -> Optional[pd.Series]:
        return self.file_df.service_line

    def get_full_procedure_name(self) -> pd.Series:
        # lowercasing to keep with the convention re. other files
        return self.file_df.planned_primary_procedure.str.lower()

    def get_site(self) -> pd.Series:
        return self.file_df.or_location

    def get_primary_surgeon(self) -> pd.Series:
        return self.file_df.primary_procedure_physician.apply(self._primary_surgeon_struct)

    def get_cancellation_reason(self) -> pd.Series:
        return self.file_df.cancel_reason.str.lower()

    def get_primary_surgeon_staff_id(self) -> pd.Series:
        return None
