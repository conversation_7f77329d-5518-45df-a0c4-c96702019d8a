import json

import pandas as pd

from warehouse.utils.historical_cases.schema_mergers.apella_spec.apella_spec_schema_merger_v1 import (
    ApellaSpecSchemaMergerV1,
)
from warehouse.utils.historical_cases.schema_mergers.base_schema_merger import (
    CaseClass,
    PatientClass,
)


class TGHSchemaMerger(ApellaSpecSchemaMergerV1):
    _DATE_FORMAT = "%Y-%m-%d"
    _TIME_FORMAT = "%H:%M:%S"

    # the staff_role attribute provided in the surgical_staff column
    _STAFF_ROLES = {"primary": "Primary Physician"}

    @property
    def _patient_class_mapping(self) -> dict[PatientClass, set[str]]:
        return {
            PatientClass.OUTPATIENT: {
                "outpatient",
                "hospital outpatient surgery",
                "specimen",
                "dialysis series",
                "radiation/oncology series",
                "therapies series",
                "infusion series",
                "transplant",
                "outpatient in a bed",
                "nuclear med series",
                "blood administration series",
                "neurodiagnostics series",
                "aeromed",
                "ltvm series",
                "education series",
                "eye clinic series",
            },
            PatientClass.EMERGENCY: {"emergency"},
            PatientClass.INPATIENT: {
                "inpatient",
                "newborn",
                "inpatient psych",
                "inpatient rehab",
                "deceased - organ donor",
            },
            PatientClass.SURGERY_ADMIT: {
                "surgery admit",
            },
            PatientClass.OBSERVATION: {
                "observation",
            },
        }

    @property
    def _case_class_mapping(self) -> dict[CaseClass, set[str]]:
        return {
            CaseClass.URGENT: {"expedited", "urgent"},
            CaseClass.EMERGENCY: {"emergent", "trauma"},
            CaseClass.ELECTIVE: {"elective"},
        }

    @property
    def _get_date_and_time_format(self) -> tuple[str, str]:
        return (self._DATE_FORMAT, self._TIME_FORMAT)

    def _get_primary_surgeon_staff_id(
        self, surgical_staff, primary_procedure_physician
    ) -> pd.Series:
        surgical_staff_json = json.loads(surgical_staff)
        for staff_info in surgical_staff_json:
            if (
                # staff_name may contain a trailing title (i.e. MD) which is not included in the primary_procedure_physician field
                primary_procedure_physician in staff_info["staff_name"]
                and staff_info["staff_role"] == self._STAFF_ROLES["primary"]
            ):
                return staff_info["staff_id"]
        return None

    def get_primary_surgeon_staff_id(self) -> pd.Series:
        return self.file_df[["surgical_staff", "primary_procedure_physician"]].apply(
            lambda row: self._get_primary_surgeon_staff_id(
                row.surgical_staff, row.primary_procedure_physician
            ),
            axis=1,
        )
