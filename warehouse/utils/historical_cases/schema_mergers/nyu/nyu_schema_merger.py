from datetime import datetime

import pandas as pd
from dagster import DagsterLogManager

from warehouse.utils.historical_cases.schema_mergers.apella_spec.apella_spec_schema_merger_v1 import (
    ApellaSpecSchemaMergerV1,
)
from warehouse.utils.historical_cases.schema_mergers.base_schema_merger import (
    CaseClass,
    PatientClass,
)


class NYUSchemaMerger(ApellaSpecSchemaMergerV1):
    _DATE_FORMAT = "%m/%d/%Y"
    _TIME_FORMAT = "%I:%M %p"

    __COL_NAME_TO_APELLA_SPEC_COL_NAME_MAPPING = {
        "add_on_case_yn": "add_on_indicator",
        "case_begin_instant": "scheduled_start_time",
        "case_class": "case_classification",
        "case_end_instant": "scheduled_end_time",
        "wheels_in": "case_start_time",
        "wheels_out": "case_end_time",
        "loc_name": "or_location",
        "log_name": "case_identifier",
        "num_of_procs": "count_of_total_procedures",
        "op_room": "room_identifier",
        "patient_class": "patient_classification",
        "or_proc": "planned_primary_procedure",
        "primary_surg_as_scheduled": "primary_procedure_physician",
        "surg_service": "service_line",
        "all_procs": "all_planned_procedures",
    }

    def __init__(
        self,
        table_name: str,
        file_dataframe: pd.DataFrame,
        load_date: datetime,
        logger: DagsterLogManager,
    ):
        file_dataframe = file_dataframe.rename(
            columns=self.__COL_NAME_TO_APELLA_SPEC_COL_NAME_MAPPING
        )
        super().__init__(table_name, file_dataframe, load_date, logger)

    def get_cancellation_reason(self) -> pd.Series:
        return None

    @property
    def _patient_class_mapping(self) -> dict[PatientClass, set[str]]:
        return {
            PatientClass.OUTPATIENT: {"outpatient surgery"},
            PatientClass.EMERGENCY: {"emergency"},
            PatientClass.INPATIENT: {
                "inpatient",
                "deceased - organ donor",
            },
            PatientClass.SURGERY_ADMIT: {"surgery admit"},
        }

    @property
    def _case_class_mapping(self) -> dict[CaseClass, set[str]]:
        return {
            CaseClass.URGENT: {"b - urgent <6 hours", "urgent"},
            CaseClass.ADD_ON: {"c - add-on <24 hours"},
            CaseClass.EMERGENCY: {
                "a - emergent/trauma <1 hour",
                "trauma",
                "emergent",
            },
            CaseClass.ELECTIVE: {"elective", "scheduled"},
        }

    @property
    def _get_date_and_time_format(self) -> tuple[str, str]:
        return (self._DATE_FORMAT, self._TIME_FORMAT)
