import re
from datetime import datetime, timedelta
from functools import lru_cache
from typing import Optional

import pandas as pd

from warehouse.utils.historical_cases.schema_mergers.base_schema_merger import (  # noqa: E501
    BaseSchemaMerger,
    CaseClass,
    PatientClass,
)


class HealthFirstSchemaMerger(BaseSchemaMerger):
    _DATA_START = datetime(2022, 5, 9)
    _DATE_FORMAT = "%m/%d/%Y"
    _TIME_FORMAT = "%I:%M:%S %p"
    _SITE = "VH02"
    _CANCELLATION_REASON = "cancelled"

    def _get_datetime_column(self, date_column: str, time_column: str) -> pd.Series:
        return (
            getattr(self.file_df, date_column)
            .str.cat(getattr(self.file_df, time_column), sep=" ")
            .apply(
                lambda t_str: self._parse_datetime_str(t_str, self._DATE_FORMAT, self._TIME_FORMAT)
            )
        )

    def _case_class(self, cc: str) -> Optional[str]:
        if cc == "elective":
            return CaseClass.ELECTIVE.value
        elif cc == "emergency":
            return CaseClass.EMERGENCY.value
        elif cc == "urgent":
            return CaseClass.URGENT.value
        elif cc in (
            "add on",
            "call back",
            "trauma",
            "unknown case schedule type",
        ):
            return None
        else:
            raise ValueError(f"Unexpected case class {cc}")

    def _patient_class(self, pc: str) -> Optional[str]:
        if pc == "outpatient":
            return PatientClass.OUTPATIENT.value
        elif pc == "inpatient":
            return PatientClass.INPATIENT.value
        elif pc == "emergency":
            return PatientClass.EMERGENCY.value
        else:
            raise ValueError(f"Unexpected patient class {pc}")

    def _check_pre_merge_str_contains(self, s: str, token: str) -> bool:
        tokens = re.split(r",|\s", s.lower())
        return token in tokens

    def filter_file_dataframe_premerge(self):
        self.file_df = self.file_df[
            (self.file_df.case_patient_status.str.lower() != "preadmit")
            & ~self.file_df.surgeon.apply(
                lambda s: self._check_pre_merge_str_contains(s, "physician")
            )
            & self.file_df.case_date.apply(
                lambda cd: datetime.strptime(cd, self._DATE_FORMAT)
                >= HealthFirstSchemaMerger._DATA_START
            )
            & self.file_df.location_name.notnull()
        ]

    def _shift_end_datetime(self, start_series: pd.Series, end_series: pd.Series) -> pd.DataFrame:
        """
        This is an assumption / hack to handle cases that roll over into the next day.
        We do not have any indication in the file that this is happening, except that
        the end time is less than the start time.
        """
        df = pd.concat([start_series, end_series], axis=1, keys=["start", "end"])
        return df.apply(
            lambda row: row.end + timedelta(days=1) if row.end < row.start else row.end,
            axis=1,
        )

    def get_file_case_id(self) -> pd.Series:
        return self.file_df["case_id"].str.lower()

    def get_room(self) -> pd.Series:
        return self.file_df["location_name"]

    @lru_cache()
    def get_scheduled_start_datetime_local(self) -> pd.Series:
        return self._get_datetime_column(
            "case_scheduled_start_date",
            "case_scheduled_start_time",
        )

    def get_scheduled_end_datetime_local(self) -> pd.Series:
        return self._shift_end_datetime(
            self.get_scheduled_start_datetime_local(),
            self._get_datetime_column(
                "case_scheduled_start_date",
                "case_scheduled_end_time",
            ),
        )

    @lru_cache()
    def get_actual_start_datetime_local(self) -> pd.Series:
        return self._get_datetime_column(
            "case_date",
            "case_actual_start_time",
        )

    def get_actual_end_datetime_local(self) -> pd.Series:
        return self._shift_end_datetime(
            self.get_actual_start_datetime_local(),
            self._get_datetime_column(
                "case_date",
                "case_actual_end_time",
            ),
        )

    def get_case_class(self) -> pd.Series:
        return self.file_df.case_schedule_type.apply(
            lambda cc: self._case_class(cc.lower())  # type: ignore
        )

    def get_patient_class(self) -> pd.Series:
        return self.file_df.case_patient_status.apply(
            lambda pc: self._patient_class(pc.lower())  # type: ignore
        )

    def get_is_add_on(self) -> Optional[pd.Series]:
        return self.file_df["case_schedule_type"] == "Add On"

    def get_primary_surgeon(self) -> pd.Series:
        return self.file_df[["surgeon"]].apply(
            lambda row: self._primary_surgeon_struct(row.surgeon),
            axis=1,
        )

    def get_primary_procedure_name(self) -> pd.Series:
        return self.file_df["procedure"].apply(
            lambda pp_str: self._get_clean_procedure_name(pp_str.split("|")[-1].lower().strip())
        )

    def get_full_procedure_name(self) -> pd.Series:
        return self.file_df["procedure"].str.lower()

    def get_all_procedure_names(self) -> pd.Series:
        return None

    def get_procedure_count(self) -> Optional[pd.Series]:
        return None

    def get_service_line(self) -> pd.Series:
        return self.file_df["case_specialty"].str.lower()

    def get_site(self) -> pd.Series:
        return self._SITE

    def get_cancellation_reason(self) -> pd.Series:
        return self.file_df.procedure.apply(
            lambda s: self._CANCELLATION_REASON
            if self._check_pre_merge_str_contains(s, "cancelled")
            else None
        )

    def get_primary_surgeon_staff_id(self) -> pd.Series:
        return None
