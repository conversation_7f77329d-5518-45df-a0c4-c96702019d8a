import json
from datetime import datetime
from typing import Optional

import pandas as pd
from dagster import DagsterLogManager

from warehouse.utils.historical_cases.schema_mergers.apella_spec.apella_spec_schema_merger_v1 import (
    ApellaSpecSchemaMergerV1,
)
from warehouse.utils.historical_cases.schema_mergers.base_schema_merger import (
    CaseClass,
    PatientClass,
)


class LifebridgeBaseSchemaMerger(ApellaSpecSchemaMergerV1):
    _DATE_FORMAT = "%m/%d/%Y"
    _TIME_FORMAT = "%H:%M"

    _STAFF_ROLES = {"primary": "Primary Surgeon"}

    __COL_NAME_TO_APELLA_SPEC_COL_NAME_MAPPING = {
        "site": "or_location",
        "room_identifier_actual": "room_identifier",
        "primary_procedure": "planned_primary_procedure",
        "all_procedures": "all_planned_procedures",
    }

    def _normalize_procedure_names(self, file_dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        Normalize procedure names by replacing all occurrences of '&' with 'AND'
        in the 'planned_primary_procedure' and 'all_planned_procedures' columns.
        Null values (None or NaN) are preserved.

        See: https://linear.app/apella/issue/FORC-83/spike-on-using-obx-observations-to-train-forecasting-models
        """
        df_to_modify = file_dataframe.copy()

        # Return non-string values (like None, NaN) as is
        def replace_ampersands_in_string(text_value):
            if isinstance(text_value, str):
                # First handle '&amp;' to prevent it from becoming 'ANDamp;'
                # then handle standalone '&'
                return text_value.replace("&amp;", "AND").replace("&", "AND")
            return text_value

        df_to_modify["planned_primary_procedure"] = df_to_modify["planned_primary_procedure"].apply(
            replace_ampersands_in_string
        )
        df_to_modify["all_planned_procedures"] = df_to_modify["all_planned_procedures"].apply(
            replace_ampersands_in_string
        )

        return df_to_modify

    def __init__(
        self,
        table_name: str,
        file_dataframe: pd.DataFrame,
        load_date: datetime,
        logger: DagsterLogManager,
    ):
        file_dataframe = file_dataframe.rename(
            columns=self.__COL_NAME_TO_APELLA_SPEC_COL_NAME_MAPPING
        )

        file_dataframe = self._normalize_procedure_names(file_dataframe)

        super().__init__(table_name, file_dataframe, load_date, logger)

    @property
    def _patient_class_mapping(self) -> dict[PatientClass, set[str]]:
        return {
            PatientClass.PRE_ADMIT: {"preadmit"},
            PatientClass.OUTPATIENT: {"outpatient"},
            PatientClass.EMERGENCY: {"emergency"},
            PatientClass.INPATIENT: {
                "inpatient",
                "recurring",
            },
            PatientClass.OBSERVATION: {"observation"},
        }

    @property
    def _case_class_mapping(self) -> dict[CaseClass, set[str]]:
        return {
            CaseClass.URGENT: {"urgent"},
            CaseClass.EMERGENCY: {
                "emergency",
                "trauma",
            },
            CaseClass.ELECTIVE: {
                "elective",
            },
            CaseClass.ADD_ON: {"delivery", "newborn", "psych", "rehab"},
        }

    @property
    def _get_date_and_time_format(self) -> tuple[str, str]:
        return self._DATE_FORMAT, self._TIME_FORMAT

    def _get_primary_surgeon_struct(
        self, surgical_staff, primary_procedure_surgeon_id
    ) -> Optional[dict[str, Optional[str]]]:
        if not surgical_staff or not primary_procedure_surgeon_id:
            return None

        try:
            surgical_staff_json = json.loads(surgical_staff)

            for staff_info in surgical_staff_json:
                if (
                    primary_procedure_surgeon_id in staff_info["staff_id"]
                    and staff_info["staff_role"] == self._STAFF_ROLES["primary"]
                ):
                    return self._primary_surgeon_struct(staff_info["staff_name"])
        except json.decoder.JSONDecodeError:
            return None

        return None

    def get_primary_surgeon(self) -> pd.Series:
        return self.file_df[["surgical_staff", "primary_procedure_surgeon_id"]].apply(
            lambda row: self._get_primary_surgeon_struct(
                row.surgical_staff, row.primary_procedure_surgeon_id
            ),
            axis=1,
        )

    def get_primary_surgeon_staff_id(self) -> pd.Series:
        return self.file_df.primary_procedure_surgeon_id

    def get_cancellation_reason(self) -> None:
        return None
