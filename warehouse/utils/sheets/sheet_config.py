from dataclasses import dataclass
from typing import Dict, Optional

from warehouse.configs.warehouse_config import WAREHOUSE_ENVIRONMENT


@dataclass
class SheetConfig:
    name: str  # Human-readable name for logging
    spreadsheet_id: Dict[str, str]  # Mapping of environment to spreadsheet ID
    tab_name: str  # Name of the sheet tab
    sheet_range: str  # Range of cells to read (e.g., "A1:Z1000")
    destination_table_name: str  # Name of the BigQuery table to write to
    schema: Dict[str, str]  # Mapping of column names to pandas dtypes
    col_name_mapping: Optional[Dict[str, str]] = (
        None  # Mapping of destination column names to sheet column names
    )

    @property
    def current_spreadsheet_id(self) -> Optional[str]:
        """Get the spreadsheet ID for the current environment."""
        return self.spreadsheet_id.get(WAREHOUSE_ENVIRONMENT)

    @property
    def bigquery_schema(self) -> Dict[str, str]:
        """Convert pandas dtypes to BigQuery types."""
        type_mapping = {
            "string": "string",
            "int64": "integer",
            "float64": "float",
            "bool": "boolean",
            "datetime64[ns]": "datetime",
            "date": "date",
        }
        return {col: type_mapping[dtype] for col, dtype in self.schema.items()}
