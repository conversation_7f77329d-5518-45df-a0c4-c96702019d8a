import logging
from collections import defaultdict
from typing import Dict, List

import pandas as pd
from dagster import AssetExecutionContext, AssetsDefinition

from warehouse.utils import apella_asset
from warehouse.utils.assets.helpers import get_date_from_ds_str, get_day_of_partitions_def
from warehouse.utils.sheets.sheet_config import SheetConfig

logger = logging.getLogger(__name__)


class SheetsAssetFactory:
    def _get_sheet_values(
        self, config: SheetConfig, context: AssetExecutionContext
    ) -> List[List[str]]:
        """Get values from Google Sheets API, validate response, and pad rows."""
        assert (
            config.current_spreadsheet_id is not None
        ), f"Sheet {config.name} has no spreadsheet for this environment"

        result = context.resources.sheets_resource.get_sheet(
            config.current_spreadsheet_id, f"{config.tab_name}!{config.sheet_range}"
        )
        values = result.get("values", [])
        assert values and len(values) > 1, f"No data found in sheet {config.name}"

        # Pad rows to match header length
        header = values[0]
        padded_values = [header]  # Keep header as is
        for row in values[1:]:
            padded_row = row + [""] * (len(header) - len(row))
            padded_values.append(padded_row)

        return padded_values

    def _create_dataframe(self, values: List[List[str]], config: SheetConfig) -> pd.DataFrame:
        """Create DataFrame from values and apply column mapping."""
        header = values[0]
        data = values[1:]  # Skip header row

        # Note: Google Sheets API returns all values as strings, even numbers and dates
        df = pd.DataFrame(data, columns=header)

        if config.col_name_mapping:
            df = df.rename(columns={v: k for k, v in config.col_name_mapping.items()})
            df = df[list(config.col_name_mapping.keys())]

        missing_cols = set(config.schema.keys()) - set(df.columns)
        assert not missing_cols, f"Missing columns in sheet {config.name}: {missing_cols}"

        return df

    def _convert_bool(self, x) -> bool | None:
        """Convert string value to boolean, only accepting 'true'/'false' or pd.NA."""
        if pd.isna(x):
            return pd.NA
        val = str(x).strip().lower()
        if val not in ["true", "false"]:
            raise ValueError(f"Boolean values must be 'true' or 'false', got '{x}'")
        return val == "true"

    def _convert_types(self, df: pd.DataFrame, config: SheetConfig) -> pd.DataFrame:
        """Convert empty strings to None and apply type conversion."""
        # Convert empty strings to None before type conversion
        for col in df.columns:
            df[col] = df[col].replace("", pd.NA)

        # Convert string values to their proper types as specified in schema
        for col, dtype in config.schema.items():
            if dtype == "int64":
                df[col] = pd.to_numeric(df[col], errors="raise").astype("Int64")
            elif dtype == "float64":
                df[col] = pd.to_numeric(df[col], errors="raise")
            elif dtype == "bool":
                df[col] = df[col].apply(self._convert_bool).astype("boolean")
            elif dtype == "datetime64[ns]":
                df[col] = pd.to_datetime(df[col])
            else:  # other types
                df[col] = df[col].astype(dtype)

        return df

    def _read_sheet(self, config: SheetConfig, context: AssetExecutionContext) -> pd.DataFrame:
        """Read data from a Google Sheet and convert to a DataFrame."""
        values = self._get_sheet_values(config, context)
        df = self._create_dataframe(values, config)
        df = self._convert_types(df, config)

        # Add source_google_sheet_id column
        df["source_google_sheet_id"] = config.current_spreadsheet_id
        df["source_google_sheet_id"] = df["source_google_sheet_id"].astype("string")

        return df

    def _validate_schemas_match(self, configs: List[SheetConfig]) -> None:
        first_schema = configs[0].schema
        for config in configs[1:]:
            assert config.schema == first_schema, (
                f"Schema mismatch for table {config.destination_table_name}. "
                f"Expected {first_schema}, got {config.schema}"
            )

    def _group_configs_by_table(self, configs: List[SheetConfig]) -> Dict[str, List[SheetConfig]]:
        table_configs = defaultdict(list)
        for config in configs:
            if config.current_spreadsheet_id is None:
                logger.warning(
                    f"Skipping sheet {config.name} because it has no spreadsheet for this environment"
                )
            else:
                table_configs[config.destination_table_name].append(config)
        return dict(table_configs)

    def create_asset(self, table_name: str, configs: List[SheetConfig]) -> AssetsDefinition:
        self._validate_schemas_match(configs)

        @apella_asset(
            io_manager_key="date_partitioned_pandas_bq_io_manager",
            name=table_name,
            group_name="sheets",
            key_prefix="bronze",
            compute_kind="pandas",
            partitions_def=get_day_of_partitions_def(),
            description=f"Data from Google Sheets for table: {table_name}",
            required_resource_keys={"sheets_resource"},
            metadata={
                "partition_expr": "ds",
                "partition_cadence": "day",
                "table_schema": {
                    **configs[0].bigquery_schema,
                    "ds": "date",
                    "source_google_sheet_id": "string",
                },
            },
        )
        def sheet_asset(context: AssetExecutionContext) -> pd.DataFrame:
            dfs = []
            for config in configs:
                context.log.info(f"Reading sheet: {config.name}")
                df = self._read_sheet(config, context)
                context.log.info(f"Read {len(df)} rows from sheet {config.name}")
                dfs.append(df)

            combined_df = pd.concat(dfs, ignore_index=True)
            combined_df["ds"] = get_date_from_ds_str(context.partition_key)
            context.log.info(f"Combined {len(combined_df)} total rows from {len(configs)} sheets")
            return combined_df

        return sheet_asset

    def create_assets(self, configs: List[SheetConfig]) -> List[AssetsDefinition]:
        table_configs = self._group_configs_by_table(configs)
        return [
            self.create_asset(table_name, table_configs[table_name]) for table_name in table_configs
        ]
