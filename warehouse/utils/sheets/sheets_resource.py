import google.auth
from dagster import ConfigurableResource, InitResourceContext
from googleapiclient.discovery import Resource, build
from pydantic import PrivateAttr


class SheetsResource(ConfigurableResource):  # type: ignore[misc]
    _SCOPES = [
        "https://www.googleapis.com/auth/spreadsheets",
        "https://www.googleapis.com/auth/drive",
    ]

    _google_api_client: Resource = PrivateAttr()

    def setup_for_execution(self, context: InitResourceContext) -> None:
        creds, _ = google.auth.default(scopes=self._SCOPES)
        self._google_api_client = build("sheets", "v4", credentials=creds)

    def get_sheet(self, sheet_id, range) -> dict:
        return (
            self._google_api_client.spreadsheets()  # type: ignore
            .values()
            .get(
                spreadsheetId=sheet_id,
                range=range,
            )
            .execute()
        )
