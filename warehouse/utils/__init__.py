import os

from dagster import asset


def get_key_prefix_or_sandbox(key_prefix: str) -> str:
    return key_prefix if os.environ.get("WAREHOUSE_RUNTIME_ENVIRONMENT") != "local" else "sandbox"


def apella_asset(
    **kwargs,
):
    def decorator(func):
        key_prefix = kwargs.pop("key_prefix", None)
        if key_prefix:
            kwargs["key_prefix"] = get_key_prefix_or_sandbox(key_prefix)
        return asset(**kwargs)(func)

    return decorator
