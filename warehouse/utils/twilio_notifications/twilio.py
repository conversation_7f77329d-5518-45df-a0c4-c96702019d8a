import datetime

from dagster import ConfigurableResource, InitResourceContext
from google.cloud.secretmanager import SecretManagerServiceClient
from pydantic import PrivateAttr
from twilio.rest import Client  # type: ignore
from twilio.rest.api.v2010.account.message import (  # type: ignore
    MessageInstance,
    MessagePage,
)


class TwilioResource(ConfigurableResource):  # type: ignore[misc]
    twilio_auth_token_name: str
    twilio_account_sid_name: str

    _twilio_client: Client = PrivateAttr()

    def _get_auth_token(self) -> str:
        client = SecretManagerServiceClient()
        response = client.access_secret_version(request={"name": self.twilio_auth_token_name})
        return response.payload.data.decode()

    def _get_account_sid(self) -> str:
        client = SecretManagerServiceClient()
        response = client.access_secret_version(request={"name": self.twilio_account_sid_name})
        return response.payload.data.decode()

    def setup_for_execution(self, context: InitResourceContext) -> None:
        self._twilio_client = Client(self._get_account_sid(), self._get_auth_token())

    def get_notifications(self, notification_date: str) -> list[MessageInstance]:
        # uses the run `ds` to fetch notifications for that day. recall that Dagster runs T at T+1
        messages: list[MessageInstance] = []
        messagePage: MessagePage = self._twilio_client.messages.page(
            date_sent=datetime.datetime.strptime(notification_date, "%Y-%m-%d %H:%M:%S")
        )
        while messagePage:
            for message in messagePage:
                messages.append(message)
            messagePage = messagePage.next_page()
        return messages
