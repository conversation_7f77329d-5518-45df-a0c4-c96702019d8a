from typing import List

from warehouse.utils.sheets.sheet_config import SheetConfig

# Define your sheet configurations here
SHEET_CONFIGS: List[SheetConfig] = [
    SheetConfig(
        name="CRM Configs",
        spreadsheet_id={
            "dev": "1CxbgdvhGr_IeGinlO0fBZpR9Hc6fQQBqXw7OJZWvzX8",
            "prod": "1Dcx1c3WaK8h8B7U_U5TUZzl--sqCvXz4H4U0xRUudFs",
        },
        tab_name="Users",
        sheet_range="B1:Z900",
        col_name_mapping={"email": "Email", "job_title": "Title"},
        destination_table_name="cs_google_sheets_users_to_org_titles",
        schema={"email": "string", "job_title": "string"},
    ),
    SheetConfig(
        name="HF CRM Config",
        spreadsheet_id={"prod": "1Xg7e3zUGdBpO7f-kQDQMLlo4Sylb5DQpdZuJPO9NeTU"},
        tab_name="Users",
        sheet_range="A2:F32",
        col_name_mapping={"email": "Email", "job_title": "Job Title"},
        destination_table_name="cs_google_sheets_users_to_org_titles",
        schema={"email": "string", "job_title": "string"},
    ),
    SheetConfig(
        name="NB CRM Config",
        spreadsheet_id={"prod": "1ydn8oB5KsTJYUWF7boQd-bCCKUBY0YPOnuCLE62c0JI"},
        tab_name="AD user group list",
        sheet_range="A2:Z900",
        col_name_mapping={"email": "Email", "job_title": "Cerner Active Directroy group"},
        destination_table_name="cs_google_sheets_users_to_org_titles",
        schema={"email": "string", "job_title": "string"},
    ),
    SheetConfig(
        name="TGH CRM Config",
        spreadsheet_id={"prod": "13gQKI2pQ3EVLe_BYBsqt85adsOG2blbjQgauFHSU6ZA"},
        tab_name="Users",
        sheet_range="A1:G100",
        col_name_mapping={"email": "Email", "job_title": "Role"},
        destination_table_name="cs_google_sheets_users_to_org_titles",
        schema={"email": "string", "job_title": "string"},
    ),
    SheetConfig(
        name="LifeBridge CRM Config",
        spreadsheet_id={"prod": "1K0BGioa_i_pdEjxCO9B2mCAUWZ6n2nQFCzEudwnU2yM"},
        tab_name="Users [Active]",
        sheet_range="A1:X900",
        col_name_mapping={"email": "Email", "job_title": "Title"},
        destination_table_name="cs_google_sheets_users_to_org_titles",
        schema={"email": "string", "job_title": "string"},
    ),
    SheetConfig(
        name="NYU CRM Config",
        spreadsheet_id={"prod": "1cCamDiwDjVqdtrnPgAQe9xFdGLqG3Srvi6uvkhmuP6o"},
        tab_name="Users",
        sheet_range="A1:Z900",
        col_name_mapping={"email": "Email", "job_title": "Title"},
        destination_table_name="cs_google_sheets_users_to_org_titles",
        schema={"email": "string", "job_title": "string"},
    ),
    SheetConfig(
        name="CSM Adhoc Bespoke Data Requests",
        spreadsheet_id={"prod": "16poK0IT8HQziRoULwSH6gwVBhj9dNmbcxlFKzYCIONA"},
        tab_name="Bespoke/Adhoc Data Requests",
        sheet_range="A1:K500",
        col_name_mapping={
            "org_name": "Customer Name",
            "site_name": "Site",
            "requestor_email": "Requestor Email",
            "delivery_date": "Date of Data Delivery",
            "request_intake_employee": "CSM/Apellan Who Fielded Request",
            "request_subject": "Subject",
            "request_date": "Date of Initial Request",
            "request_type": "Type of Request",
            "request_delivery_cadence": "Delivery Cadence",
        },
        destination_table_name="cs_google_sheets_adhoc_bespoke_data_requests",
        schema={
            "org_name": "string",
            "site_name": "string",
            "requestor_email": "string",
            "delivery_date": "datetime64[ns]",
            "request_intake_employee": "string",
            "request_subject": "string",
            "request_date": "datetime64[ns]",
            "request_type": "string",
            "request_delivery_cadence": "string",
        },
    ),
    # Billing Configs
    SheetConfig(
        name="Billing Site Start Dates",
        spreadsheet_id={
            "dev": "1e7w_MQQnxUN3e7NO7Vx_bcHXJA10l7K2iLTWiMm_H4g",
            "prod": "1otoeoeVzZ50kQREXNnIhUn4s0P58h6gKZ35ezr7NznQ",
        },
        tab_name="Site Start Dates",
        sheet_range="A1:C1000",
        col_name_mapping={
            "site": "Site",
            "ors": "Number of ORs",
            "recording_start_date": "Recording Start",
        },
        destination_table_name="billing_google_sheets_site_start_dates",
        schema={"site": "string", "ors": "int64", "recording_start_date": "datetime64[ns]"},
    ),
    SheetConfig(
        name="Billing GCP Service Categories",
        spreadsheet_id={
            "dev": "1e7w_MQQnxUN3e7NO7Vx_bcHXJA10l7K2iLTWiMm_H4g",
            "prod": "1otoeoeVzZ50kQREXNnIhUn4s0P58h6gKZ35ezr7NznQ",
        },
        tab_name="Services",
        sheet_range="A1:B1000",
        col_name_mapping={"service": "Services", "category": "Category"},
        destination_table_name="billing_google_sheets_gcp_services_category",
        schema={"service": "string", "category": "string"},
    ),
    SheetConfig(
        name="Billing GCP Project Categories",
        spreadsheet_id={
            "dev": "1e7w_MQQnxUN3e7NO7Vx_bcHXJA10l7K2iLTWiMm_H4g",
            "prod": "1otoeoeVzZ50kQREXNnIhUn4s0P58h6gKZ35ezr7NznQ",
        },
        tab_name="Projects",
        sheet_range="A1:C1000",
        col_name_mapping={
            "project": "Projects",
            "category": "Category",
            "compute_cogs_pct": "Compute PCT COGS",
        },
        destination_table_name="billing_google_sheets_gcp_project_category",
        schema={"project": "string", "category": "string", "compute_cogs_pct": "float64"},
    ),
    SheetConfig(
        name="Billing Kubernetes Namespaces",
        spreadsheet_id={
            "dev": "1e7w_MQQnxUN3e7NO7Vx_bcHXJA10l7K2iLTWiMm_H4g",
            "prod": "1otoeoeVzZ50kQREXNnIhUn4s0P58h6gKZ35ezr7NznQ",
        },
        tab_name="Kubernetes Namespaces",
        sheet_range="A1:D1000",
        col_name_mapping={
            "namespace": "Namespace",
            "category": "Category",
            "team": "Team",
            "feature": "Feature",
        },
        destination_table_name="billing_google_sheets_kubernetes_namespaces",
        schema={"namespace": "string", "category": "string", "team": "string", "feature": "string"},
    ),
    SheetConfig(
        name="Billing GCP Project Service Ownership",
        spreadsheet_id={
            "dev": "1e7w_MQQnxUN3e7NO7Vx_bcHXJA10l7K2iLTWiMm_H4g",
            "prod": "1otoeoeVzZ50kQREXNnIhUn4s0P58h6gKZ35ezr7NznQ",
        },
        tab_name="Project Service Ownership",
        sheet_range="A1:D1000",
        col_name_mapping={
            "project": "Projects",
            "service": "Service",
            "team": "Team",
            "feature": "Feature",
        },
        destination_table_name="billing_google_sheets_gcp_project_service_ownership",
        schema={"project": "string", "service": "string", "team": "string", "feature": "string"},
    ),
    SheetConfig(
        name="Harmonized Procedure Names Config",
        spreadsheet_id={
            "dev": "1w-H2PPX6ttUKtH2gvf-FBBmTJJI9w4GvMc3e7imxYAM",
            "prod": "1w-H2PPX6ttUKtH2gvf-FBBmTJJI9w4GvMc3e7imxYAM",
        },
        tab_name="main",
        sheet_range="A1:J30000",
        destination_table_name="procedure_name_harmonized",
        schema={
            "original_name": "string",
            "procedure_name_harmonized": "string",
            "procedure_name_harmonized_alphabetically_sorted": "string",
            "laterality": "string",
            "min_duration": "string",
            "mean_duration": "string",
            "max_duration": "string",
            "cpt_code": "string",
            "cpt_code_notes": "string",
            "duration_notes": "string",
        },
    ),
    SheetConfig(
        name="Implicit Turnover Within Scheduled Case Duration Config",
        spreadsheet_id={
            "dev": "1wRW8yQLXBrOrCuU3prUdgh3jotO-d0pI9xSRp8A1su0",
            "prod": "1wRW8yQLXBrOrCuU3prUdgh3jotO-d0pI9xSRp8A1su0",
        },
        tab_name="implicit_turnover",
        sheet_range="A1:G3000",
        destination_table_name="implicit_turnover",
        schema={
            "org_id": "string",
            "site_id": "string",
            "site_name": "string",
            "first_case": "bool",
            "service_line_name": "string",
            "flip_room": "bool",
            "implicit_turnover": "int64",
        },
    ),
    SheetConfig(
        name="Linear Issues",
        spreadsheet_id={
            "dev": "1KbstyU6qZP_J9Ls9wi6o9cwPDqZUUuzoFa7bok7ixxc",
            "prod": "1KbstyU6qZP_J9Ls9wi6o9cwPDqZUUuzoFa7bok7ixxc",
        },
        tab_name="Issues",
        sheet_range="A1:AD30000",
        destination_table_name="linear_issues",
        schema={
            "ID": "string",
            "Team": "string",
            "Title": "string",
            "Description": "string",
            "Status": "string",
            "Estimate": "int64",
            "Priority": "string",
            "Project ID": "string",
            "Project": "string",
            "Creator": "string",
            "Assignee": "string",
            "Labels": "string",
            "Cycle Number": "int64",
            "Cycle Name": "string",
            "Cycle Start": "datetime64[ns]",
            "Cycle End": "datetime64[ns]",
            "Created": "datetime64[ns]",
            "Updated": "datetime64[ns]",
            "Started": "datetime64[ns]",
            "Triaged": "datetime64[ns]",
            "Completed": "datetime64[ns]",
            "Canceled": "datetime64[ns]",
            "Archived": "datetime64[ns]",
            "Due Date": "datetime64[ns]",
            "Parent issue": "string",
            "Initiatives": "string",
            "Project Milestone ID": "string",
            "Project Milestone": "string",
            "SLA Status": "string",
            "Roadmaps": "string",
        },
        col_name_mapping={
            "id": "ID",
            "team": "Team",
            "title": "Title",
            "description": "Description",
            "status": "Status",
            "estimate": "Estimate",
            "priority": "Priority",
            "project_id": "Project ID",
            "project": "Project",
            "creator": "Creator",
            "assignee": "Assignee",
            "labels": "Labels",
            "cycle_number": "Cycle Number",
            "cycle_name": "Cycle Name",
            "cycle_start": "Cycle Start",
            "cycle_end": "Cycle End",
            "created": "Created",
            "updated": "Updated",
            "started": "Started",
            "triaged": "Triaged",
            "completed": "Completed",
            "canceled": "Canceled",
            "archived": "Archived",
            "due_date": "Due Date",
            "parent_issue": "Parent issue",
            "initiatives": "Initiatives",
            "project_milestone_id": "Project Milestone ID",
            "project_milestone": "Project Milestone",
            "sla_status": "SLA Status",
            "roadmaps": "Roadmaps",
        },
    ),
]
