import pathlib
from datetime import datetime
from typing import List, Type

from warehouse.configs.warehouse_config import WAREHOUSE_ENVIRONMENT
from warehouse.utils.historical_cases.schema_mergers.base_schema_merger import (
    BaseSchemaMerger,
)
from warehouse.utils.historical_cases.schema_mergers.health_first.health_first_schema_merger import (
    HealthFirstSchemaMerger,
)
from warehouse.utils.historical_cases.schema_mergers.houston_methodist.hmh_first_file_schema_merger import (
    HMHFirstFileSchemaMerger,
)
from warehouse.utils.historical_cases.schema_mergers.houston_methodist.hmh_second_file_schema_merger import (
    HMHSecondFileSchemaMerger,
)
from warehouse.utils.historical_cases.schema_mergers.lifebridge.lifebridge_base_schema_merger import (
    LifebridgeBaseSchemaMerger,
)
from warehouse.utils.historical_cases.schema_mergers.nyu.nyu_schema_merger import (
    NYUSchemaMerger,
)
from warehouse.utils.historical_cases.schema_mergers.tampa_general.tgh_schema_merger import (
    TGHSchemaMerger,
)


class HistoricalCasesConfig:
    def __init__(
        self,
        org_id: str,
        file_name: str,
        file_date: str,
        schema_merger: Type[BaseSchemaMerger],
        file_encoding: str = "utf-8",
        file_delimiter: str = ",",
    ):
        self.org_id = org_id
        self.file_name = file_name
        self.file_date = datetime.strptime(file_date, "%Y-%m-%d").date()
        self.schema_merger = schema_merger
        self.table_name = pathlib.Path(file_name).stem.replace("-", "_").lower()
        self.file_encoding = file_encoding
        self.file_delimiter = file_delimiter


_CONFIG = {
    "prod": [
        HistoricalCasesConfig(
            org_id="health_first",
            file_name="health_first_historical_cases_2023-03-01.csv",
            file_date="2023-03-01",
            schema_merger=HealthFirstSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="houston_methodist",
            file_name="houston_methodist_historical_cases_2022-09-01.csv",
            file_date="2022-09-01",
            schema_merger=HMHFirstFileSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="houston_methodist",
            file_name="Apella_Historical_Cases_HMCL_ASC_OR_20240308.csv",
            file_date="2024-03-08",
            schema_merger=HMHSecondFileSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="houston_methodist",
            file_name="Apella_Historical_Cases_HMCL_LD_20240308.csv",
            file_date="2024-03-08",
            schema_merger=HMHSecondFileSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="houston_methodist",
            file_name="Apella_Historical_Cases_HMCL_OR_20240308.csv",
            file_date="2024-03-08",
            schema_merger=HMHSecondFileSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="houston_methodist",
            file_name="Apella_Historical_Cases_HMH_DUNN_20240308.csv",
            file_date="2024-03-08",
            schema_merger=HMHSecondFileSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="houston_methodist",
            file_name="Apella_Historical_Cases_HMH_DUNN_6_20240308.csv",
            file_date="2024-03-08",
            schema_merger=HMHSecondFileSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="houston_methodist",
            file_name="Apella_Historical_Cases_HMH_LD_20240308.csv",
            file_date="2024-03-08",
            schema_merger=HMHSecondFileSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="houston_methodist",
            file_name="Apella_Historical_Cases_HMH_Main_OR_20240308.csv",
            file_date="2024-03-08",
            schema_merger=HMHSecondFileSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="houston_methodist",
            file_name="Apella_Historical_Cases_HMH_OPC_18_20240308.csv",
            file_date="2024-03-08",
            schema_merger=HMHSecondFileSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="houston_methodist",
            file_name="Apella_Historical_Cases_HMH_OPC_19_20240308.csv",
            file_date="2024-03-08",
            schema_merger=HMHSecondFileSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="houston_methodist",
            file_name="Apella_Historical_Cases_HMH_WALTER_20240308.csv",
            file_date="2024-03-08",
            schema_merger=HMHSecondFileSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="houston_methodist",
            file_name="Apella_Historical_Cases_HMSJ_ASU_OR_20240308.csv",
            file_date="2024-03-08",
            schema_merger=HMHSecondFileSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="houston_methodist",
            file_name="Apella_Historical_Cases_HMSJ_LD_20240308.csv",
            file_date="2024-03-08",
            schema_merger=HMHSecondFileSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="houston_methodist",
            file_name="Apella_Historical_Cases_HMSJ_OR_20240308.csv",
            file_date="2024-03-08",
            schema_merger=HMHSecondFileSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="houston_methodist",
            file_name="Apella_Historical_Cases_HMSL_LD_20240308.csv",
            file_date="2024-03-08",
            schema_merger=HMHSecondFileSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="houston_methodist",
            file_name="Apella_Historical_Cases_HMSL_MAIN_20240308.csv",
            file_date="2024-03-08",
            schema_merger=HMHSecondFileSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="houston_methodist",
            file_name="Apella_Historical_Cases_HMTW_LD_20240308.csv",
            file_date="2024-03-08",
            schema_merger=HMHSecondFileSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="houston_methodist",
            file_name="Apella_Historical_Cases_HMTW_OR_20240308.csv",
            file_date="2024-03-08",
            schema_merger=HMHSecondFileSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="houston_methodist",
            file_name="Apella_Historical_Cases_HMWB_CF_OR_20240308.csv",
            file_date="2024-03-08",
            schema_merger=HMHSecondFileSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="houston_methodist",
            file_name="Apella_Historical_Cases_HMWB_LD_20240308.csv",
            file_date="2024-03-08",
            schema_merger=HMHSecondFileSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="houston_methodist",
            file_name="Apella_Historical_Cases_HMWB_OR_20240308.csv",
            file_date="2024-03-08",
            schema_merger=HMHSecondFileSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="houston_methodist",
            file_name="Apella_Historical_Cases_HMW_LD_20240308.csv",
            file_date="2024-03-08",
            schema_merger=HMHSecondFileSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="houston_methodist",
            file_name="Apella_Historical_Cases_HMW_OR_20240308.csv",
            file_date="2024-03-08",
            schema_merger=HMHSecondFileSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="tampa_general",
            file_name="TGH_Historical_Surgical_Data_v3.txt",
            file_date="2024-03-05",
            schema_merger=TGHSchemaMerger,
            file_encoding="windows-1252",
            file_delimiter="|",
        ),
        HistoricalCasesConfig(
            org_id="nyu",
            file_name="nyu_data_ingest_20111101-20240404.csv",
            file_date="2023-04-04",
            schema_merger=NYUSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="nyu",
            file_name="nyu_data_ingest_20240405-20241029.csv",
            file_date="2023-10-29",
            schema_merger=NYUSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="nyu",
            file_name="ORLdata-20230405-20241029_EAF.csv",
            file_date="2025-05-19",
            schema_merger=NYUSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="lifebridge",
            file_name="apella_or_hist_rpt_01jul2022_31dec2022_11_25_24.csv",
            file_date="2024-11-27",
            schema_merger=LifebridgeBaseSchemaMerger,
            file_encoding="windows-1252",
        ),
        HistoricalCasesConfig(
            org_id="lifebridge",
            file_name="apella_or_hist_rpt_01jan2023_30jun2023_NEW.csv",
            file_date="2024-12-16",
            schema_merger=LifebridgeBaseSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="lifebridge",
            file_name="apella_or_hist_rpt_01jul2023_31dec2023_NEW.csv",
            file_date="2024-12-16",
            schema_merger=LifebridgeBaseSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="lifebridge",
            file_name="apella_or_hist_rpt_01jan2024_30jun2024_NEW.csv",
            file_date="2024-12-16",
            schema_merger=LifebridgeBaseSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="lifebridge",
            file_name="apella_or_hist_rpt_01jul2024_30nov2024_NEW.csv",
            file_date="2024-12-16",
            schema_merger=LifebridgeBaseSchemaMerger,
        ),
    ],
    "dev": [
        HistoricalCasesConfig(
            org_id="apella",
            file_name="apella_historical_cases_2023-03-01.csv",
            file_date="2023-03-01",
            # this dev file has the same schema as `health_first_*.csv`
            schema_merger=HealthFirstSchemaMerger,
        ),
        HistoricalCasesConfig(
            org_id="sacred_heart",
            file_name="sacred_heart_historical_cases_2022-09-01.csv",
            file_date="2022-09-01",
            # this dev file has the same schema as `houston_methodist_*.csv`
            schema_merger=HMHFirstFileSchemaMerger,
        ),
    ],
}


def get_historical_cases_configs() -> List[HistoricalCasesConfig]:
    return _CONFIG[WAREHOUSE_ENVIRONMENT]
