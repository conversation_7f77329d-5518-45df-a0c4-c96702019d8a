import os
from dataclasses import dataclass
from enum import Enum

from dagster import FilesystemIOManager
from dagster._utils import file_relative_path
from dagster_gcp.gcs.io_manager import PickledObjectGCSIOManager

# TODO: clean this up, refactor into yaml configs


class WarehouseDatasets(Enum):
    BRONZE = "bronze"
    SILVER = "silver"
    GOLD = "gold"


_AUTH0_DOMAINS = {
    "dev": "apella-dev.us.auth0.com",
    "staging": "apella-staging.us.auth0.com",
    "prod": "apella.us.auth0.com",
}


_GCP_PROJECT_IDS = {
    "dev": "dev-data-platform-439b4c",
    "prod": "prod-data-platform-027529",
}

_GCP_ML_PROJECT_IDS = {
    "dev": "dev-ml-794354",
    "prod": "prod-ml-2fc132",
}

_WEBSERVER_BASE_URLS = {
    "dev": "https://dagster.dev.internal.apella.io",
    "prod": "https://dagster.internal.apella.io",
}


ONCALL_PLAYBOOK_LINK = (
    "https://www.notion.so/apella/Data-Warehouse-Playbook-df7eddaf65f742b7bd7aa1291170ddb1"  # noqa: E501
)

WAREHOUSE_ENVIRONMENT = os.environ["WAREHOUSE_ENVIRONMENT"]

AUTH0_DOMAIN = _AUTH0_DOMAINS[WAREHOUSE_ENVIRONMENT]

GCP_PROJECT_ID = _GCP_PROJECT_IDS[WAREHOUSE_ENVIRONMENT]
GCP_ML_PROJECT_ID = _GCP_ML_PROJECT_IDS[WAREHOUSE_ENVIRONMENT]
GCS_HISTORICAL_FILE_BUCKET = f"{WAREHOUSE_ENVIRONMENT}-data-warehouse"

DBT_PROJECT_NAME = "data_warehouse"
DBT_PROJECT_DIR = file_relative_path(__file__, "../../dbt")
DBT_PROFILES_DIR = file_relative_path(__file__, "../../dbt/config")
DBT_CONFIG = {
    "project_dir": DBT_PROJECT_DIR,
    "profiles_dir": DBT_PROFILES_DIR,
    "target": WAREHOUSE_ENVIRONMENT,
}

DS_FORMAT = "%Y-%m-%d %H:%M:%S"
DEFAULT_PARTITION_TIMEZONE = "America/Los_Angeles"

WEBSERVER_BASE_URL = _WEBSERVER_BASE_URLS[WAREHOUSE_ENVIRONMENT]

ENCORD_ENVIRONMENT_OPTIONS = {
    "dev": [
        "e45a828c-4ace-45c0-af17-e58a54815328",  # Main Objects Dev
        "dbb68bff-3985-48f6-a3b5-63dfb96f0dc1",  # Secondary Objects Dev
    ],
    "prod": [
        "f8a3f441-e328-4f5f-b1af-b63d5c51cd5e",  # Apella Non-Personnel Prod
        "e4b5c374-0258-40de-b6fc-bf7c06f827c9",  # Apella Personnel Prod
    ],
}


def _get_latest_secret_name(secret_name, project_name=GCP_PROJECT_ID) -> str:
    return "/".join(
        [
            f"projects/{project_name}/secrets",
            f"{WAREHOUSE_ENVIRONMENT}-{secret_name}",
            "versions/latest",
        ]
    )


AMPLITUDE_API_KEY = _get_latest_secret_name("amplitude-dashboard-api-key")
AMPLITUDE_SECRET_KEY = _get_latest_secret_name("amplitude-dashboard-secret-key")
AUTH0_MANAGEMENT_CLIENT_ID_NAME = _get_latest_secret_name("auth0-management-client-id")
AUTH0_MANAGEMENT_CLIENT_SECRET_NAME = _get_latest_secret_name("auth0-management-client-secret")
OPSGENIE_API_KEY_SECRET_NAME = _get_latest_secret_name("opsgenie-dsml-api-key")
SLACK_AUTH_TOKEN_SECRET_NAME = _get_latest_secret_name("slack-auth-token")
TWILIO_AUTH_TOKEN_NAME = _get_latest_secret_name("twilio-auth-token")
TWILIO_SID_NAME = _get_latest_secret_name("twilio-account-sid")
ENCORD_API_KEY_NAME = _get_latest_secret_name("encord-api-key", project_name=GCP_ML_PROJECT_ID)
ENCORD_PROJECT_HASHES = ENCORD_ENVIRONMENT_OPTIONS[WAREHOUSE_ENVIRONMENT]


def is_warehouse_prod_env():
    return WAREHOUSE_ENVIRONMENT == "prod"


def get_pickled_io_manager():
    return (
        FilesystemIOManager()
        if os.environ.get("WAREHOUSE_RUNTIME_ENVIRONMENT") == "local"
        else PickledObjectGCSIOManager(f"{WAREHOUSE_ENVIRONMENT}-data-warehouse-dagster-io-manager")
    )


@dataclass
class ApiDBSqlConfig:
    database: str
    host: str
    port: int
    user: str
    password_key: str
    gcp_project: str


_API_SQL_DB_CONFIGS = {
    "local": ApiDBSqlConfig(
        database="postgres",
        host="localhost",
        port=3306,
        user="api-server",
        password_key="dev-api-user-password",
        gcp_project="dev-web-api-72f12b",
    ),
    "dev": ApiDBSqlConfig(
        database="postgres",
        host="dev-web-api-72f12b:us-central1:dev-postgres-01",
        port=5432,
        user="api-server",
        password_key="dev-api-user-password",
        gcp_project="dev-web-api-72f12b",
    ),
    "prod": ApiDBSqlConfig(
        database="postgres",
        host="prod-web-api-7f60bf:us-central1:prod-postgres-01-replica-analytics",
        port=5432,
        user="api-server",
        password_key="prod-api-user-password",
        gcp_project="prod-web-api-7f60bf",
    ),
}

API_SQL_DB_CONFIG = (
    _API_SQL_DB_CONFIGS["local"]
    if os.environ.get("WAREHOUSE_RUNTIME_ENVIRONMENT") == "local"
    else _API_SQL_DB_CONFIGS[WAREHOUSE_ENVIRONMENT]
)
