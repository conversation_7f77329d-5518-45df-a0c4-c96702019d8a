Dagster can be run locally using `$ make run-dev`. Check out localhost to make sure your dependency graph is compiled correctly, and that you can materialize python assets adhoc for a test partition. Note that DBT assets should be materialized using the worfklow described in DBT docs.

If you are adding Python models, run this first as a quick way to verify that your assets can load properly into Dagster runtime.

```
$ make run-tests
```

To run a specific test, you can use the `pytest_args`:

```
$ make run-tests pytest_args=tests/test_defs.py
```

Python assets can also be materialized from the command line:

```
$ make dagster-materialize-assets-[dev|prod] ds="%Y-%m-%d %H:%M:%S" select="<dagster asset select statement>"
```

`select` is a direct interface to dagster's `select` -- see [documentation](https://docs.dagster.io/concepts/assets/asset-selection-syntax#usage). This command will automatically write your selected tables to sandbox, so note that the asset key will need to include the sandbox/ prefix. Most of the time you will only need the following:

- `select="sandbox/<asset name>"` materializes a single asset
- `select="+sandbox/<asset name>"` materializes all dependencies of the provided asset, and then finally the assset itself

#### Google Sheets

##### Adding A New Sheet

If you want to ingest a new Google Sheet to the Data Warehouse, simply:
1. Grant the **Viewer** role on the sheet to the appropriate service account:
   - prod: `<EMAIL>`
   - dev: `<EMAIL>`
2. Add a new config to `warehouse/configs/sheets_config.py`.

```python
SheetConfig(
    # human-readable name used in logs
    name="My Sheet Name",
    # you need a mocked sheet ID for `dev` to test via `make run-dev` (see below)
    spreadsheet_id={"prod": "<prod sheet id>", "dev": "<mocked sheet id for testing in dev>"},
    # the tab where the data lives
    tab_name="Tab Name",
    # data range including the columns
    sheet_range="A1:Z100",
    # OPTIONAL: rename the columns in the sheet to canonical names
    col_name_mapping={
        "dwh_column_name": "Sheet Column Name", 
        "dwh_column_name_2": "Other Sheet Column Name"
    },
    # where the data is loaded within `bronze`
    destination_table_name="my_bigquery_destination_table_name",
    # declare the Pandas data types of your sheet data
    schema={"dwh_column_name": "str", "dwh_column_name_2": "int64"},
)
```

##### Testing

Testing is done via `make run-dev` but you must have a spreadsheet ID for `dev` in the config per the above.

When working with assets reading from Google sheets, users need additional scopes for testing. Authenticate using:

```shell
$ gcloud auth application-default login --scopes=https://www.googleapis.com/auth/cloud-platform,https://www.googleapis.com/auth/spreadsheets,https://www.googleapis.com/auth/drive,openid,https://www.googleapis.com/auth/userinfo.email --billing-project dev-internal-b2aa9f
```

Then with `make run-dev` you can execute your asset which will load your dev sheet data into dev sandbox.