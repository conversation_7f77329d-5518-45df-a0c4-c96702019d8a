from contextlib import contextmanager
from typing import Iterator

from dagster import ConfigurableResource, InitResourceContext
from google.cloud.sql.connector import Connector, IPTypes
from pg8000.dbapi import Connection
from pydantic import PrivateAttr
from sqlalchemy import create_engine
from sqlalchemy.engine import Engine
from sqlalchemy.orm import Session, sessionmaker

from .secret_manager import SecretManagerResource


class PostgresConnection:
    def __init__(
        self,
        username: str,
        password_key: str,
        database: str,
        host: str,
        port: int,
        db_gcp_project: str,
        secret_manager: SecretManagerResource,
    ):
        self.username = username
        self.password = secret_manager.get_secret(
            f"projects/{db_gcp_project}/secrets/{password_key}/versions/latest",
        )
        self.database = database
        self.host = host
        self.port = port

    def create_engine(self) -> Engine:
        if self.host == "localhost":
            return create_engine(
                f"postgresql+pg8000://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"
            )

        def get_gcp_sql_connection() -> Connection:
            with Connector() as connector:
                return connector.connect(
                    self.host,
                    "pg8000",
                    user=self.username,
                    password=self.password,
                    db=self.database,
                    port=self.port,
                    ip_type=IPTypes.PRIVATE,
                )

        return create_engine(
            "postgresql+pg8000://",
            creator=get_gcp_sql_connection,
        )


class ApiDbResource(ConfigurableResource):
    db_sql_user: str
    db_sql_password_key: str
    db_sql_database: str
    db_sql_host: str
    db_sql_port: int
    db_gcp_project: str

    secret_manager_resource: SecretManagerResource
    _db_engine: Engine = PrivateAttr()

    def setup_for_execution(self, context: InitResourceContext) -> None:
        self._db_engine = PostgresConnection(
            username=self.db_sql_user,
            password_key=self.db_sql_password_key,
            database=self.db_sql_database,
            host=self.db_sql_host,
            port=self.db_sql_port,
            db_gcp_project=self.db_gcp_project,
            secret_manager=self.secret_manager_resource,
        ).create_engine()

    def teardown_after_execution(self, context: InitResourceContext) -> None:
        self._db_engine.dispose()

    @contextmanager
    def get_session(self) -> Iterator[Session]:
        session_maker = sessionmaker(self._db_engine)
        new_session = session_maker()
        try:
            yield new_session
        finally:
            new_session.close()
