from dagster import ConfigurableResource
from dagster._core.execution.context.init import InitResourceContext
from google.cloud.secretmanager import SecretManagerServiceClient
from pydantic import PrivateAttr


class SecretManagerResource(ConfigurableResource):
    _client = PrivateAttr()

    def setup_for_execution(self, context: InitResourceContext) -> None:
        try:
            self._client = SecretManagerServiceClient()
        except Exception as e:
            # <PERSON><PERSON><PERSON> doesn't log exceptions raised here, so we'll log them ourselves.
            if context.log is not None:
                context.log.exception("Failed to create SecretManagerServiceClient")
            raise e

    def get_secret(self, secret_id: str) -> str:
        """Retrieves a secret from Google Secret Manager"""
        response = self._client.access_secret_version(request={"name": secret_id})
        return response.payload.data.decode()
