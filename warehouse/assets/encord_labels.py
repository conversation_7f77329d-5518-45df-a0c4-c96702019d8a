import json

import pandas as pd
from dagster import OpExecutionContext
from encord.objects.coordinates import BoundingBoxCoordinates

from warehouse.utils import apella_asset
from warehouse.utils.assets.helpers import get_date_from_ds_str, get_day_of_partitions_def
from warehouse.utils.encord.encord import EncordResource
from warehouse.utils.encord.schema import encord_schema


@apella_asset(
    io_manager_key="date_partitioned_pandas_bq_io_manager",
    metadata={
        "partition_expr": "ds",
        "partition_cadence": "day",
        "table_schema": {
            **encord_schema,
            "ds": "date",
        },
    },
    # bronze holds data from external sources
    group_name="encord",
    key_prefix="bronze",
    # cosmetic, for the UI
    compute_kind="pandas",
    partitions_def=get_day_of_partitions_def(),
)
def encord_labels_daily(
    context: OpExecutionContext,
    encord_resource: EncordResource,
):
    rows = [
        {
            "id": label_row_capsule.label_row.data_hash,
            "annotation_task_status": label_row_capsule.label_row.workflow_graph_node.title
            if label_row_capsule.label_row.workflow_graph_node is not None
            else "",
            "last_edited_at": label_row_capsule.label_row.last_edited_at,
            "object_json": json.dumps(label_row_capsule.label_row.to_encord_dict()),
            "project_hash": label_row_capsule.project_hash,
            "project_name": label_row_capsule.project_name,
            "custom_metadata": json.dumps(label_row_capsule.label_row.client_metadata)
            if label_row_capsule.label_row.client_metadata
            else "",
            "object_labels": [
                {
                    "object_hash": label.object_hash,
                    "label_name": label.object_name,
                    "x1": annotation.coordinates.top_left_x,
                    "x2": annotation.coordinates.width + annotation.coordinates.top_left_x,
                    "y1": annotation.coordinates.top_left_y,
                    "y2": annotation.coordinates.top_left_y + annotation.coordinates.height,
                    "confidence": annotation.confidence,
                }
                for label in label_row_capsule.label_row.get_object_instances()
                for annotation in label.get_annotations()
                if isinstance(annotation.coordinates, BoundingBoxCoordinates)
            ],
            "classifications": json.dumps(label_row_capsule.classifications),
        }
        for label_row_capsule in encord_resource.get_labels()
    ]

    df = pd.DataFrame(rows)
    # The last_edited_at column is created as an object column
    # we need to convert it to datetime for the IO Manager
    df["last_edited_at"] = pd.to_datetime(df.last_edited_at)
    df["ds"] = get_date_from_ds_str(context.partition_key)

    return df
