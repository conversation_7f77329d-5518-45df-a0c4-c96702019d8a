from datetime import datetime
from typing import List

import pandas as pd
from dagster import (
    AssetIn,
    AssetsDefinition,
    OpExecutionContext,
)

from warehouse.configs.historical_cases_config import (
    HistoricalCasesConfig,
    get_historical_cases_configs,
)
from warehouse.configs.warehouse_config import DS_FORMAT
from warehouse.utils import apella_asset
from warehouse.utils.assets.helpers import get_day_of_partitions_def
from warehouse.utils.historical_cases.gcs_historical_file_fetcher import (
    GCSHistoricalFileFetcher,
)
from warehouse.utils.historical_cases.schema_mergers.base_schema_merger import (  # noqa: E501
    BaseSchemaMerger,
)

_partitions_def = get_day_of_partitions_def()


def _get_load_date_from_context(context: OpExecutionContext) -> datetime:
    """Converts the partition key to a datetime object using DS_FORMAT."""
    partition_date_str = context.partition_key
    return datetime.strptime(partition_date_str, DS_FORMAT)


def org_specific_historical_cases(
    config: HistoricalCasesConfig,
) -> AssetsDefinition:
    @apella_asset(
        name=config.table_name,
        group_name="historical_cases",
        key_prefix="bronze",
        required_resource_keys={"gcs_resource"},
        io_manager_key="date_partitioned_pandas_bq_io_manager",
        compute_kind="pandas",
        metadata={
            "partition_expr": "ds",
            "partition_cadence": "day",
            "partition_expiration_days": 30,
        },
        partitions_def=_partitions_def,
    )
    def _org_specific_historical_cases(
        context: OpExecutionContext,
    ) -> pd.DataFrame:
        load_date = _get_load_date_from_context(context)
        context.log.info(f"Fetching data for load_date: {load_date.strftime(DS_FORMAT)}")
        df = GCSHistoricalFileFetcher(
            context.resources.gcs_resource,
            config,
            load_date,
        ).get_dataframe()

        return df.reset_index(drop=True)

    return _org_specific_historical_cases


org_specific_historical_cases_assets: List[tuple[HistoricalCasesConfig, AssetsDefinition]] = [
    (config, org_specific_historical_cases(config)) for config in get_historical_cases_configs()
]


def standardized_historical_cases_by_file(
    config: HistoricalCasesConfig,
    org_specific_historical_case_asset: AssetsDefinition,
) -> AssetsDefinition:
    @apella_asset(
        name=config.table_name + "_standardized",
        ins={
            asset_key.path[1]: AssetIn(key=asset_key)
            for asset_key in org_specific_historical_case_asset.keys_by_output_name.values()
        },
        group_name="historical_cases",
        key_prefix="silver",
        compute_kind="pandas",
        partitions_def=_partitions_def,
        io_manager_key="pickled_io_manager",
    )
    def _standardized_historical_cases(context: OpExecutionContext, **kwargs) -> pd.DataFrame:
        load_date = _get_load_date_from_context(context)
        context.log.info(f"Standardizing data for load_date: {load_date.strftime(DS_FORMAT)}")
        dfs = [
            config.schema_merger(
                asset_name, asset_dataframe, load_date, context.log
            ).get_unified_dataframe()
            for asset_name, asset_dataframe in kwargs.items()
        ]
        return pd.concat(dfs).reset_index(drop=True)

    return _standardized_historical_cases


standardized_historical_cases_file_assets: List[tuple[HistoricalCasesConfig, AssetsDefinition]] = [
    (
        config,
        standardized_historical_cases_by_file(config, asset_def),
    )
    for config, asset_def in org_specific_historical_cases_assets
]


@apella_asset(
    ins={
        asset_key.path[1]: AssetIn(key=asset_key)
        for _, asset_def in standardized_historical_cases_file_assets
        for asset_key in asset_def.keys_by_output_name.values()
    },
    io_manager_key="date_partitioned_pandas_bq_io_manager",
    metadata={
        "partition_expr": "ds",
        "partition_cadence": "day",
        "table_schema": BaseSchemaMerger.get_schema(),
        "partition_expiration_days": 30,
    },
    group_name="historical_cases",
    key_prefix="silver",
    compute_kind="pandas",
    partitions_def=_partitions_def,
)
def historical_cases_merged(context: OpExecutionContext, **kwargs):
    sub_dfs = []
    for asset_name, asset_dataframe in kwargs.items():
        context.log.info(f"Merging {asset_name} for partition {context.partition_key}")
        sub_dfs.append(asset_dataframe)
    return pd.concat(sub_dfs).reset_index(drop=True)


historical_cases_assets = (
    [a[1] for a in org_specific_historical_cases_assets]
    + [a[1] for a in standardized_historical_cases_file_assets]
    + [historical_cases_merged]
)
