import json
import os
from typing import Any

from dagster import <PERSON><PERSON><PERSON><PERSON>, AssetsDefinition, OpExecutionContext, asset
from dagster_dbt import DbtCliResource

from warehouse.utils.assets.helpers import get_day_of_partitions_def


class DBTDataTests:
    def __init__(
        self,
        table_to_asset_definition_mapping: dict[str, AssetsDefinition],
        table_to_asset_mapping: dict[str, Asset<PERSON>ey],
        dbt_manifest_nodes: dict[str, Any],
    ):
        self.table_to_asset_definition_mapping = table_to_asset_definition_mapping
        self.table_to_asset_mapping = table_to_asset_mapping
        self.dbt_manifest_nodes = dbt_manifest_nodes
        self._asset_key_str_to_test_assets_mapping: dict[str, list[AssetsDefinition]] = (
            self._construct_test_assets()
        )

    def get_all_tests(self) -> list[AssetsDefinition]:
        all_test_assets: list[AssetsDefinition] = []
        for test_assets in self._asset_key_str_to_test_assets_mapping.values():
            all_test_assets.extend(test_assets)
        return all_test_assets

    def get_tests_for_asset_key(self, asset_key: Asset<PERSON>ey) -> list[AssetsDefinition]:
        return self._asset_key_str_to_test_assets_mapping.get(asset_key.to_string(), [])

    def _construct_test_assets(
        self,
    ) -> dict[str, list[AssetsDefinition]]:
        asset_key_str_to_test_assets_mapping: dict[str, list[AssetsDefinition]] = {}
        for config in self.dbt_manifest_nodes.values():
            if config["resource_type"] != "test":
                continue
            asset = self._dbt_data_test_factory(config, self.table_to_asset_mapping)
            for dep_name in config["depends_on"]["nodes"]:
                asset_key_str_to_test_assets_mapping.setdefault(
                    self.table_to_asset_mapping[dep_name].to_string(), []
                ).append(asset)
        return asset_key_str_to_test_assets_mapping

    def _is_schema_file_test(self, test_config):
        return "attached_node" in test_config

    def _get_group_name(self, test_config: dict[str, Any]) -> str | None:
        if self._is_schema_file_test(test_config):
            tested_model = test_config["attached_node"]
            attached_model_asset_definiton = self.table_to_asset_definition_mapping[tested_model]
            return attached_model_asset_definiton.group_names_by_key.get(
                self.table_to_asset_mapping[tested_model]
            )
        else:
            path_components = test_config["path"].split(os.path.sep)
            # Dagster expects group names in the regex ^[A-Za-z0-9_]+$, so replace
            # dot with underscore
            return path_components.pop(0)

    def _get_test_name(self, test_config: dict[str, Any]) -> str:
        return (
            test_config["name"]
            if not self._is_schema_file_test(test_config)
            else f"test_{test_config['name']}"
        )

    def _dbt_data_test_factory(
        self,
        test_config: dict[str, Any],
        dbt_asset_key_lookup: dict[str, AssetKey],
    ) -> AssetsDefinition:
        test_name = test_config["name"]

        assert isinstance(
            test_name, str
        ), f'Expected type `string` for `test_config["name"]`. Got: type `{ type(test_config["name"]) }`, `{ test_config["name"] }`'

        @asset(
            name=self._get_test_name(test_config),
            group_name=self._get_group_name(test_config),
            compute_kind="dbt test",
            deps={
                dbt_asset_key_lookup[dep_name] for dep_name in test_config["depends_on"]["nodes"]
            },
            partitions_def=get_day_of_partitions_def(),
        )
        def dbt_data_test(context: OpExecutionContext, dbt: DbtCliResource):
            invocation = dbt.cli(
                args=[
                    "test",
                    "--vars",
                    json.dumps(
                        {
                            "ds": context.partition_key,
                        }
                    ),
                    "--select",
                    test_name,
                ],
            )

            # `stream_raw_events` flushes the DBT CLI process logs to stdout
            test_output = list(invocation.stream_raw_events())

            assert invocation.is_successful(), test_output

        return dbt_data_test
