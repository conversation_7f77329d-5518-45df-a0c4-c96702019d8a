"""
This is a temporary table to address Available Time Slot (commonly known as <PERSON>'s email)
Attribution. This is a temporary solution because we think at some point in the near future
we will have a very different user experience where emails are not going to be used to advertise
time but rather users who want time are going to log into Apella directly (they can't today and
this service doesn't yet exist). This is also short lived because we are planning on adding
available slots to decodable. Once any of these systems is in place, this asset can be safely deleted.

Here is the original ticket describing the problem in more detail:
https://linear.app/apella/issue/DATA-3302/carols-email-attribution

And the design doc in notion
https://www.notion.so/apella/Carol-s-email-attribution-156af54ad37a808f8688f209070dfc6f?pvs=4
"""

from logging import getLogger

import pandas as pd
from apella_cloud_api import Client as ApellaCloudApiClient
from apella_cloud_api.new_api_server_schema import ApellaSchema as ApellaCloudApiSchema
from dagster import ConfigurableResource, OpExecutionContext

from warehouse.utils import apella_asset
from warehouse.utils.assets.helpers import get_date_from_ds_str, get_previous_day_partitions_def
from warehouse.utils.available_time_slots.available_time_slots import get_available_time_slots_df

logger = getLogger(__name__)


bq_schema = {
    "site_id": "string",
    "room_id": "string",
    "start_time": "timestamp",
    "end_time": "timestamp",
    "max_available_duration_minutes": "integer",
    "block_time_ids": ["string"],
}


class ApellaClientResource(ConfigurableResource):
    environment: str

    def get_client(self) -> ApellaCloudApiClient:
        return ApellaCloudApiClient(
            environment=self.environment,
            permissions=[
                "org:read:any",
                "site:read:any",
                "room:read:any",
                "case:read:any",
                "user:read:any",
            ],
        )


def get_site_id_to_timezone(
    apella_client: ApellaCloudApiClient, apella_schema: ApellaCloudApiSchema
) -> dict[str, str]:
    sites_query = apella_schema.Query.sites.args().select(
        apella_schema.SiteConnection.edges.select(
            apella_schema.SiteEdge.node.select(apella_schema.Site.id, apella_schema.Site.timezone)
        )
    )
    results = apella_client.query_graphql_from_schema(sites_query)
    site_id_to_timezone = {edge.node.id: edge.node.timezone for edge in results.sites.edges}
    return site_id_to_timezone


@apella_asset(
    io_manager_key="date_partitioned_pandas_bq_io_manager",
    metadata={
        "partition_expr": "ds",
        "partition_cadence": "day",
        "table_schema": {
            **bq_schema,
            "ds": "date",
        },
    },
    # bronze holds data from external sources
    group_name="available_time_slots",
    key_prefix="silver",
    # cosmetic, for the UI
    compute_kind="pandas",
    partitions_def=get_previous_day_partitions_def(),
)
def available_time_slots_daily(
    context: OpExecutionContext,
    apella_cloud_api_resource: ApellaClientResource,
) -> pd.DataFrame:
    num_days = 28
    min_available_duration = 30
    apella_schema = ApellaCloudApiSchema()
    apella_client = apella_cloud_api_resource.get_client()

    site_id_to_timezone = get_site_id_to_timezone(apella_client, apella_schema)
    # there is no real need to query AvailableTimeSlots per site, other than the fact than
    # every now and then we get a 500 error from the API server due to timeouts.
    # This is a workaround to avoid it
    dfs = []

    for site_id, timezone in site_id_to_timezone.items():
        # TODO once we understand why graphql query fails, and fix the issue, remove the
        #   try/except block
        try:
            temp_df = get_available_time_slots_df(
                apella_client, apella_schema, site_id, timezone, num_days, min_available_duration
            )
            dfs.append(temp_df)
        except Exception as e:
            logger.warning(f"Failed to get available time slots for site {site_id}: {e}")
    df = pd.concat(dfs)
    df["ds"] = get_date_from_ds_str(context.partition_key)

    return df
