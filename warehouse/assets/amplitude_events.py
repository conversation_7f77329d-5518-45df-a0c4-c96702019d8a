import pandas as pd
from dagster import OpExecutionContext

from warehouse.utils import apella_asset
from warehouse.utils.amplitude_events.schema import amplitude_schema
from warehouse.utils.assets.helpers import get_date_from_ds_str, get_previous_day_partitions_def


@apella_asset(
    io_manager_key="date_partitioned_pandas_bq_io_manager",
    metadata={
        "partition_expr": "ds",
        "partition_cadence": "day",
        "table_schema": {
            **amplitude_schema,
            "ds": "date",
        },
    },
    # bronze holds data from external sources
    group_name="amplitude",
    key_prefix="bronze",
    # cosmetic, for the UI
    compute_kind="pandas",
    partitions_def=get_previous_day_partitions_def(),
    required_resource_keys={"amplitude_resource"},
)
def amplitude_events_export_daily(context: OpExecutionContext):
    events = context.resources.amplitude_resource.fetch_events(context.partition_key)
    rows = [event.to_dict() for event in events]

    context.log.info(f"Fetched {len(rows)} events for {context.partition_key}")

    df = pd.DataFrame(rows)
    df["ds"] = get_date_from_ds_str(context.partition_key)

    return df
