from datetime import datetime

from dagster import OpExecutionContext, TimeWindowPartitionsDefinition
from sqlalchemy.sql import text

from warehouse.utils import apella_asset

from ..resources.api_db_resource import ApiDbResource

_EXPECTED_SCHEDULED_AND_ACTUAL_CASES = """
        with scheduled_cases as (
            select
                cases.org_id,
                cases.site_id as case_site_id,
                cases.room_id as case_room_id,
                sites.timezone as case_timezone,
                cases.case_id,
                cases.scheduled_start_time,
                cases.scheduled_end_time,
                cases.status,
                cases.case_classification_types_id,
                cases.service_line_id,
                cases.is_add_on,
                cases.patient_class,
                cases.cancellation_reason,
                cases.external_case_id,
                cases.updated_time
            from
                "postgres"."public"."cases" as cases
            inner join "postgres"."public"."sites" as sites
                on cases.site_id = sites.id
        ),

        phases_with_event_times as (
            select
                cast(p0.id as text) as id,
                p0.org_id,
                p0.site_id,
                p0.room_id,
                p0.start_event_id,
                p0.end_event_id,
                p0.case_id,
                p0.type_id,
                cast(p0.event_matching_status as text) as event_matching_status,
                greatest(p0.updated_time, s.updated_time, e.updated_time) as updated_time,
                sites.timezone as phase_timezone,
                s.start_time as predicted_start_time,
                e.start_time as predicted_end_time
            from
                "postgres"."public"."phases" as p0
            inner join "postgres"."public"."events" as s on p0.start_event_id = s.id
            inner join "postgres"."public"."sites" as sites on p0.site_id = sites.id
            left join "postgres"."public"."events" as e on p0.end_event_id = e.id
            where
                p0.source_type = 'unified'
                and p0.status = 'VALID'
                and p0.type_id = 'CASE'
        ),

        expected_scheduled_and_actual_cases as (
            select
                coalesce(sc.case_id, '') as case_id,
                coalesce(p1.id, '') as phase_id,
                p1.site_id as phase_site_id,
                p1.room_id as phase_room_id,
                p1.phase_timezone,
                p1.start_event_id,
                p1.end_event_id,
                p1.event_matching_status,
                p1.predicted_start_time as actual_start_time,
                p1.predicted_end_time as actual_end_time,
                p1.updated_time as actual_updated_time,
                sc.updated_time as scheduled_updated_time,
                sc.case_site_id,
                sc.case_room_id,
                sc.case_timezone,
                sc.scheduled_start_time,
                sc.scheduled_end_time,
                sc.status as scheduled_case_status,
                sc.case_classification_types_id,
                sc.service_line_id,
                sc.patient_class,
                sc.cancellation_reason,
                sc.external_case_id,
                case
                    when p1.predicted_end_time is not null then 'COMPLETE'
                    when p1.predicted_start_time is not null then 'LIVE'
                    else 'FORECAST'
                end as case_type,
                coalesce(p1.org_id, sc.org_id, '') as org_id,
                coalesce(p1.site_id, sc.case_site_id, '') as site_id,
                coalesce(p1.room_id, sc.case_room_id, '') as room_id,
                coalesce(sc.is_add_on, false) as is_add_on,
                coalesce(
                    p1.predicted_start_time,
                    sc.scheduled_start_time,
                    now()
                ) as start_time,
                case
                    when p1.predicted_start_time is null then sc.scheduled_end_time
                    else p1.predicted_end_time
                end as end_time,
                coalesce(
                    
            least(coalesce(p1.predicted_start_time, sc.scheduled_start_time), coalesce(sc.scheduled_start_time, p1.predicted_start_time))
        ,
                    now()
                ) as min_start_time,
                case
                    when p1.predicted_end_time is not null then 
            greatest(coalesce(sc.scheduled_end_time, p1.predicted_end_time), coalesce(p1.predicted_end_time, sc.scheduled_end_time))

                    when p1.predicted_start_time is not null then null
                    else sc.scheduled_end_time
                end as max_end_time
            from scheduled_cases as sc
            full outer join phases_with_event_times as p1 on sc.case_id = p1.case_id
        )
    """


def _partitions_def() -> TimeWindowPartitionsDefinition:
    return TimeWindowPartitionsDefinition(
        start=datetime(2025, 4, 29),
        fmt="%Y-%m-%d %H:%M:%S",
        cron_schedule="0 * * * *",
    )


def _run_query(
    context: OpExecutionContext,
    sql: str,
    api_db_resource: ApiDbResource,
) -> None:
    with api_db_resource.get_session() as pg_session:
        context.log.info(f"Executing data test. SQL: {sql}")

        result = [row for row in pg_session.execute(text(sql))]

        if result:
            context.log.info(f"Decodable validation failed: {result}")
            raise ValueError("Decodable validation failed.")
        else:
            context.log.info("Decodable validation passed.")


@apella_asset(
    name="cases_activity_has_all_obx_events",
    group_name="decodable",
    compute_kind="postgres",
    output_required=False,
    partitions_def=_partitions_def(),
)
def case_activity_has_all_obx_events(
    context: OpExecutionContext,
    api_db_resource: ApiDbResource,
) -> None:
    _, partition_window_end = context.partition_time_window
    sql = f"""
        with decodable_obx as (
            select
                id as event_id,
                case_id,
                org_id,
                updated_time,
                created_time,
                event_type_id,
                start_time,
                process_timestamp,
                site_id,
                room_id,
                source_type,
                source,
                deleted_time
            from postgres.decodable.case_activity
            where
                source_type = 'ehr'
                and start_time >= '{ partition_window_end.isoformat() }'::timestamptz - interval '14 days'
                and updated_time <= '{ partition_window_end.isoformat() }'::timestamptz - interval '1 minute'
        ),
        api_obx as (
            select
                cast(a.id as text) as event_id,
                cast(a.case_id as text) as case_id,
                a.org_id as org_id,
                a.updated_time as updated_time,
                a.created_time as created_time,
                a.type_id as event_type_id,
                a.observation_time as start_time,
                a.recorded_time as process_timestamp,
                b.site_id as site_id,
                coalesce(b.room_id, '') as room_id,
                cast('ehr' as text) as source_type,
                cast('ehr' as text) source,
                cast(null as timestamp) as deleted_time
            from postgres.public.observations a
            left join postgres.public.cases b
            on a.case_id = b.case_id
            where
                a.case_id is not null
                and a.type_id in (
                    'OBSERVED_IN_PRE_PROCEDURE',
                    'OBSERVED_PRE_PROCEDURE_COMPLETE'
                )
                and a.observation_time >= '{ partition_window_end.isoformat() }'::timestamptz - interval '14 days'
                and a.updated_time <= '{ partition_window_end.isoformat() }'::timestamptz - interval '1 minute'
        ), api_not_decodable as (
            select *
            from api_obx
            except distinct
            select *
            from decodable_obx
        ), decodable_not_api as (
            select *
            from decodable_obx
            except distinct
            select *
            from api_obx
        )

        select * from api_not_decodable
        union all
        select * from decodable_not_api

        limit 5
    """
    _run_query(context=context, sql=sql, api_db_resource=api_db_resource)


@apella_asset(
    name="scheduled_and_actual_cases_sink_has_all_expected_rows",
    group_name="decodable",
    compute_kind="postgres",
    output_required=False,
    partitions_def=_partitions_def(),
)
def scheduled_and_actual_cases_sink_has_all_expected_rows(
    context: OpExecutionContext,
    api_db_resource: ApiDbResource,
) -> None:
    _, partition_window_end = context.partition_time_window
    sql = f"""
        {_EXPECTED_SCHEDULED_AND_ACTUAL_CASES}
        select
            org_id,
            site_id,
            room_id,
            case_type,
            case_id,
            phase_id,
            start_time,
            end_time,
            start_event_id,
            end_event_id,
            actual_start_time,
            actual_end_time,
            scheduled_start_time,
            scheduled_end_time,
            event_matching_status,
            min_start_time,
            max_end_time,
            'expected_but_not_in_sink' as reason
        from expected_scheduled_and_actual_cases
            where (scheduled_updated_time <= '{ partition_window_end.isoformat() }'::timestamptz - interval '1 minute' or scheduled_updated_time is null)
                and (actual_updated_time <= '{ partition_window_end.isoformat() }'::timestamptz - interval '1 minute' or actual_updated_time is null)
        except distinct
        select
            org_id,
            site_id,
            room_id,
            case_type,
            case_id,
            phase_id,
            start_time,
            end_time,
            start_event_id,
            end_event_id,
            actual_start_time,
            actual_end_time,
            scheduled_start_time,
            scheduled_end_time,
            event_matching_status,
            min_start_time,
            max_end_time,
            'expected_but_not_in_sink' as reason
        from "postgres"."decodable"."scheduled_and_actual_cases_v2"

        limit 5
    """

    _run_query(context=context, sql=sql, api_db_resource=api_db_resource)


@apella_asset(
    name="scheduled_and_actual_cases_sink_has_no_unexpected_rows",
    group_name="decodable",
    compute_kind="postgres",
    output_required=False,
    partitions_def=_partitions_def(),
)
def scheduled_and_actual_cases_sink_has_no_unexpected_rows(
    context: OpExecutionContext,
    api_db_resource: ApiDbResource,
) -> None:
    _, partition_window_end = context.partition_time_window
    sql = f"""
        {_EXPECTED_SCHEDULED_AND_ACTUAL_CASES},
        diff as (
                select
                org_id,
                site_id,
                room_id,
                case_type,
                case_id,
                phase_id,
                start_time,
                end_time,
                start_event_id,
                end_event_id,
                actual_start_time,
                actual_end_time,
                scheduled_start_time,
                scheduled_end_time,
                event_matching_status,
                min_start_time,
                max_end_time,
                'in_sink_but_not_expected' as reason
            from "postgres"."decodable"."scheduled_and_actual_cases_v2"
            except distinct
            select
                org_id,
                site_id,
                room_id,
                case_type,
                case_id,
                phase_id,
                start_time,
                end_time,
                start_event_id,
                end_event_id,
                actual_start_time,
                actual_end_time,
                scheduled_start_time,
                scheduled_end_time,
                event_matching_status,
                min_start_time,
                max_end_time,
                'in_sink_but_not_expected' as reason
            from expected_scheduled_and_actual_cases)

        -- Confirm that different rows aren't due to more recent changes in postgres that haven't been propagated
        select diff.*
        from diff
        left join expected_scheduled_and_actual_cases as expected
        on expected.case_id = diff.case_id or
            expected.phase_id = diff.phase_id
        where (expected.scheduled_updated_time <= '{ partition_window_end.isoformat() }'::timestamptz - interval '1 minute' or expected.scheduled_updated_time is null)
            and (expected.actual_updated_time <= '{ partition_window_end.isoformat() }'::timestamptz - interval '1 minute' or expected.actual_updated_time is null)

        limit 5
    """

    _run_query(context=context, sql=sql, api_db_resource=api_db_resource)
