import pandas as pd
from dagster import OpExecutionContext

from warehouse.utils import apella_asset
from warehouse.utils.assets.helpers import get_date_from_ds_str, get_previous_day_partitions_def
from warehouse.utils.twilio_notifications.schema import twilio_schema


@apella_asset(
    io_manager_key="date_partitioned_pandas_bq_io_manager",
    metadata={
        "partition_expr": "ds",
        "partition_cadence": "day",
        "table_schema": {
            **twilio_schema,
            "ds": "date",
        },
    },
    # bronze holds data from external sources
    group_name="twilio",
    key_prefix="bronze",
    # cosmetic, for the UI
    compute_kind="pandas",
    partitions_def=get_previous_day_partitions_def(timezone="UTC"),
    required_resource_keys={"twilio_resource"},
)
def twilio_notifications_daily(context: OpExecutionContext):
    rows = [
        {key: getattr(message, key) for key in twilio_schema.keys()}
        for message in context.resources.twilio_resource.get_notifications(context.partition_key)
    ]

    df = pd.DataFrame(rows)
    df["ds"] = get_date_from_ds_str(context.partition_key)

    return df
