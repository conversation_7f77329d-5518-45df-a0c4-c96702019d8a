from warehouse.assets.dbt_data_tests import DBTDataTests
from warehouse.assets.latest_table_factory import LatestTableFactory
from warehouse.utils.assets.helpers import get_day_of_partitions_def
from warehouse.utils.dbt.helpers import (
    DBTScheduleCadences,
    DBTTranslator,
    dbt_asset_factory,
    get_dbt_manifest,
)

_partitions_def = get_day_of_partitions_def()
_dbt_manifest = get_dbt_manifest()

nodes = _dbt_manifest["nodes"]
sources = _dbt_manifest["sources"]

dbt_models_lu = {
    model_config["unique_id"]: dbt_asset_factory(model_config["name"], _partitions_def)
    for model_config in nodes.values()
    if (
        DBTScheduleCadences.DAILY in model_config["tags"]
        and model_config["resource_type"] == "model"
    )
}

dbt_models = list(dbt_models_lu.values())

dbt_asset_key_lookup = {
    n: DBTTranslator.get_asset_key(props) for n, props in (nodes | sources).items()
}

dbt_test_helper = DBTDataTests(dbt_models_lu, dbt_asset_key_lookup, nodes)

dbt_data_tests = dbt_test_helper.get_all_tests()

dbt_latest_models = [
    LatestTableFactory.get_latest_asset(
        model_config,
        _partitions_def,
        dbt_test_helper.get_tests_for_asset_key(dbt_asset_key_lookup[model_config["unique_id"]]),
    )
    for model_config in nodes.values()
    if (model_config["config"]["meta"].get("produce_latest_snapshot", False))
]
