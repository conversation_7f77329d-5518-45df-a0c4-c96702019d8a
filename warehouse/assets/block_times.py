from datetime import datetime, timed<PERSON>ta

from dagster import (
    DefaultSensorStatus,
    MultiPartitionKey,
    OpExecutionContext,
    RunRequest,
    SensorDefinition,
    SensorEvaluationContext,
    SensorResult,
    sensor,
)
from dagster._core.definitions.unresolved_asset_job_definition import UnresolvedAssetJobDefinition
from dagster_gcp.bigquery.resources import BigQueryResource
from zoneinfo import ZoneInfo

from warehouse.assets.org_id_partition_sensor import org_date_partition, org_partitions
from warehouse.configs.warehouse_config import DS_FORMAT
from warehouse.utils import apella_asset
from warehouse.utils.blocks.helpers import get_org_date_pairs_query_str

UTC_TIMEZONE = "UTC"
PACIFIC_TIMEZONE = "America/Los_Angeles"


@apella_asset(
    name="block_times",
    description="Scheduled block times from the block schedule file rows",
    group_name="blocks",
    partitions_def=org_date_partition,
)
def block_times(context: OpExecutionContext):
    """
    Retrieve scheduled block times for a specific org and date.
    This asset is partitioned by org_id and date.
    """

    partition_key = context.partition_key
    partition_parts = partition_key.split("|")
    org_id = partition_parts[0]
    partition_date = partition_parts[1]
    context.log.info(
        f"Processing scheduled block times for org_id: {org_id}, date: {partition_date}"
    )


def block_changes_sensor_factory(
    job: UnresolvedAssetJobDefinition,
    sensor_name: str,
    minimum_interval_seconds: int = 3600,
) -> SensorDefinition:
    @sensor(
        minimum_interval_seconds=minimum_interval_seconds,
        job=job,
        name=sensor_name,
        default_status=DefaultSensorStatus.RUNNING,
    )
    def block_schedule_block_releases_for_org_day_sensor(
        context: SensorEvaluationContext, bigquery_resource: BigQueryResource
    ) -> SensorResult:
        sensor_current_timestamp = datetime.now(tz=ZoneInfo(PACIFIC_TIMEZONE))
        sliding_window = timedelta(minutes=30)

        if context.cursor is None or context.cursor == "":
            context.log.warning("Could not find a cursor value. Setting to now.")
            sensor_last_timestamp = sensor_current_timestamp
        else:
            sensor_last_timestamp = datetime.fromisoformat(context.cursor)

        changes_start_time = sensor_last_timestamp - sliding_window
        changes_end_time = sensor_current_timestamp + sliding_window

        context.log.info(
            f"Using a sliding window to look for changes between {changes_start_time} and {changes_end_time}"
        )

        with bigquery_resource.get_client() as client:
            df = client.query(
                query=get_org_date_pairs_query_str(
                    start_time=changes_start_time, end_time=changes_end_time
                )
            ).to_dataframe()

        if df.empty:
            return SensorResult(
                skip_reason="No new changes found in block schedule or block releases",
                cursor=str(changes_end_time.isoformat()),
            )

        updated_org_dates = [
            (
                row.org_id,
                row.date.replace(tzinfo=ZoneInfo(UTC_TIMEZONE))
                .astimezone(ZoneInfo(PACIFIC_TIMEZONE))
                .strftime(DS_FORMAT),
            )
            for row in df.itertuples(index=False)
        ]

        context.log.info(
            f"Found the following org-date pairs to process ({len(updated_org_dates)}): {updated_org_dates}"
        )

        valid_org_ids = set(
            org_partitions.get_partition_keys(dynamic_partitions_store=context.instance)
        )

        context.log.info(f"Found {len(valid_org_ids)} valid org ids: {valid_org_ids}")

        org_date_pairs_for_request = [  # only keep known orgs
            (org_id, date) for org_id, date in updated_org_dates if org_id in valid_org_ids
        ]

        run_requests = []

        context.log.info(
            f"Found the following valid org-date pairs to process ({len(org_date_pairs_for_request)}): {org_date_pairs_for_request}"
        )

        for org_id, date in org_date_pairs_for_request:
            if org_id not in valid_org_ids:
                context.log.warning(f"Skipping org_id: {org_id} because it is not a valid org id")
                continue
            context.log.info(f"Using formatted date: {date} for org_id: {org_id}")
            run_requests.append(
                RunRequest(
                    job_name="process_block_times",
                    run_key=f"block_times_{org_id}_{date}",
                    partition_key=MultiPartitionKey({"org_id": org_id, "date": date}),
                    tags={"org_id": org_id, "date_received": date},
                )
            )

        context.log.info(f"Created {len(run_requests)} requests")

        return SensorResult(
            run_requests=run_requests,
            cursor=str(changes_end_time.isoformat()),
        )

    return block_schedule_block_releases_for_org_day_sensor
