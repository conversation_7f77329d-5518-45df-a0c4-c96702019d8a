import re
from datetime import datetime
from functools import lru_cache
from logging import Logger

import pandas as pd
from dagster import (
    AssetsDefinition,
    OpExecutionContext,
    SourceAsset,
    TimeWindow,
    TimeWindowPartitionsDefinition,
    asset,
)
from dagster_gcp.bigquery.resources import BigQueryResource
from google.api_core.exceptions import NotFound
from google.cloud.bigquery.client import Client as BigQueryClient
from sqlalchemy.orm import Session
from sqlalchemy.sql import text

from ..resources.api_db_resource import ApiDbResource

DATASTREAM_GRACE_PERIOD = pd.Timedelta(hours=1)


class DataStreamCheckFactory:
    @staticmethod
    def get_datastream_check_assets(
        source_assets: list[SourceAsset],
    ) -> list[AssetsDefinition]:
        datastream_check_assets = [
            DataStreamCheckFactory.get_datastream_check_asset(asset)
            for asset in source_assets
            if asset.group_name == "api_postgres"
        ]
        return datastream_check_assets

    @staticmethod
    def get_datastream_check_asset(
        source_asset: SourceAsset,
    ) -> AssetsDefinition:
        _, source_asset_bq_table_name = source_asset.key.path
        model_name = f"check__{source_asset_bq_table_name}"

        @asset(
            name=model_name,
            group_name="datastream_checks",
            compute_kind="bigquery",
            deps=[source_asset],
            description=f"DataStream check for {source_asset_bq_table_name}",
            output_required=False,
            partitions_def=TimeWindowPartitionsDefinition(
                start=datetime(2025, 1, 1),
                fmt="%Y-%m-%d %H:%M:%S",
                cron_schedule="0 * * * *",
            ),
        )
        def datastream_compare(
            context: OpExecutionContext,
            bigquery_resource: BigQueryResource,
            api_db_resource: ApiDbResource,
        ) -> None:
            with api_db_resource.get_session() as pg_session, bigquery_resource.get_client() as bq_client:
                checker = DataStreamChecker(
                    source_asset, context.partition_time_window, bq_client, pg_session, context.log
                )

                context.log.info(f"Checking {source_asset_bq_table_name}...")

                if checker.postgres_never_populated():
                    context.log.warning(
                        f"Table {source_asset_bq_table_name} has never been populated in Postgres. Skipping Datastream check."
                    )
                    return None
                elif not checker.check_updated_time_column_exists():
                    context.log.warning(
                        f"Table {source_asset_bq_table_name} does not have an updated_time column. Skipping Datastream check."
                    )
                    return None

                missing_ids = checker.find_missing_postgres_records()
                if len(missing_ids) > 0:
                    context.log.error(
                        f"Data mismatch between BigQuery and Postgres for {source_asset_bq_table_name}. Found {len(missing_ids)} ids from Postgres that are not in BigQuery"
                    )
                    context.log.info("Missing ids. Example:")
                    DataStreamCheckFactory._pretty_print_df(
                        missing_ids.head(5), context.log, indent=2
                    )

                    raise ValueError("Ids mismatch between BigQuery and Postgres")
                else:
                    context.log.info(
                        f"Datastream ids check passed for {source_asset_bq_table_name}"
                    )

                if not checker.compare_counts():
                    raise ValueError(
                        "Postgres and BigQuery have drifted in counts by more than the allowed threshold"
                    )
                else:
                    context.log.info(
                        f"Datastream count check passed for {source_asset_bq_table_name}"
                    )

        return datastream_compare

    @staticmethod
    def _pretty_print_df(log_df: pd.DataFrame, logger: Logger, indent=0):
        df_string = log_df.to_string(index=False)

        indent_str = " " * indent
        for line in df_string.split("\n"):
            logger.info(f"{indent_str}{line}")


class DataStreamChecker:
    COUNT_PCT_DIFF_THRESHOLD = 0.01

    def __init__(
        self,
        source_asset: SourceAsset,
        partition_time_window: TimeWindow,
        bigquery_client: BigQueryClient,
        postgres_session: Session,
        logger: Logger,
    ):
        source_asset_bq_schema_name, source_asset_bq_table_name = source_asset.key.path
        self.bq_table_name = source_asset_bq_table_name
        self.bq_table_id = f"{source_asset_bq_schema_name}.{source_asset_bq_table_name}"
        self.psql_schema_name, self.psql_table_name = DataStreamChecker._split_asset_name(
            source_asset_bq_table_name
        )
        self.psql_table_name_with_schema = f"{self.psql_schema_name}.{self.psql_table_name}"

        self.check_start, self.check_end = self.get_query_window(partition_time_window)

        self.bigquery_client = bigquery_client
        self.postgres_session = postgres_session

        self.pk_column_names = self._find_pk_columns()
        self.logger = logger

    @staticmethod
    def get_query_window(partition_time_window: TimeWindow, grace_period=DATASTREAM_GRACE_PERIOD):
        # [T, T + 1] comes in at T + 1 given offset = 0 (default in partitions def)
        start, end = partition_time_window
        # shift back the start and end by the grace period to account for datastream delays
        return (start - grace_period, end - grace_period)

    def query_postgres_ids(self) -> pd.DataFrame:
        sql = self._generate_query(self.psql_table_name_with_schema)
        self.logger.info(f"Running Postgres query: \n{sql}")
        return pd.read_sql(sql, self.postgres_session.connection())

    def query_bigquery_ids(self) -> pd.DataFrame:
        bq_sql = self._generate_query(f"`{self.bq_table_id}`")
        self.logger.info(f"Running BigQuery query: \n{bq_sql}")
        return self.bigquery_client.query(bq_sql).to_dataframe()

    def _generate_query(self, table_name: str) -> str:
        return f"""
            select {",".join(self.pk_column_names)}
            from {table_name}
            where updated_time >= '{self.check_start.isoformat()}'
                and updated_time < '{self.check_end.isoformat()}'
        """

    @staticmethod
    def _compare_pks(
        psql_df: pd.DataFrame, bq_df: pd.DataFrame, pk_col_names: list[str]
    ) -> pd.DataFrame:
        # Create a copy and convert all the columns to strings, because sometimes UUIDs are stored
        # as UUIDs in Postgres and as strings in BigQuery
        psql_df = psql_df.copy().astype(str)
        bq_df = bq_df.copy().astype(str)

        # Merge the dataframes on the id columns
        merged = pd.merge(psql_df, bq_df, on=pk_col_names, how="left", indicator=True)
        # Find records that don't exist in bq_df
        missing_records = merged[merged["_merge"] == "left_only"]
        return missing_records[pk_col_names]

    def find_missing_postgres_records(self) -> pd.DataFrame:
        psql_df = self.query_postgres_ids()
        bq_df = self.query_bigquery_ids()
        return self._compare_pks(psql_df, bq_df, self.pk_column_names)

    @staticmethod
    def _split_asset_name(asset_name: str) -> tuple[str, str]:
        # Breaks the asset name into schema and table name. Example asset name: "public_my_table"
        pattern = r"^([^\._]+)_(.+)$"
        match = re.match(pattern, asset_name)
        if not match:
            raise ValueError(f"Invalid asset name: {asset_name}")
        schema_name = match.group(1)
        table_name = match.group(2)
        return schema_name, table_name

    def _find_pk_columns(self) -> list[str]:
        sql = f"""
                select a.attname
                from pg_index i
                join pg_attribute a on a.attrelid = i.indrelid
                    and a.attnum = any(i.indkey)
                where i.indrelid = '{self.psql_table_name_with_schema}'::regclass
                    and i.indisprimary
        """
        result = self.postgres_session.execute(text(sql))
        return [row[0] for row in result]

    def check_updated_time_column_exists(self) -> bool:
        sql = f"""
            select exists (
                select 1
                from information_schema.columns
                where table_schema = '{self.psql_schema_name}'
                and table_name = '{self.psql_table_name}'
                and column_name = 'updated_time'
            )
        """
        result = self.postgres_session.execute(text(sql))
        return next(result)[0]

    @lru_cache(maxsize=1)
    def _count_postgres_records(self) -> int:
        sql = f"""
            select count(*)
            from {self.psql_table_name_with_schema}
        """
        result = self.postgres_session.execute(text(sql))
        return next(result)[0]

    def _count_bigquery_records(self) -> int:
        sql = f"""
            select count(*)
            from `{self.bq_table_id}`
        """
        result = self.bigquery_client.query(sql).result()
        return next(result)[0]

    def compare_counts(self) -> bool:
        # we expect the difference in counts to be within 0.01% from the Postgres table. BigQuery may take time to catch up
        psql_count = self._count_postgres_records()
        bq_count = self._count_bigquery_records()
        pct_diff = 100 * (psql_count - bq_count) / psql_count

        # log the BQ table name to be consistent with the rest of the job & logging
        self.logger.info(
            f"{self.bq_table_name} has a {pct_diff:.4f}% drift in row count between Postgres and BigQuery: {psql_count} vs {bq_count}"
        )

        return abs(pct_diff) <= self.COUNT_PCT_DIFF_THRESHOLD

    def postgres_never_populated(self) -> bool:
        # if the postgres table is empty and the bigquery table does not exist, then it was never populated.
        # datastream lazy creates the corresponding bigquery tables when there is a write to the corresponding postgres table
        try:
            self.bigquery_client.get_table(self.bq_table_id)
            bq_exists = True
        except NotFound:
            bq_exists = False
        return self._count_postgres_records() == 0 and not bq_exists
