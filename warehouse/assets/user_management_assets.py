import pandas as pd
from dagster import AssetIn, OpExecutionContext

from warehouse.utils import apella_asset
from warehouse.utils.assets.helpers import get_date_from_ds_str, get_day_of_partitions_def
from warehouse.utils.user_management.schema import (
    auth0_org_roles_schema,
    auth0_orgs_schema,
    auth0_user_schema,
)


@apella_asset(
    io_manager_key="date_partitioned_pandas_bq_io_manager",
    metadata={
        "partition_expr": "ds",
        "partition_cadence": "day",
        "table_schema": {
            **auth0_user_schema,
            "ds": "date",
        },
    },
    # bronze holds data from external sources
    group_name="user_management",
    key_prefix="bronze",
    # cosmetic, for the UI
    compute_kind="pandas",
    partitions_def=get_day_of_partitions_def(),
    required_resource_keys={"auth0_resource"},
)
def auth0_users(context: OpExecutionContext):
    rows = context.resources.auth0_resource.get_user_info()
    df = pd.DataFrame(rows)
    # Groups are either a list or a string.
    df["groups"] = df["groups"].apply(lambda x: [x] if isinstance(x, str) else x)
    # Needed for correct BigQuery conversion.
    df["created_at_utc"] = pd.to_datetime(df["created_at_utc"])
    df["last_login_utc"] = pd.to_datetime(df["last_login_utc"])
    df["ds"] = get_date_from_ds_str(context.partition_key)
    return df


@apella_asset(
    io_manager_key="date_partitioned_pandas_bq_io_manager",
    metadata={
        "partition_expr": "ds",
        "partition_cadence": "day",
        "table_schema": {
            **auth0_orgs_schema,
            "ds": "date",
        },
    },
    # bronze holds data from external sources
    group_name="user_management",
    key_prefix="bronze",
    # cosmetic, for the UI
    compute_kind="pandas",
    partitions_def=get_day_of_partitions_def(),
    required_resource_keys={"auth0_resource"},
)
def auth0_orgs(context: OpExecutionContext):
    rows = context.resources.auth0_resource.get_orgs()
    df = pd.DataFrame(rows)
    df["ds"] = get_date_from_ds_str(context.partition_key)
    return df


@apella_asset(
    io_manager_key="date_partitioned_pandas_bq_io_manager",
    metadata={
        "partition_expr": "ds",
        "partition_cadence": "day",
        "table_schema": {
            **auth0_org_roles_schema,
            "ds": "date",
        },
    },
    # bronze holds data from external sources
    group_name="user_management",
    key_prefix="bronze",
    ins={"auth0_orgs": AssetIn(key=["bronze", "auth0_orgs"])},
    # cosmetic, for the UI
    compute_kind="pandas",
    partitions_def=get_day_of_partitions_def(),
    required_resource_keys={"auth0_resource"},
)
def auth0_org_to_user_roles(context: OpExecutionContext, auth0_orgs: pd.DataFrame):
    rows = []
    for _, org in auth0_orgs.iterrows():
        rows += context.resources.auth0_resource.get_user_roles(org["org_id"])
    df = pd.DataFrame(rows)
    df["ds"] = get_date_from_ds_str(context.partition_key)
    return df
