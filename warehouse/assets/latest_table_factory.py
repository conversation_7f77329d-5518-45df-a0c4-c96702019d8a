from typing import Any

from dagster import (
    AssetObservation,
    AssetsDefinition,
    MaterializeResult,
    OpExecutionContext,
    PartitionsDefinition,
)
from dagster_dbt import default_metadata_from_dbt_resource_props
from dagster_gcp.bigquery.resources import BigQueryResource
from google.api_core.exceptions import BadRequest
from google.cloud import bigquery
from google.cloud.bigquery import Client as BigQueryClient

from warehouse.utils import apella_asset
from warehouse.utils.assets.helpers import get_date_from_ds_str

_OLDER_PARTITION_ERROR = "Existing table data is newer than the provided partition key."


class LatestTableFactory:
    @staticmethod
    def get_latest_asset(
        source_model_props: dict[str, Any],
        partitions_def: PartitionsDefinition,
        blocking_tests: list[AssetsDefinition],
    ) -> AssetsDefinition:
        source_model_name = source_model_props["name"]
        source_model_schema_name = source_model_props["schema"]
        assert (
            len(blocking_tests) > 0
        ), f"Latest table factory requires data tests but none were found for {source_model_name}."

        model_name = source_model_name + "_latest"

        @apella_asset(
            name=model_name,
            group_name="latest",
            key_prefix="gold",
            compute_kind="bigquery",
            deps={a for a in blocking_tests},
            description=f"Contains the latest validated data from {source_model_schema_name}.{source_model_name}.\n\n{source_model_props['description']}",
            metadata=default_metadata_from_dbt_resource_props(source_model_props),
            partitions_def=partitions_def,
            output_required=False,
        )
        def latest_table(context: OpExecutionContext, bigquery_resource: BigQueryResource):
            with bigquery_resource.get_client() as client:
                src_bq_id = f"{source_model_schema_name}.{source_model_name}"
                dest_bq_id = f"{context.asset_key.path[0]}.{model_name}"
                create_table_if_needed_sql = f"""
                        create table if not exists `{dest_bq_id}`
                        like `{src_bq_id}`
                        options (partition_expiration_days = null);
                """
                client.query(query=create_table_if_needed_sql).result()

                src_table_schema = client.get_table(src_bq_id).schema
                src_table_col_names = [col.name for col in src_table_schema]
                LatestTableFactory._synchronize_schemas(
                    context, client, src_table_schema, dest_bq_id
                )

                ds_str = str(get_date_from_ds_str(context.partition_key))

                update_sql = f"""
                    declare max_ds date;
                    begin transaction;
                    set max_ds = (select max(ds) from `{dest_bq_id}`);
                    if (max_ds is not null and '{ds_str}' < max_ds) then
                        select error(
                            "{_OLDER_PARTITION_ERROR} Found ds=" || max_ds ||
                            " in {dest_bq_id}. Requested ds={ds_str}."

                        );
                    else
                        truncate table `{dest_bq_id}`;

                        insert into `{dest_bq_id}` ({", ".join(src_table_col_names)})
                        select {", ".join(src_table_col_names)}
                        from `{src_bq_id}`
                        where ds = '{ds_str}';
                    end if;
                    commit transaction;
                """
                try:
                    client.query(query=update_sql).result()
                    # Only materialize partition if this is newer data.
                    yield MaterializeResult()
                except BadRequest as e:
                    if _OLDER_PARTITION_ERROR in str(e):
                        context.log_event(
                            AssetObservation(
                                asset_key=context.asset_key,
                                partition=context.partition_key,
                                metadata={"skip_reason": str(e)},
                            )
                        )
                    else:
                        context.log.error("Failed to update latest table")
                        raise

        return latest_table

    @staticmethod
    def _synchronize_schemas(
        context: OpExecutionContext,
        client: BigQueryClient,
        src_table_schema: list[bigquery.SchemaField],
        dest_bq_id: str,
    ):
        dest_table = client.get_table(dest_bq_id)
        dest_table_col_names = set([col.name for col in dest_table.schema])
        src_table_col_names = set([col.name for col in src_table_schema])
        cols_in_dest_not_in_src = dest_table_col_names - src_table_col_names
        if len(cols_in_dest_not_in_src) > 0:
            raise ValueError(
                f"Columns {cols_in_dest_not_in_src} are in latest table but not in the source table. Automatically deleting or renaming columns is not currently supported. See more info here: https://github.com/Apella-Technology/data-warehouse/tree/main/dbt#latest-snapshots"
            )

        if len(src_table_col_names - dest_table_col_names) > 0:
            context.log.info(
                f"New columns detected. Updating from {dest_table.schema} to {src_table_schema}."
            )
            dest_table.schema = src_table_schema
            dest_table = client.update_table(dest_table, ["schema"])
        else:
            context.log.info("No change in schemas detected. Not updating schema.")
