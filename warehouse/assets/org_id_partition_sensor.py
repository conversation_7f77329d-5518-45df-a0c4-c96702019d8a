from apella_cloud_api import Client as ApellaCloudApiClient
from apella_cloud_api.new_api_server_schema import ApellaSchema as ApellaCloudApiSchema
from dagster import (
    ConfigurableResource,
    DefaultSensorStatus,
    DynamicPartitionsDefinition,
    MultiPartitionsDefinition,
    SensorEvaluationContext,
    SensorResult,
    sensor,
)

from warehouse.utils.assets.helpers import get_day_of_partitions_def

org_partitions = DynamicPartitionsDefinition(name="org_partitions")
date_partitions = get_day_of_partitions_def()

org_date_partition = MultiPartitionsDefinition(
    {
        "org_id": org_partitions,
        "date": date_partitions,
    }
)


class ApellaClientResource(ConfigurableResource):
    environment: str

    def get_client(self) -> ApellaCloudApiClient:
        return ApellaCloudApiClient(
            environment=self.environment,
            permissions=[
                "org:read:any",
                "site:read:any",
                "room:read:any",
            ],
        )


# query org ids from cloud api server
def query_orgs(
    apella_client: ApellaCloudApiClient, apella_schema: ApellaCloudApiSchema
) -> list[str]:
    orgs_query = apella_schema.Query.organizations.args().select(
        apella_schema.OrganizationConnection.edges.select(
            apella_schema.OrganizationEdge.node.select(apella_schema.Organization.id)
        )
    )
    results = apella_client.query_graphql_from_schema(orgs_query)
    return list(set([edge.node.id for edge in results.organizations.edges]))


@sensor(
    minimum_interval_seconds=3600,
    default_status=DefaultSensorStatus.RUNNING,
)
def org_id_partition_sensor(
    context: SensorEvaluationContext, apella_cloud_api_resource: ApellaClientResource
) -> SensorResult:
    """This sensor is used to add new org ids to the org_partitions partition set, based
    on the orgs that we currently have in the API server
    """

    apella_schema = ApellaCloudApiSchema()
    apella_client = apella_cloud_api_resource.get_client()

    # get org_ids from the API
    org_ids = query_orgs(apella_client, apella_schema)

    current_partition_org_ids = frozenset(
        org_partitions.get_partition_keys(dynamic_partitions_store=context.instance)
    )
    context.log.info(
        f"Found {len(org_ids)} sites in the API. Currently have {len(current_partition_org_ids)} org_id partitions."
    )

    new_partitions = frozenset(org_ids) - current_partition_org_ids

    if new_partitions:
        context.log.info(f"Adding {len(new_partitions)} new org_id partitions: {new_partitions}")

    dynamic_partitions_requests = org_partitions.build_add_request(list(new_partitions))

    return SensorResult(
        dynamic_partitions_requests=[dynamic_partitions_requests],
        skip_reason="This sensor does not create run requests, it only updates the org id partitions",
    )
