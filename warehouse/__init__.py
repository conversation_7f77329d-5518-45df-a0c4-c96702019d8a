from typing import List, Sequence, Union

from dagster import (
    AssetKey,
    AssetsDefinition,
    Backoff,
    ConfigurableResource,
    Definitions,
    RetryPolicy,
    SourceAsset,
    build_init_resource_context,
    build_schedule_from_partitioned_job,
    define_asset_job,
    load_assets_from_package_module,
)
from dagster._core.definitions.cacheable_assets import CacheableAssetsDefinition
from dagster_gcp import BigQueryResource, gcs_resource

from warehouse.assets.block_times import (
    block_changes_sensor_factory,
    block_times,
)
from warehouse.assets.decodable_validation import (
    case_activity_has_all_obx_events,
    scheduled_and_actual_cases_sink_has_all_expected_rows,
    scheduled_and_actual_cases_sink_has_no_unexpected_rows,
)
from warehouse.assets.org_id_partition_sensor import org_id_partition_sensor
from warehouse.assets.sheets import sheets_assets
from warehouse.utils.amplitude_events.amplitude import AmplitudeResource
from warehouse.utils.dbt.helpers import get_dbt_cli
from warehouse.utils.sheets.sheets_resource import SheetsResource
from warehouse.utils.twilio_notifications.twilio import TwilioResource
from warehouse.utils.user_management.auth0 import Auth0Resource

from . import assets
from .assets.amplitude_events import amplitude_events_export_daily
from .assets.available_time_slots import (
    ApellaClientResource,
    available_time_slots_daily,
)
from .assets.datastream_check_factory import DataStreamCheckFactory
from .assets.dbt_models import dbt_data_tests, dbt_latest_models, dbt_models
from .assets.encord_labels import encord_labels_daily
from .assets.historical_cases import historical_cases_assets
from .assets.twilio_notifications_daily import twilio_notifications_daily
from .assets.user_management_assets import (
    auth0_org_to_user_roles,
    auth0_orgs,
    auth0_users,
)
from .configs.warehouse_config import (
    AMPLITUDE_API_KEY,
    AMPLITUDE_SECRET_KEY,
    API_SQL_DB_CONFIG,
    AUTH0_DOMAIN,
    AUTH0_MANAGEMENT_CLIENT_ID_NAME,
    AUTH0_MANAGEMENT_CLIENT_SECRET_NAME,
    ENCORD_API_KEY_NAME,
    ENCORD_PROJECT_HASHES,
    GCP_PROJECT_ID,
    TWILIO_AUTH_TOKEN_NAME,
    TWILIO_SID_NAME,
    WAREHOUSE_ENVIRONMENT,
    WarehouseDatasets,
    get_pickled_io_manager,
)
from .resources.api_db_resource import ApiDbResource
from .resources.secret_manager import SecretManagerResource
from .utils.encord.encord import EncordResource
from .utils.io_managers.date_partitioned_pandas_bigquery_io_manager import (
    DatePartitionedPandasBigQueryIOManager,
)


def get_source_group_name(asset_key: AssetKey) -> Union[str, None]:
    dataset, table_name = asset_key.path
    # could probably make a regex factory to clean this up
    if dataset == WarehouseDatasets.BRONZE.value:
        if table_name.startswith("public_"):
            return "api_postgres"
        elif table_name.startswith("analytics_"):
            return "analytics_postgres"
    elif dataset.endswith("_realtime"):
        return dataset
    return None


def get_source_assets(
    assets: Sequence[Union[AssetsDefinition, SourceAsset, CacheableAssetsDefinition]],
) -> List[SourceAsset]:
    non_source_assets: set[AssetKey] = set()
    for assets_def in assets:
        non_source_assets |= getattr(assets_def, "keys", set())

    source_assets = list(
        {
            key: SourceAsset(key=key, group_name=get_source_group_name(key))
            for asset in assets
            for _, key in getattr(asset, "keys_by_input_name", {}).items()
            if key not in non_source_assets
        }.values()
    )

    return source_assets


warehouse_assets = load_assets_from_package_module(assets)
source_assets = get_source_assets(warehouse_assets)

datastream_check_assets = DataStreamCheckFactory.get_datastream_check_assets(source_assets)

all_assets = list(warehouse_assets) + source_assets + datastream_check_assets


resources: dict[str, ConfigurableResource] = {
    "date_partitioned_pandas_bq_io_manager": DatePartitionedPandasBigQueryIOManager(
        project=GCP_PROJECT_ID
    ),
    "dbt": get_dbt_cli(),
    "amplitude_resource": AmplitudeResource(
        amplitude_api_key=AMPLITUDE_API_KEY,
        amplitude_secret_key=AMPLITUDE_SECRET_KEY,
    ),
    "apella_cloud_api_resource": ApellaClientResource(environment=WAREHOUSE_ENVIRONMENT),
    "auth0_resource": Auth0Resource(
        auth0_client_id_name=AUTH0_MANAGEMENT_CLIENT_ID_NAME,
        auth0_client_secret_name=AUTH0_MANAGEMENT_CLIENT_SECRET_NAME,
        auth0_domain=AUTH0_DOMAIN,
    ),
    "bigquery_resource": BigQueryResource(project=GCP_PROJECT_ID),
    "gcs_resource": gcs_resource(
        build_init_resource_context(
            {
                "project": GCP_PROJECT_ID,
            }
        )
    ),
    "sheets_resource": SheetsResource(),
    "twilio_resource": TwilioResource(
        twilio_auth_token_name=TWILIO_AUTH_TOKEN_NAME,
        twilio_account_sid_name=TWILIO_SID_NAME,
    ),
    "encord_resource": EncordResource(
        encord_api_key_name=ENCORD_API_KEY_NAME,
        encord_project_hashes=ENCORD_PROJECT_HASHES,
    ),
    "pickled_io_manager": get_pickled_io_manager(),
    "api_db_resource": ApiDbResource(
        db_sql_user=API_SQL_DB_CONFIG.user,
        db_sql_password_key=API_SQL_DB_CONFIG.password_key,
        db_sql_database=API_SQL_DB_CONFIG.database,
        db_sql_host=API_SQL_DB_CONFIG.host,
        db_sql_port=API_SQL_DB_CONFIG.port,
        db_gcp_project=API_SQL_DB_CONFIG.gcp_project,
        secret_manager_resource=SecretManagerResource(),
    ),
}

daily_dbt_assets_job = define_asset_job(
    name="daily_dbt_assets",
    selection=historical_cases_assets
    + dbt_models
    + dbt_data_tests
    + dbt_latest_models
    + sheets_assets,
    config={
        "execution": {
            "config": {
                "multiprocess": {
                    "max_concurrent": 5,
                },
            }
        }
    },
    tags={
        "dagster-k8s/config": {
            "container_config": {
                "resources": {
                    "limits": {"cpu": "4000m", "memory": "5120Mi"},
                    "requests": {
                        "cpu": "2000m",
                        "memory": "2048Mi",
                    },
                }
            },
        },
        "dagster/max_retries": 3,
    },
)

daily_datastream_check_job = define_asset_job(
    name="datastream_check_assets",
    selection=datastream_check_assets,
    config={
        "execution": {
            "config": {
                "multiprocess": {
                    "max_concurrent": 5,
                },
            }
        }
    },
    tags={
        "dagster-k8s/config": {
            "container_config": {
                "resources": {
                    "limits": {"cpu": "2000m", "memory": "2048Mi"},
                    "requests": {
                        "cpu": "1000m",
                        "memory": "2048Mi",
                    },
                }
            },
        },
    },
    # formula is ((2 ^ attempt_num) - 1) * delay, so this goes 60, 180, 420, 900, 1860
    # adding these up, total job delay is 57 minutes, so we retry until the next job starts
    op_retry_policy=RetryPolicy(
        max_retries=5,
        delay=60,
        backoff=Backoff.EXPONENTIAL,
    ),
)

process_block_times = define_asset_job("process_block_times", selection=[block_times])

block_schedule_block_releases_updates_sensor = block_changes_sensor_factory(
    job=process_block_times, sensor_name="block_schedule_block_releases_updates_sensor"
)


defs = Definitions(
    assets=all_assets,
    resources=resources,
    jobs=[process_block_times],
    sensors=[org_id_partition_sensor, block_schedule_block_releases_updates_sensor],
    schedules=[
        build_schedule_from_partitioned_job(
            name="daily_user_management_assets",
            job=define_asset_job(
                name="daily_user_management_assets",
                selection=[
                    auth0_users,
                    auth0_org_to_user_roles,
                    auth0_orgs,
                ],
            ),
            tags={
                "dagster/max_retries": 3,  # type: ignore
            },
        ),
        build_schedule_from_partitioned_job(
            name="daily_available_time_slots",
            job=define_asset_job(
                name="daily_available_time_slots",
                selection=[available_time_slots_daily],
            ),
            tags={
                "dagster/max_retries": 3,  # type: ignore
            },
        ),
        build_schedule_from_partitioned_job(
            name="daily_dbt_assets",
            job=daily_dbt_assets_job,
        ),
        build_schedule_from_partitioned_job(
            name="daily_twilio_notifications",
            job=define_asset_job(
                name="daily_twilio_notifications",
                selection=[twilio_notifications_daily],
            ),
            tags={
                "dagster/max_retries": 3,  # type: ignore
            },
        ),
        build_schedule_from_partitioned_job(job=daily_datastream_check_job),
        build_schedule_from_partitioned_job(
            name="daily_amplitude_events",
            job=define_asset_job(
                name="daily_amplitude_events",
                selection=[amplitude_events_export_daily],
            ),
            tags={
                "dagster-k8s/config": {  # type: ignore
                    "container_config": {
                        "resources": {
                            "limits": {"cpu": "1000m", "memory": "4096Mi"},
                            "requests": {"cpu": "500m", "memory": "2048Mi"},
                        }
                    },
                },
                "dagster/max_retries": 3,  # type: ignore
            },
        ),
        build_schedule_from_partitioned_job(
            name="daily_encord_labels",
            job=define_asset_job(
                name="daily_encord_labels",
                selection=[encord_labels_daily],
                # formula is ((2 ^ attempt_num) - 1) * delay, so this goes 60, 180, 420, 900, 1860
                # adding these up, total job delay is 57 minutes
                op_retry_policy=RetryPolicy(
                    max_retries=5,
                    delay=60,
                    backoff=Backoff.EXPONENTIAL,
                ),
            ),
            # Encord export will run at 10pm to ensure the previous day's data is available for the dbt assets
            hour_of_day=22,
            tags={
                "dagster-k8s/config": {  # type: ignore
                    "container_config": {
                        "resources": {
                            "limits": {"cpu": "1000m", "memory": "11Gi"},
                            "requests": {"cpu": "500m", "memory": "10Gi"},
                        }
                    },
                },
                "dagster/max_retries": 3,  # type: ignore
            },
        ),
        build_schedule_from_partitioned_job(
            name="decodable_validation",
            job=define_asset_job(
                name="decodable_validation",
                selection=[
                    case_activity_has_all_obx_events,
                    scheduled_and_actual_cases_sink_has_all_expected_rows,
                    scheduled_and_actual_cases_sink_has_no_unexpected_rows,
                ],
                tags={
                    "dagster/max_retries": 3,  # type: ignore
                },
            ),
        ),
    ],
)
